#!/usr/bin/env python3
"""
基于连胜连败的策略选择器
利用移动窗口胜率、连胜、连败等指标进行智能策略选择
"""

import sys
import os
import logging
import warnings
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class StreakBasedSelector:
    """基于连胜连败的策略选择器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_streak_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算连胜连败特征"""
        self.logger.info("🔥 计算连胜连败特征...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        for strategy in strategy_cols:
            # 计算每个策略的正确性
            df[f'{strategy}_correct'] = (df[strategy] == df['actual_result']).astype(int)
            
            # 计算连胜连败
            df[f'{strategy}_streak'] = self.calculate_streak(df[f'{strategy}_correct'])
            
            # 计算当前连胜/连败长度
            df[f'{strategy}_current_streak'] = self.calculate_current_streak(df[f'{strategy}_correct'])
            
            # 计算最近N次的胜率
            for window in [3, 5, 10, 20]:
                df[f'{strategy}_winrate_{window}'] = df[f'{strategy}_correct'].rolling(
                    window=window, min_periods=1
                ).mean()
            
            # 计算连胜连败统计
            df[f'{strategy}_max_win_streak'] = df[f'{strategy}_correct'].rolling(
                window=20, min_periods=1
            ).apply(lambda x: self.get_max_win_streak(x))
            
            df[f'{strategy}_max_loss_streak'] = df[f'{strategy}_correct'].rolling(
                window=20, min_periods=1
            ).apply(lambda x: self.get_max_loss_streak(x))
        
        return df
    
    def calculate_streak(self, correct_series: pd.Series) -> pd.Series:
        """计算连胜连败序列"""
        streaks = []
        current_streak = 0
        
        for correct in correct_series:
            if correct == 1:
                if current_streak >= 0:
                    current_streak += 1
                else:
                    current_streak = 1
            else:
                if current_streak <= 0:
                    current_streak -= 1
                else:
                    current_streak = -1
            
            streaks.append(current_streak)
        
        return pd.Series(streaks, index=correct_series.index)
    
    def calculate_current_streak(self, correct_series: pd.Series) -> pd.Series:
        """计算当前连胜连败长度"""
        current_streaks = []
        current_streak = 0
        last_result = None
        
        for correct in correct_series:
            if last_result is None:
                current_streak = 1 if correct else -1
            elif correct == last_result:
                if correct:
                    current_streak += 1
                else:
                    current_streak -= 1
            else:
                current_streak = 1 if correct else -1
            
            current_streaks.append(abs(current_streak))
            last_result = correct
        
        return pd.Series(current_streaks, index=correct_series.index)
    
    def get_max_win_streak(self, window_data):
        """获取窗口内最大连胜"""
        if len(window_data) == 0:
            return 0
        
        max_streak = 0
        current_streak = 0
        
        for val in window_data:
            if val == 1:
                current_streak += 1
                max_streak = max(max_streak, current_streak)
            else:
                current_streak = 0
        
        return max_streak
    
    def get_max_loss_streak(self, window_data):
        """获取窗口内最大连败"""
        if len(window_data) == 0:
            return 0
        
        max_streak = 0
        current_streak = 0
        
        for val in window_data:
            if val == 0:
                current_streak += 1
                max_streak = max(max_streak, current_streak)
            else:
                current_streak = 0
        
        return max_streak
    
    def create_streak_based_selector(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建基于连胜连败的选择器"""
        self.logger.info("🎯 创建基于连胜连败的选择器...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        selected_strategies = []
        selection_reasons = []
        selection_scores = []
        
        for idx, row in df.iterrows():
            if idx % 5000 == 0:
                self.logger.info(f"   处理进度: {idx}/{len(df)}")
            
            strategy_scores = {}
            
            for strategy in strategy_cols:
                score = 0
                
                # 1. 基础胜率权重 (40%)
                winrate_5 = row.get(f'{strategy}_winrate_5', 0.5)
                winrate_10 = row.get(f'{strategy}_winrate_10', 0.5)
                winrate_20 = row.get(f'{strategy}_winrate_20', 0.5)
                
                # 加权平均胜率 (近期权重更高)
                avg_winrate = (winrate_5 * 0.5 + winrate_10 * 0.3 + winrate_20 * 0.2)
                score += avg_winrate * 0.4
                
                # 2. 连胜奖励 (30%)
                current_streak = row.get(f'{strategy}_streak', 0)
                if current_streak > 0:  # 连胜中
                    streak_bonus = min(current_streak * 0.05, 0.3)  # 最多30%奖励
                    score += streak_bonus * 0.3
                elif current_streak < -2:  # 连败超过2次，给予反弹机会
                    rebound_bonus = min(abs(current_streak) * 0.02, 0.1)
                    score += rebound_bonus * 0.3
                
                # 3. 连败惩罚 (20%)
                if current_streak < 0:
                    loss_penalty = min(abs(current_streak) * 0.1, 0.5)
                    score -= loss_penalty * 0.2
                
                # 4. 历史最大连胜奖励 (10%)
                max_win_streak = row.get(f'{strategy}_max_win_streak', 0)
                if max_win_streak > 3:
                    history_bonus = min((max_win_streak - 3) * 0.02, 0.1)
                    score += history_bonus * 0.1
                
                # 确保分数在合理范围内
                score = max(0, min(1, score))
                strategy_scores[strategy] = score
            
            # 选择得分最高的策略
            best_strategy = max(strategy_scores, key=strategy_scores.get)
            best_score = strategy_scores[best_strategy]
            
            # 生成选择原因
            reason = self.generate_selection_reason(row, best_strategy)
            
            selected_strategies.append(best_strategy)
            selection_scores.append(best_score)
            selection_reasons.append(reason)
        
        df['selected_strategy'] = selected_strategies
        df['selection_score'] = selection_scores
        df['selection_reason'] = selection_reasons
        
        return df
    
    def generate_selection_reason(self, row: pd.Series, strategy: str) -> str:
        """生成选择原因"""
        winrate_5 = row.get(f'{strategy}_winrate_5', 0.5)
        current_streak = row.get(f'{strategy}_streak', 0)
        
        if current_streak > 2:
            return f"连胜{current_streak}次,胜率{winrate_5:.1%}"
        elif current_streak < -2:
            return f"连败{abs(current_streak)}次,反弹机会"
        elif winrate_5 > 0.7:
            return f"近期胜率高{winrate_5:.1%}"
        elif winrate_5 > 0.6:
            return f"胜率稳定{winrate_5:.1%}"
        else:
            return f"综合评分最高"
    
    def evaluate_streak_selector(self, df: pd.DataFrame) -> Dict[str, Any]:
        """评估连胜连败选择器"""
        self.logger.info("📊 评估连胜连败选择器...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 计算选择器准确率
        correct_predictions = 0
        total_predictions = len(df)
        
        strategy_usage = {strategy: 0 for strategy in strategy_cols}
        reason_stats = {}
        
        for _, row in df.iterrows():
            selected_strategy = row['selected_strategy']
            prediction = row[selected_strategy]
            actual = row['actual_result']
            reason = row['selection_reason']
            
            if prediction == actual:
                correct_predictions += 1
            
            strategy_usage[selected_strategy] += 1
            
            # 统计选择原因
            reason_key = reason.split(',')[0] if ',' in reason else reason
            if reason_key not in reason_stats:
                reason_stats[reason_key] = {'total': 0, 'correct': 0}
            reason_stats[reason_key]['total'] += 1
            if prediction == actual:
                reason_stats[reason_key]['correct'] += 1
        
        streak_accuracy = correct_predictions / total_predictions
        
        # 对比基准
        baseline_accuracy = (df['strategy_1'] == df['actual_result']).mean()
        improvement = streak_accuracy - baseline_accuracy
        
        # 计算理论上限
        perfect_selections = 0
        for _, row in df.iterrows():
            for strategy in strategy_cols:
                if row[strategy] == row['actual_result']:
                    perfect_selections += 1
                    break
        
        perfect_accuracy = perfect_selections / len(df)
        
        self.logger.info(f"   📊 连胜连败选择器准确率: {streak_accuracy:.3f}")
        self.logger.info(f"   📊 基准准确率: {baseline_accuracy:.3f}")
        self.logger.info(f"   📊 提升幅度: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        self.logger.info(f"   📊 理论上限: {perfect_accuracy:.3f}")
        
        if perfect_accuracy > baseline_accuracy:
            realization_ratio = (streak_accuracy - baseline_accuracy) / (perfect_accuracy - baseline_accuracy)
            self.logger.info(f"   📊 潜力实现: {realization_ratio*100:.1f}%")
        else:
            realization_ratio = 0
        
        self.logger.info(f"\n   🎯 策略使用分布:")
        for strategy, count in strategy_usage.items():
            percentage = count / total_predictions * 100
            self.logger.info(f"     {strategy}: {count} ({percentage:.1f}%)")
        
        self.logger.info(f"\n   🎯 选择原因效果:")
        for reason, stats in reason_stats.items():
            if stats['total'] > 10:  # 只显示样本数足够的原因
                accuracy = stats['correct'] / stats['total']
                self.logger.info(f"     {reason}: {accuracy:.3f} ({stats['total']}次)")
        
        return {
            'streak_accuracy': streak_accuracy,
            'baseline_accuracy': baseline_accuracy,
            'improvement': improvement,
            'perfect_accuracy': perfect_accuracy,
            'realization_ratio': realization_ratio,
            'strategy_usage': strategy_usage,
            'reason_stats': reason_stats
        }
    
    def analyze_streak_patterns(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析连胜连败模式"""
        self.logger.info("🔍 分析连胜连败模式...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 分析各策略的连胜连败统计
        streak_analysis = {}
        
        for strategy in strategy_cols:
            correct_col = f'{strategy}_correct'
            streak_col = f'{strategy}_streak'
            
            if correct_col in df.columns:
                # 基础统计
                total_correct = df[correct_col].sum()
                total_samples = len(df)
                accuracy = total_correct / total_samples
                
                # 连胜统计
                win_streaks = df[df[streak_col] > 0][streak_col].tolist()
                loss_streaks = df[df[streak_col] < 0][streak_col].abs().tolist()
                
                streak_analysis[strategy] = {
                    'accuracy': accuracy,
                    'max_win_streak': max(win_streaks) if win_streaks else 0,
                    'max_loss_streak': max(loss_streaks) if loss_streaks else 0,
                    'avg_win_streak': np.mean(win_streaks) if win_streaks else 0,
                    'avg_loss_streak': np.mean(loss_streaks) if loss_streaks else 0,
                    'win_streak_count': len([s for s in win_streaks if s >= 3]),
                    'loss_streak_count': len([s for s in loss_streaks if s >= 3])
                }
        
        self.logger.info(f"   📈 各策略连胜连败分析:")
        for strategy, analysis in streak_analysis.items():
            self.logger.info(f"     {strategy}: 准确率{analysis['accuracy']:.3f}, "
                           f"最大连胜{analysis['max_win_streak']}, "
                           f"最大连败{analysis['max_loss_streak']}, "
                           f"3+连胜{analysis['win_streak_count']}次")
        
        return streak_analysis
    
    def run_optimization(self):
        """运行优化"""
        self.logger.info("🚀 开始基于连胜连败的策略选择优化")
        self.logger.info("🎯 目标：利用胜率、连胜、连败等指标实现智能策略选择")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，优化终止")
            return
        
        # 2. 计算连胜连败特征
        df = self.calculate_streak_features(df)
        
        # 3. 创建选择器
        df = self.create_streak_based_selector(df)
        
        # 4. 评估选择器
        evaluation_result = self.evaluate_streak_selector(df)
        
        # 5. 分析连胜连败模式
        streak_analysis = self.analyze_streak_patterns(df)
        
        # 6. 生成最终报告
        total_time = time.time() - start_time
        self.generate_final_report(evaluation_result, streak_analysis, total_time)
    
    def generate_final_report(self, evaluation_result: Dict[str, Any], 
                            streak_analysis: Dict[str, Any], total_time: float):
        """生成最终报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 基于连胜连败的策略选择最终报告")
        self.logger.info("="*80)
        
        streak_accuracy = evaluation_result.get('streak_accuracy', 0)
        baseline_accuracy = evaluation_result.get('baseline_accuracy', 0.568)
        improvement = evaluation_result.get('improvement', 0)
        perfect_accuracy = evaluation_result.get('perfect_accuracy', 0.985)
        realization_ratio = evaluation_result.get('realization_ratio', 0)
        
        self.logger.info(f"\n📊 最终性能:")
        self.logger.info(f"   - 基准准确率 (strategy_1): {baseline_accuracy:.3f}")
        self.logger.info(f"   - 连胜连败选择器准确率: {streak_accuracy:.3f}")
        self.logger.info(f"   - 理论上限: {perfect_accuracy:.3f}")
        self.logger.info(f"   - 实际提升: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        self.logger.info(f"   - 潜力实现: {realization_ratio*100:.1f}%")
        
        target_accuracy = 0.60
        if streak_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 成功达到目标准确率 {target_accuracy:.1%}!")
            self.logger.info("✅ 连胜连败策略选择系统已准备好投入生产！")
        else:
            gap = target_accuracy - streak_accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f}")
            
            if improvement > 0.03:  # 提升超过3%
                self.logger.info("🎉 连胜连败选择器效果显著！")
                self.logger.info("✅ 证明了您的思路完全正确！")
            elif improvement > 0.01:  # 提升超过1%
                self.logger.info("✅ 连胜连败选择器有明显效果")
                self.logger.info("💡 方向正确，可以进一步优化")
            elif improvement > 0.005:  # 提升超过0.5%
                self.logger.info("✅ 连胜连败选择器有一定效果")
                self.logger.info("💡 需要调整参数或算法")
            else:
                self.logger.info("⚠️ 连胜连败选择器效果有限")
                self.logger.info("💡 可能需要结合其他指标")
        
        # 关键结论
        self.logger.info(f"\n💡 关键结论:")
        if improvement > 0.02:
            self.logger.info("   🎉 您的思路完全正确！")
            self.logger.info("   ✅ 移动窗口胜率 + 连胜连败是有效的选择策略")
            self.logger.info("   ✅ 98.5%的理论上限确实可以部分实现")
            self.logger.info("   ✅ 问题确实在于选择器，而不是策略本身")
        elif improvement > 0.005:
            self.logger.info("   ✅ 连胜连败方法有一定效果")
            self.logger.info("   💡 证明了策略选择的价值")
            self.logger.info("   🔧 需要进一步优化算法")
        else:
            self.logger.info("   ⚠️ 当前算法仍不足以有效利用策略多样性")
            self.logger.info("   💡 可能需要更复杂的机器学习方法")
        
        self.logger.info(f"\n⏱️ 总优化时间: {total_time:.1f}秒")
        self.logger.info("="*80)

if __name__ == "__main__":
    optimizer = StreakBasedSelector()
    optimizer.run_optimization()
