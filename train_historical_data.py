#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V8系统历史数据训练

使用真实历史数据训练V8系统的所有机器学习模型
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import pandas as pd
import logging
import time
import yaml
import warnings
from typing import Dict, List, Any, Tuple
from pathlib import Path

# 过滤NumPy警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered in divide')
warnings.filterwarnings('ignore', message='invalid value encountered in scalar divide')

try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

from main import SimpleFusionV8

# 配置日志 - 训练期间减少详细输出
logging.basicConfig(
    level=logging.WARNING,  # 只显示警告和错误
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 为训练器设置INFO级别
trainer_logger = logging.getLogger('__main__')
trainer_logger.setLevel(logging.INFO)

class HistoricalDataTrainer:
    """历史数据训练器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_config = self.load_database_config()
        self.system = None
        
        # 训练统计
        self.training_stats = {
            'total_records': 0,
            'processed_records': 0,
            'training_records': 0,
            'validation_records': 0,
            'start_time': None,
            'end_time': None,
            'accuracy_history': [],
            'loss_history': []
        }
    
    def load_database_config(self) -> Dict[str, Any]:
        """加载数据库配置"""
        return {
            'host': '**************',
            'user': 'root',
            'password': '216888',
            'database': 'lushu',
            'charset': 'utf8mb4',
            'table': 'strategy_results'
        }
    
    def load_historical_data(self, limit: int = None, offset: int = 0) -> pd.DataFrame:
        """加载历史数据"""
        try:
            conn = pymysql.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset=self.db_config.get('charset', 'utf8mb4')
            )
            
            table = self.db_config['table']
            query = f"""
            SELECT 
                id,
                strategy_1,
                strategy_2, 
                strategy_6,
                true_label as actual_result,
                boot_id
            FROM {table}
            WHERE strategy_1 IS NOT NULL 
                AND strategy_2 IS NOT NULL 
                AND strategy_6 IS NOT NULL
                AND true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            if limit:
                query += f" LIMIT {limit}"
            if offset:
                query += f" OFFSET {offset}"
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            self.logger.info(f"✅ 加载了 {len(df)} 条历史数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 数据加载失败: {str(e)}")
            return pd.DataFrame()
    
    def get_data_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        try:
            conn = pymysql.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset=self.db_config.get('charset', 'utf8mb4')
            )
            
            table = self.db_config['table']
            
            # 总记录数
            total_query = f"SELECT COUNT(*) as total FROM {table}"
            total_result = pd.read_sql_query(total_query, conn)
            total_records = total_result['total'].iloc[0]
            
            # 有效记录数
            valid_query = f"""
            SELECT COUNT(*) as valid FROM {table}
            WHERE strategy_1 IS NOT NULL 
                AND strategy_2 IS NOT NULL 
                AND strategy_6 IS NOT NULL
                AND true_label IS NOT NULL
            """
            valid_result = pd.read_sql_query(valid_query, conn)
            valid_records = valid_result['valid'].iloc[0]
            
            # 靴统计
            boot_query = f"""
            SELECT 
                MIN(boot_id) as min_boot,
                MAX(boot_id) as max_boot,
                COUNT(DISTINCT boot_id) as total_boots
            FROM {table}
            WHERE strategy_1 IS NOT NULL 
                AND strategy_2 IS NOT NULL 
                AND strategy_6 IS NOT NULL
            """
            boot_result = pd.read_sql_query(boot_query, conn)
            
            # 策略分布
            strategy_query = f"""
            SELECT 
                AVG(strategy_1) as avg_s1,
                AVG(strategy_2) as avg_s2,
                AVG(strategy_6) as avg_s6,
                AVG(true_label) as avg_actual,
                SUM(CASE WHEN strategy_1 = 1 THEN 1 ELSE 0 END) as s1_count,
                SUM(CASE WHEN strategy_2 = 1 THEN 1 ELSE 0 END) as s2_count,
                SUM(CASE WHEN strategy_6 = 1 THEN 1 ELSE 0 END) as s6_count,
                SUM(CASE WHEN true_label = 1 THEN 1 ELSE 0 END) as positive_count
            FROM {table}
            WHERE strategy_1 IS NOT NULL 
                AND strategy_2 IS NOT NULL 
                AND strategy_6 IS NOT NULL
                AND true_label IS NOT NULL
            """
            strategy_result = pd.read_sql_query(strategy_query, conn)
            
            conn.close()
            
            return {
                'total_records': total_records,
                'valid_records': valid_records,
                'data_quality': valid_records / total_records if total_records > 0 else 0,
                'boot_range': {
                    'min_boot': boot_result['min_boot'].iloc[0],
                    'max_boot': boot_result['max_boot'].iloc[0],
                    'total_boots': boot_result['total_boots'].iloc[0]
                },
                'strategy_distribution': {
                    'strategy_1': {
                        'avg': strategy_result['avg_s1'].iloc[0],
                        'count': strategy_result['s1_count'].iloc[0],
                        'rate': strategy_result['s1_count'].iloc[0] / valid_records
                    },
                    'strategy_2': {
                        'avg': strategy_result['avg_s2'].iloc[0],
                        'count': strategy_result['s2_count'].iloc[0],
                        'rate': strategy_result['s2_count'].iloc[0] / valid_records
                    },
                    'strategy_6': {
                        'avg': strategy_result['avg_s6'].iloc[0],
                        'count': strategy_result['s6_count'].iloc[0],
                        'rate': strategy_result['s6_count'].iloc[0] / valid_records
                    },
                    'actual_result': {
                        'avg': strategy_result['avg_actual'].iloc[0],
                        'positive_count': strategy_result['positive_count'].iloc[0],
                        'positive_rate': strategy_result['positive_count'].iloc[0] / valid_records
                    }
                }
            }
            
        except Exception as e:
            self.logger.error(f"统计信息获取失败: {str(e)}")
            return {}
    
    def prepare_training_data(self, df: pd.DataFrame, train_ratio: float = 0.8) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """准备训练数据"""
        # 按靴分割，确保同一靴的数据在同一个集合中
        unique_boots = df['boot_id'].unique()
        np.random.shuffle(unique_boots)
        
        train_boot_count = int(len(unique_boots) * train_ratio)
        train_boots = unique_boots[:train_boot_count]
        val_boots = unique_boots[train_boot_count:]
        
        train_df = df[df['boot_id'].isin(train_boots)].copy()
        val_df = df[df['boot_id'].isin(val_boots)].copy()
        
        self.logger.info(f"📊 训练数据分割:")
        self.logger.info(f"   - 训练靴数: {len(train_boots)} ({len(train_df)} 条记录)")
        self.logger.info(f"   - 验证靴数: {len(val_boots)} ({len(val_df)} 条记录)")
        
        return train_df, val_df
    
    def create_external_data(self, row: pd.Series) -> Dict[str, Any]:
        """创建外部数据格式"""
        return {
            'decision_id': f"train_decision_{row['id']}",
            'boot_id': row.get('boot_id', 1),
            'market_data': {
                'historical_strategies': {
                    'strategy_1': int(row['strategy_1']),
                    'strategy_2': int(row['strategy_2']),
                    'strategy_6': int(row['strategy_6'])
                }
            }
        }
    
    def train_batch(self, batch_df: pd.DataFrame, batch_id: int) -> Dict[str, Any]:
        """训练一个批次"""
        batch_stats = {
            'batch_id': batch_id,
            'size': len(batch_df),
            'correct_predictions': 0,
            'total_predictions': 0,
            'processing_time': 0,
            'features_generated': 0
        }
        
        start_time = time.time()
        
        # 显示批次开始
        print(f"   🔄 处理批次 {batch_id} ({len(batch_df)} 条记录)...", end="", flush=True)

        for idx, row in batch_df.iterrows():
            try:
                # 创建外部数据
                external_data = self.create_external_data(row)

                # 处理决策（这会生成特征和进行预测）
                decision = self.system.process_decision(external_data)

                # 提供真实标签进行训练
                actual_result = int(row['actual_result'])
                decision_id = external_data['decision_id']

                # 更新反馈（这会触发模型训练）
                self.system.update_feedback(decision_id, actual_result)

                # 统计
                batch_stats['total_predictions'] += 1
                if int(decision.prediction) == actual_result:
                    batch_stats['correct_predictions'] += 1

                # 记录特征数量
                if hasattr(decision, 'features') and decision.features:
                    batch_stats['features_generated'] = len(decision.features)

                # 显示进度点
                if batch_stats['total_predictions'] % 50 == 0:
                    print(".", end="", flush=True)

            except Exception as e:
                self.logger.warning(f"批次 {batch_id} 中记录 {idx} 处理失败: {str(e)}")
                continue

        print(" ✅", flush=True)  # 批次完成标记
        
        batch_stats['processing_time'] = time.time() - start_time
        batch_stats['accuracy'] = (batch_stats['correct_predictions'] / 
                                 batch_stats['total_predictions'] 
                                 if batch_stats['total_predictions'] > 0 else 0)
        
        return batch_stats
    
    def validate_model(self, val_df: pd.DataFrame) -> Dict[str, Any]:
        """验证模型性能"""
        val_stats = {
            'total_predictions': 0,
            'correct_predictions': 0,
            'strategy_consistency': {},
            'confidence_stats': [],
            'processing_time': 0
        }
        
        start_time = time.time()
        predictions = []
        actuals = []
        confidences = []
        strategy_outputs = []
        
        self.logger.info("🔍 开始模型验证...")
        
        for idx, row in val_df.iterrows():
            try:
                external_data = self.create_external_data(row)
                decision = self.system.process_decision(external_data)
                
                pred = int(decision.prediction)
                actual = int(row['actual_result'])
                
                predictions.append(pred)
                actuals.append(actual)
                confidences.append(float(decision.confidence))
                
                # 记录策略输出
                strategies = {
                    'strategy_1': int(row['strategy_1']),
                    'strategy_2': int(row['strategy_2']),
                    'strategy_6': int(row['strategy_6'])
                }
                strategy_outputs.append(strategies)
                
                val_stats['total_predictions'] += 1
                if pred == actual:
                    val_stats['correct_predictions'] += 1
                    
            except Exception as e:
                self.logger.warning(f"验证记录 {idx} 处理失败: {str(e)}")
                continue
        
        # 计算统计指标
        val_stats['accuracy'] = (val_stats['correct_predictions'] / 
                                val_stats['total_predictions'] 
                                if val_stats['total_predictions'] > 0 else 0)
        
        val_stats['avg_confidence'] = np.mean(confidences) if confidences else 0
        val_stats['confidence_std'] = np.std(confidences) if confidences else 0
        
        # 计算策略一致性
        for strategy_name in ['strategy_1', 'strategy_2', 'strategy_6']:
            matches = sum(1 for i, pred in enumerate(predictions) 
                         if strategy_outputs[i][strategy_name] == pred)
            val_stats['strategy_consistency'][strategy_name] = (
                matches / len(predictions) if len(predictions) > 0 else 0
            )
        
        val_stats['processing_time'] = time.time() - start_time
        
        return val_stats

    def train_full_dataset(self, batch_size: int = 1000, max_records: int = None):
        """训练完整数据集"""
        print("🚀 开始V8系统历史数据训练")
        print("="*80)

        # 获取数据统计
        print("📊 获取数据统计信息...")
        stats = self.get_data_statistics()

        if not stats:
            print("❌ 无法获取数据统计信息")
            return

        print(f"📈 数据库统计:")
        print(f"   - 总记录数: {stats['total_records']:,}")
        print(f"   - 有效记录数: {stats['valid_records']:,}")
        print(f"   - 数据质量: {stats['data_quality']:.2%}")
        print(f"   - 靴范围: {stats['boot_range']['min_boot']} 到 {stats['boot_range']['max_boot']} (共{stats['boot_range']['total_boots']}靴)")

        print(f"📊 策略分布:")
        for strategy, dist in stats['strategy_distribution'].items():
            if strategy != 'actual_result':
                print(f"   - {strategy}: {dist['rate']:.2%} ({dist['count']:,}/{stats['valid_records']:,})")
            else:
                print(f"   - 正例比例: {dist['positive_rate']:.2%} ({dist['positive_count']:,}/{stats['valid_records']:,})")

        # 确定训练数据量
        total_records = min(stats['valid_records'], max_records) if max_records else stats['valid_records']
        self.training_stats['total_records'] = total_records

        print(f"\n🎯 训练配置:")
        print(f"   - 训练记录数: {total_records:,}")
        print(f"   - 批次大小: {batch_size:,}")
        print(f"   - 预计批次数: {(total_records + batch_size - 1) // batch_size}")

        # 初始化系统
        print(f"\n🔧 初始化V8系统...")
        self.system = SimpleFusionV8()
        self.system.initialize()

        # 训练期间放宽风险管理限制
        if hasattr(self.system, 'risk_manager'):
            # 降低风险阈值，允许更多决策通过
            self.system.risk_manager.config['risk_thresholds']['high_risk'] = 0.9  # 从0.7提高到0.9
            self.system.risk_manager.config['risk_thresholds']['medium_risk'] = 0.8  # 从0.5提高到0.8
            print("   📉 已放宽训练期间的风险管理限制")

        # 开始训练
        self.training_stats['start_time'] = time.time()

        # 分批加载和训练
        offset = 0
        batch_id = 1
        total_processed = 0

        while offset < total_records:
            current_batch_size = min(batch_size, total_records - offset)

            print(f"\n📦 批次 {batch_id}/{(total_records + batch_size - 1) // batch_size}: 偏移 {offset:,}")

            # 加载批次数据
            batch_df = self.load_historical_data(limit=current_batch_size, offset=offset)

            if batch_df.empty:
                print(f"   ⚠️ 批次 {batch_id} 数据为空，跳过")
                break

            # 分割训练和验证数据
            train_df, val_df = self.prepare_training_data(batch_df, train_ratio=0.8)

            # 训练批次
            print(f"   🏋️ 训练: {len(train_df)} 样本")
            batch_stats = self.train_batch(train_df, batch_id)

            # 验证批次
            if len(val_df) > 0:
                print(f"   🔍 验证: {len(val_df)} 样本")
                val_stats = self.validate_model(val_df)
            else:
                val_stats = {'accuracy': 0, 'avg_confidence': 0}

            # 记录统计
            self.training_stats['accuracy_history'].append({
                'batch_id': batch_id,
                'train_accuracy': batch_stats['accuracy'],
                'val_accuracy': val_stats['accuracy'],
                'train_size': len(train_df),
                'val_size': len(val_df)
            })

            # 显示批次结果（简化版）
            print(f"   📊 结果: 训练={batch_stats['accuracy']:.3f}, "
                  f"验证={val_stats['accuracy']:.3f}, "
                  f"时间={batch_stats['processing_time']:.1f}s, "
                  f"特征={batch_stats['features_generated']}")

            # 只在特定批次显示详细策略一致性
            if batch_id % 5 == 0 and 'strategy_consistency' in val_stats:
                print(f"   🎯 策略一致性: S1={val_stats['strategy_consistency'].get('strategy_1', 0):.2f}, "
                      f"S2={val_stats['strategy_consistency'].get('strategy_2', 0):.2f}, "
                      f"S6={val_stats['strategy_consistency'].get('strategy_6', 0):.2f}")

            total_processed += len(batch_df)
            offset += current_batch_size
            batch_id += 1

            # 进度报告
            progress = total_processed / total_records
            print(f"   📈 总进度: {progress:.1%} ({total_processed:,}/{total_records:,})")

        # 训练完成
        self.training_stats['end_time'] = time.time()
        self.training_stats['processed_records'] = total_processed

        # 最终验证
        self.logger.info(f"\n🎯 最终模型验证...")
        final_val_df = self.load_historical_data(limit=2000, offset=total_records-2000 if total_records > 2000 else 0)
        if not final_val_df.empty:
            final_stats = self.validate_model(final_val_df)
            self.logger.info(f"🏆 最终验证结果:")
            self.logger.info(f"   - 准确率: {final_stats['accuracy']:.3f}")
            self.logger.info(f"   - 平均置信度: {final_stats['avg_confidence']:.3f}")
            self.logger.info(f"   - 策略一致性:")
            for strategy, consistency in final_stats['strategy_consistency'].items():
                self.logger.info(f"     * {strategy}: {consistency:.3f}")

        # 生成训练报告
        self.generate_training_report()

    def generate_training_report(self):
        """生成训练报告"""
        total_time = self.training_stats['end_time'] - self.training_stats['start_time']

        report_lines = []
        report_lines.append("="*80)
        report_lines.append("V8系统历史数据训练报告")
        report_lines.append("="*80)

        report_lines.append(f"\n📊 训练统计:")
        report_lines.append(f"   - 总记录数: {self.training_stats['total_records']:,}")
        report_lines.append(f"   - 处理记录数: {self.training_stats['processed_records']:,}")
        report_lines.append(f"   - 训练时间: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
        report_lines.append(f"   - 处理速度: {self.training_stats['processed_records']/total_time:.1f} 记录/秒")

        if self.training_stats['accuracy_history']:
            report_lines.append(f"\n📈 训练历史:")
            for record in self.training_stats['accuracy_history']:
                report_lines.append(f"   批次 {record['batch_id']}: "
                                  f"训练={record['train_accuracy']:.3f}, "
                                  f"验证={record['val_accuracy']:.3f}, "
                                  f"训练样本={record['train_size']}, "
                                  f"验证样本={record['val_size']}")

            # 计算平均性能
            avg_train_acc = np.mean([r['train_accuracy'] for r in self.training_stats['accuracy_history']])
            avg_val_acc = np.mean([r['val_accuracy'] for r in self.training_stats['accuracy_history']])

            report_lines.append(f"\n🎯 平均性能:")
            report_lines.append(f"   - 平均训练准确率: {avg_train_acc:.3f}")
            report_lines.append(f"   - 平均验证准确率: {avg_val_acc:.3f}")

        # 获取系统状态
        if self.system:
            system_status = self.system.get_system_status()
            report_lines.append(f"\n🔧 系统状态:")
            report_lines.append(f"   - 决策计数: {system_status.get('decision_count', 0)}")
            report_lines.append(f"   - 系统初始化: {system_status.get('is_initialized', False)}")

            if 'ml_fusion' in system_status:
                ml_status = system_status['ml_fusion']
                report_lines.append(f"   - ML模型状态: {ml_status.get('models_trained', 0)} 个模型已训练")

        report_content = "\n".join(report_lines)

        # 保存报告
        report_path = "V8_training_report.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        self.logger.info(f"\n💾 训练报告已保存到: {report_path}")

        # 显示报告
        print(report_content)


def main():
    """主训练函数"""
    trainer = HistoricalDataTrainer()

    try:
        # 开始训练
        # 可以调整参数：
        # - batch_size: 批次大小，默认1000
        # - max_records: 最大训练记录数，None表示使用全部数据

        # 大规模模型训练 - 目标准确率60%+
        trainer.train_full_dataset(batch_size=500, max_records=10000)

    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
