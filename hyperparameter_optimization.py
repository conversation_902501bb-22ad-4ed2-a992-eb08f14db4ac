#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V8系统超参数优化

使用网格搜索和贝叶斯优化对ML模型进行超参数调优
"""

import sys
import os
import time
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV, cross_val_score
from sklearn.metrics import accuracy_score, classification_report
from sklearn.ensemble import RandomForestClassifier
import warnings
warnings.filterwarnings('ignore')

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from main import SimpleFusionV8
    from utils.data_processor import DataProcessor
    from core.ml_models import XGBoostModel, NeuralNetworkModel
    print("✅ 成功导入所需模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, config_path="config/config.yaml"):
        """初始化优化器"""
        self.system = SimpleFusionV8(config_path)
        self.system.initialize()
        
        self.data_processor = self.system.data_processor
        self.optimization_results = {}
        
        print("✅ 超参数优化器初始化完成")
    
    def load_training_data(self, limit=5000):
        """
        加载训练数据
        
        Args:
            limit: 数据量限制（用于快速测试）
            
        Returns:
            (X, y): 特征矩阵和标签向量
        """
        print(f"\n📊 加载训练数据 (限制: {limit} 条)...")
        
        # 加载历史数据
        df = self.data_processor.load_historical_data(limit=limit)
        
        if df.empty:
            raise ValueError("无法加载训练数据")
        
        print(f"✅ 成功加载 {len(df)} 条历史数据")
        
        # 创建增强特征
        X, y = self._create_enhanced_features(df)
        
        print(f"📈 训练数据统计:")
        print(f"   样本数量: {len(X)}")
        print(f"   特征维度: {X.shape[1] if len(X) > 0 else 0}")
        print(f"   正样本比例: {np.mean(y):.4f}")
        
        return X, y
    
    def _create_enhanced_features(self, df):
        """创建增强特征"""
        enhanced_features = []
        labels = []
        
        for idx, row in df.iterrows():
            # 模拟策略输出
            strategy_outputs = {
                'strategy_1': type('obj', (object,), {
                    'prediction': int(row['strategy_1']),
                    'confidence': 0.6 + np.random.random() * 0.3
                })(),
                'strategy_2': type('obj', (object,), {
                    'prediction': int(row['strategy_2']),
                    'confidence': 0.5 + np.random.random() * 0.4
                })(),
                'strategy_6': type('obj', (object,), {
                    'prediction': int(row['strategy_6']),
                    'confidence': 0.4 + np.random.random() * 0.4
                })()
            }
            
            # 创建历史上下文
            history = []
            if idx > 0:
                start_idx = max(0, idx - 10)
                for hist_idx in range(start_idx, idx):
                    if hist_idx < len(df):
                        hist_row = df.iloc[hist_idx]
                        history.append({'result': int(hist_row['actual_result'])})
            
            # 使用特征工程层提取特征
            features = self.system.feature_engineering.extract_features(strategy_outputs, history)
            
            # 转换为特征向量
            feature_vector = self._features_to_vector(features)
            enhanced_features.append(feature_vector)
            labels.append(int(row['actual_result']))
        
        return np.array(enhanced_features), np.array(labels)
    
    def _features_to_vector(self, features):
        """将特征字典转换为向量"""
        # 定义特征顺序（与ML模型保持一致）
        feature_names = [
            'consensus_ratio', 'weighted_consensus', 'avg_confidence',
            'min_confidence', 'max_confidence', 'confidence_std',
            'total_divergence', 'max_divergence',
            'strategy_1_prediction', 'strategy_2_prediction', 'strategy_6_prediction',
            'strategy_1_confidence', 'strategy_2_confidence', 'strategy_6_confidence',
            'win_rate_10', 'win_rate_5', 'current_streak',
            'strategy_1_weight', 'strategy_2_weight', 'strategy_6_weight'
        ]
        
        feature_vector = []
        for name in feature_names:
            feature_vector.append(features.get(name, 0.0))
        
        return feature_vector
    
    def optimize_xgboost(self, X, y):
        """优化XGBoost超参数"""
        print(f"\n🌳 优化XGBoost超参数...")
        
        try:
            import xgboost as xgb
            
            # 定义超参数搜索空间
            param_grid = {
                'n_estimators': [50, 100, 200],
                'max_depth': [3, 5, 7],
                'learning_rate': [0.01, 0.1, 0.2],
                'subsample': [0.8, 0.9, 1.0],
                'colsample_bytree': [0.8, 0.9, 1.0]
            }
            
            # 创建XGBoost分类器
            xgb_classifier = xgb.XGBClassifier(
                random_state=42,
                eval_metric='logloss'
            )
            
            # 网格搜索
            print("   执行网格搜索...")
            grid_search = GridSearchCV(
                xgb_classifier,
                param_grid,
                cv=3,
                scoring='accuracy',
                n_jobs=-1,
                verbose=0
            )
            
            grid_search.fit(X, y)
            
            # 保存结果
            self.optimization_results['xgboost'] = {
                'best_params': grid_search.best_params_,
                'best_score': grid_search.best_score_,
                'cv_results': grid_search.cv_results_
            }
            
            print(f"✅ XGBoost优化完成:")
            print(f"   最佳参数: {grid_search.best_params_}")
            print(f"   最佳得分: {grid_search.best_score_:.4f}")
            
            return grid_search.best_estimator_
            
        except ImportError:
            print("⚠️ XGBoost未安装，跳过优化")
            return None
        except Exception as e:
            print(f"❌ XGBoost优化失败: {str(e)}")
            return None
    
    def optimize_random_forest(self, X, y):
        """优化随机森林超参数（作为XGBoost的替代）"""
        print(f"\n🌲 优化随机森林超参数...")
        
        # 定义超参数搜索空间
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [5, 10, 15, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4],
            'max_features': ['sqrt', 'log2', None]
        }
        
        # 创建随机森林分类器
        rf_classifier = RandomForestClassifier(random_state=42)
        
        # 随机搜索（更快）
        print("   执行随机搜索...")
        random_search = RandomizedSearchCV(
            rf_classifier,
            param_grid,
            n_iter=20,  # 随机尝试20种组合
            cv=3,
            scoring='accuracy',
            n_jobs=-1,
            random_state=42,
            verbose=0
        )
        
        random_search.fit(X, y)
        
        # 保存结果
        self.optimization_results['random_forest'] = {
            'best_params': random_search.best_params_,
            'best_score': random_search.best_score_,
            'cv_results': random_search.cv_results_
        }
        
        print(f"✅ 随机森林优化完成:")
        print(f"   最佳参数: {random_search.best_params_}")
        print(f"   最佳得分: {random_search.best_score_:.4f}")
        
        return random_search.best_estimator_
    
    def optimize_neural_network(self, X, y):
        """优化神经网络超参数"""
        print(f"\n🧠 优化神经网络超参数...")
        
        try:
            from sklearn.neural_network import MLPClassifier
            
            # 定义超参数搜索空间
            param_grid = {
                'hidden_layer_sizes': [(50,), (100,), (50, 25), (100, 50)],
                'activation': ['relu', 'tanh'],
                'alpha': [0.0001, 0.001, 0.01],
                'learning_rate': ['constant', 'adaptive'],
                'max_iter': [200, 500]
            }
            
            # 创建神经网络分类器
            mlp_classifier = MLPClassifier(random_state=42)
            
            # 随机搜索
            print("   执行随机搜索...")
            random_search = RandomizedSearchCV(
                mlp_classifier,
                param_grid,
                n_iter=15,
                cv=3,
                scoring='accuracy',
                n_jobs=-1,
                random_state=42,
                verbose=0
            )
            
            random_search.fit(X, y)
            
            # 保存结果
            self.optimization_results['neural_network'] = {
                'best_params': random_search.best_params_,
                'best_score': random_search.best_score_,
                'cv_results': random_search.cv_results_
            }
            
            print(f"✅ 神经网络优化完成:")
            print(f"   最佳参数: {random_search.best_params_}")
            print(f"   最佳得分: {random_search.best_score_:.4f}")
            
            return random_search.best_estimator_
            
        except Exception as e:
            print(f"❌ 神经网络优化失败: {str(e)}")
            return None
    
    def evaluate_optimized_models(self, X, y):
        """评估优化后的模型"""
        print(f"\n📊 评估优化后的模型...")
        
        evaluation_results = {}
        
        for model_name, result in self.optimization_results.items():
            if 'best_score' in result:
                evaluation_results[model_name] = {
                    'cv_accuracy': result['best_score'],
                    'best_params': result['best_params']
                }
        
        # 按准确率排序
        sorted_results = sorted(evaluation_results.items(), 
                              key=lambda x: x[1]['cv_accuracy'], 
                              reverse=True)
        
        print(f"📈 模型性能排序:")
        for i, (model_name, result) in enumerate(sorted_results):
            print(f"   {i+1}. {model_name}: {result['cv_accuracy']:.4f}")
        
        return evaluation_results
    
    def save_optimization_results(self):
        """保存优化结果"""
        print(f"\n💾 保存优化结果...")
        
        try:
            import json
            
            # 保存结果到文件
            results_file = PROJECT_ROOT / "logs" / "hyperparameter_optimization.json"
            results_file.parent.mkdir(exist_ok=True)
            
            # 转换numpy类型为Python原生类型
            serializable_results = {}
            for model_name, result in self.optimization_results.items():
                serializable_results[model_name] = {
                    'best_params': result['best_params'],
                    'best_score': float(result['best_score'])
                }
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 优化结果已保存到: {results_file}")
            
        except Exception as e:
            print(f"❌ 保存失败: {str(e)}")


def main():
    """主优化函数"""
    print("🚀 V8系统超参数优化开始")
    print("=" * 60)
    
    try:
        # 创建优化器
        optimizer = HyperparameterOptimizer()
        
        # 加载训练数据
        X, y = optimizer.load_training_data(limit=2000)  # 使用较小数据集进行快速测试
        
        # 优化XGBoost
        xgb_model = optimizer.optimize_xgboost(X, y)
        
        # 优化随机森林（作为备选）
        rf_model = optimizer.optimize_random_forest(X, y)
        
        # 优化神经网络
        nn_model = optimizer.optimize_neural_network(X, y)
        
        # 评估所有模型
        evaluation_results = optimizer.evaluate_optimized_models(X, y)
        
        # 保存结果
        optimizer.save_optimization_results()
        
        # 显示最终结果
        print(f"\n" + "=" * 60)
        print("🎉 超参数优化完成！")
        
        print(f"\n📊 优化总结:")
        for model_name, result in optimizer.optimization_results.items():
            print(f"   {model_name}:")
            print(f"     最佳准确率: {result['best_score']:.4f}")
            print(f"     最佳参数: {result['best_params']}")
        
        return optimizer
        
    except Exception as e:
        print(f"❌ 优化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    optimizer = main()
