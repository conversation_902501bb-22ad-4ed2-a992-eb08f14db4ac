#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能风险管理模块

实现动态风险评估、实时风险监控、自适应风险阈值、风险预警机制等功能
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
import time
from collections import deque
from abc import ABC, abstractmethod
from enum import Enum
import warnings

try:
    from scipy import stats
    from sklearn.preprocessing import StandardScaler
    from sklearn.ensemble import IsolationForest
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False


class RiskLevel(Enum):
    """风险等级枚举"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


class AlertType(Enum):
    """预警类型枚举"""
    RISK_THRESHOLD = "risk_threshold"
    VOLATILITY_SPIKE = "volatility_spike"
    DRAWDOWN_WARNING = "drawdown_warning"
    CONSECUTIVE_LOSSES = "consecutive_losses"
    ANOMALY_DETECTED = "anomaly_detected"
    PERFORMANCE_DEGRADATION = "performance_degradation"


@dataclass
class RiskMetrics:
    """风险指标"""
    volatility: float
    max_drawdown: float
    var_95: float  # 95% Value at Risk
    var_99: float  # 99% Value at Risk
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    consecutive_losses: int
    win_rate: float
    avg_loss: float
    max_loss: float
    risk_score: float
    timestamp: float


@dataclass
class RiskAlert:
    """风险预警"""
    alert_id: str
    alert_type: AlertType
    risk_level: RiskLevel
    message: str
    metrics: Dict[str, float]
    recommendations: List[str]
    timestamp: float
    is_active: bool = True


@dataclass
class RiskThresholds:
    """风险阈值配置"""
    volatility_threshold: float = 0.3
    max_drawdown_threshold: float = 0.15
    var_95_threshold: float = 0.1
    consecutive_losses_threshold: int = 5
    win_rate_threshold: float = 0.4
    risk_score_threshold: float = 0.7
    performance_degradation_threshold: float = 0.2


class RiskCalculator:
    """风险计算器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_volatility(self, returns: List[float], window: int = 20) -> float:
        """计算波动率"""
        if len(returns) < 2:
            return 0.0
        
        recent_returns = returns[-window:] if len(returns) >= window else returns
        return np.std(recent_returns) * np.sqrt(252)  # 年化波动率
    
    def calculate_max_drawdown(self, returns: List[float]) -> float:
        """计算最大回撤"""
        if len(returns) < 2:
            return 0.0
        
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = running_max - cumulative_returns
        return np.max(drawdown)
    
    def calculate_var(self, returns: List[float], confidence: float = 0.95) -> float:
        """计算风险价值(VaR)"""
        if len(returns) < 10:
            return 0.0
        
        return np.percentile(returns, (1 - confidence) * 100)
    
    def calculate_sharpe_ratio(self, returns: List[float], risk_free_rate: float = 0.02) -> float:
        """计算夏普比率"""
        if len(returns) < 2:
            return 0.0
        
        excess_returns = np.array(returns) - risk_free_rate / 252
        if np.std(excess_returns) == 0:
            return 0.0
        
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
    
    def calculate_sortino_ratio(self, returns: List[float], risk_free_rate: float = 0.02) -> float:
        """计算索提诺比率"""
        if len(returns) < 2:
            return 0.0
        
        excess_returns = np.array(returns) - risk_free_rate / 252
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0:
            return float('inf') if np.mean(excess_returns) > 0 else 0.0
        
        downside_deviation = np.std(downside_returns)
        if downside_deviation == 0:
            return 0.0
        
        return np.mean(excess_returns) / downside_deviation * np.sqrt(252)
    
    def calculate_calmar_ratio(self, returns: List[float]) -> float:
        """计算卡玛比率"""
        if len(returns) < 2:
            return 0.0
        
        annual_return = np.mean(returns) * 252
        max_drawdown = self.calculate_max_drawdown(returns)
        
        if max_drawdown == 0:
            return float('inf') if annual_return > 0 else 0.0
        
        return annual_return / max_drawdown
    
    def calculate_consecutive_losses(self, results: List[bool]) -> int:
        """计算连续亏损次数"""
        if not results:
            return 0
        
        max_consecutive = 0
        current_consecutive = 0
        
        for result in reversed(results):  # 从最新开始计算
            if not result:  # 亏损
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                break  # 遇到盈利就停止
        
        return current_consecutive
    
    def calculate_win_rate(self, results: List[bool], window: int = 50) -> float:
        """计算胜率"""
        if not results:
            return 0.5
        
        recent_results = results[-window:] if len(results) >= window else results
        return sum(recent_results) / len(recent_results)


class DynamicRiskAssessor:
    """动态风险评估器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.calculator = RiskCalculator()
        
        # 历史数据
        self.returns_history = deque(maxlen=1000)
        self.results_history = deque(maxlen=500)
        self.confidence_history = deque(maxlen=200)
        self.prediction_history = deque(maxlen=200)
        
        # 风险阈值
        self.thresholds = RiskThresholds(**config.get('risk_thresholds', {}))
        
        # 异常检测器
        if SCIPY_AVAILABLE:
            self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
            self.anomaly_fitted = False
        else:
            self.anomaly_detector = None
            self.anomaly_fitted = False
    
    def update_data(self, return_value: float, is_correct: bool, 
                   confidence: float, prediction: float):
        """更新历史数据"""
        self.returns_history.append(return_value)
        self.results_history.append(is_correct)
        self.confidence_history.append(confidence)
        self.prediction_history.append(prediction)
        
        # 更新异常检测器
        if self.anomaly_detector is not None and len(self.returns_history) >= 50:
            try:
                self._update_anomaly_detector()
            except Exception as e:
                # sklearn版本兼容性问题，禁用异常检测
                self.logger.warning(f"异常检测器初始化失败，已禁用: {str(e)}")
                self.anomaly_detector = None
    
    def _update_anomaly_detector(self):
        """更新异常检测器"""
        if not SCIPY_AVAILABLE:
            return
        
        try:
            # 准备特征数据
            features = []
            min_len = min(len(self.returns_history), len(self.confidence_history), 
                         len(self.prediction_history))
            
            for i in range(max(0, min_len - 50), min_len):
                feature_vector = [
                    list(self.returns_history)[i],
                    list(self.confidence_history)[i],
                    list(self.prediction_history)[i]
                ]
                features.append(feature_vector)
            
            if len(features) >= 10:
                self.anomaly_detector.fit(features)
                self.anomaly_fitted = True
                
        except Exception as e:
            self.logger.warning(f"异常检测器更新失败: {str(e)}")
    
    def assess_risk(self, current_context: Dict[str, Any]) -> RiskMetrics:
        """评估当前风险"""
        returns = list(self.returns_history)
        results = list(self.results_history)
        
        # 计算各项风险指标
        volatility = self.calculator.calculate_volatility(returns)
        max_drawdown = self.calculator.calculate_max_drawdown(returns)
        var_95 = abs(self.calculator.calculate_var(returns, 0.95))
        var_99 = abs(self.calculator.calculate_var(returns, 0.99))
        sharpe_ratio = self.calculator.calculate_sharpe_ratio(returns)
        sortino_ratio = self.calculator.calculate_sortino_ratio(returns)
        calmar_ratio = self.calculator.calculate_calmar_ratio(returns)
        consecutive_losses = self.calculator.calculate_consecutive_losses(results)
        win_rate = self.calculator.calculate_win_rate(results)
        
        # 计算损失相关指标
        losses = [r for r in returns if r < 0]
        avg_loss = np.mean(losses) if losses else 0.0
        max_loss = min(losses) if losses else 0.0
        
        # 计算综合风险评分
        risk_score = self._calculate_risk_score(
            volatility, max_drawdown, var_95, consecutive_losses, win_rate
        )
        
        return RiskMetrics(
            volatility=volatility,
            max_drawdown=max_drawdown,
            var_95=var_95,
            var_99=var_99,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            consecutive_losses=consecutive_losses,
            win_rate=win_rate,
            avg_loss=avg_loss,
            max_loss=max_loss,
            risk_score=risk_score,
            timestamp=time.time()
        )
    
    def _calculate_risk_score(self, volatility: float, max_drawdown: float, 
                            var_95: float, consecutive_losses: int, win_rate: float) -> float:
        """计算综合风险评分 (0-1, 越高风险越大)"""
        # 归一化各项指标
        vol_score = min(volatility / 0.5, 1.0)  # 50%波动率为满分
        dd_score = min(max_drawdown / 0.3, 1.0)  # 30%回撤为满分
        var_score = min(var_95 / 0.2, 1.0)  # 20% VaR为满分
        loss_score = min(consecutive_losses / 10, 1.0)  # 10连败为满分
        winrate_score = max(0, (0.5 - win_rate) / 0.5)  # 胜率低于50%开始计分
        
        # 加权计算综合评分
        weights = [0.25, 0.25, 0.2, 0.15, 0.15]
        scores = [vol_score, dd_score, var_score, loss_score, winrate_score]
        
        return sum(w * s for w, s in zip(weights, scores))
    
    def detect_anomaly(self, current_features: Dict[str, float]) -> bool:
        """检测异常"""
        if not self.anomaly_fitted or not SCIPY_AVAILABLE:
            return False
        
        try:
            feature_vector = [
                current_features.get('return_value', 0.0),
                current_features.get('confidence', 0.5),
                current_features.get('prediction', 0.5)
            ]
            
            prediction = self.anomaly_detector.predict([feature_vector])
            return prediction[0] == -1  # -1 表示异常
            
        except Exception as e:
            self.logger.warning(f"异常检测失败: {str(e)}")
            return False
    
    def get_risk_level(self, risk_score: float) -> RiskLevel:
        """根据风险评分获取风险等级"""
        if risk_score < 0.2:
            return RiskLevel.VERY_LOW
        elif risk_score < 0.4:
            return RiskLevel.LOW
        elif risk_score < 0.6:
            return RiskLevel.MEDIUM
        elif risk_score < 0.8:
            return RiskLevel.HIGH
        elif risk_score < 0.9:
            return RiskLevel.VERY_HIGH
        else:
            return RiskLevel.CRITICAL


class RiskMonitor:
    """实时风险监控器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 风险评估器
        self.risk_assessor = DynamicRiskAssessor(config)
        
        # 监控状态
        self.monitoring_active = True
        self.last_assessment_time = 0
        self.assessment_frequency = config.get('assessment_frequency', 10)  # 每10次决策评估一次
        
        # 预警系统
        self.active_alerts = {}
        self.alert_history = deque(maxlen=100)
        
        # 自适应阈值
        self.adaptive_thresholds = True
        self.threshold_adjustment_factor = 0.1
        
    def monitor_decision(self, decision_data: Dict[str, Any], 
                        actual_result: Optional[int] = None) -> List[RiskAlert]:
        """监控单次决策的风险"""
        alerts = []
        
        if not self.monitoring_active:
            return alerts
        
        # 更新数据
        if actual_result is not None:
            return_value = 1.0 if decision_data.get('prediction') == actual_result else -1.0
            is_correct = decision_data.get('prediction') == actual_result
            confidence = decision_data.get('confidence', 0.5)
            prediction = decision_data.get('prediction', 0.5)
            
            self.risk_assessor.update_data(return_value, is_correct, confidence, prediction)
        
        # 定期风险评估
        decision_count = decision_data.get('decision_count', 0)
        if decision_count % self.assessment_frequency == 0:
            alerts.extend(self._perform_risk_assessment(decision_data))
        
        # 实时异常检测
        anomaly_alert = self._check_anomaly(decision_data)
        if anomaly_alert:
            alerts.append(anomaly_alert)
        
        return alerts
    
    def _perform_risk_assessment(self, context: Dict[str, Any]) -> List[RiskAlert]:
        """执行风险评估"""
        alerts = []
        
        # 获取风险指标
        risk_metrics = self.risk_assessor.assess_risk(context)
        risk_level = self.risk_assessor.get_risk_level(risk_metrics.risk_score)
        
        # 检查各项风险阈值
        thresholds = self.risk_assessor.thresholds
        
        # 波动率预警
        if risk_metrics.volatility > thresholds.volatility_threshold:
            alert = self._create_alert(
                AlertType.VOLATILITY_SPIKE,
                risk_level,
                f"波动率过高: {risk_metrics.volatility:.3f} > {thresholds.volatility_threshold:.3f}",
                {'volatility': risk_metrics.volatility},
                ["降低仓位大小", "提高止损阈值", "暂停高风险决策"]
            )
            alerts.append(alert)
        
        # 回撤预警
        if risk_metrics.max_drawdown > thresholds.max_drawdown_threshold:
            alert = self._create_alert(
                AlertType.DRAWDOWN_WARNING,
                risk_level,
                f"最大回撤过大: {risk_metrics.max_drawdown:.3f} > {thresholds.max_drawdown_threshold:.3f}",
                {'max_drawdown': risk_metrics.max_drawdown},
                ["立即止损", "降低风险敞口", "重新评估策略"]
            )
            alerts.append(alert)
        
        # 连续亏损预警
        if risk_metrics.consecutive_losses >= thresholds.consecutive_losses_threshold:
            alert = self._create_alert(
                AlertType.CONSECUTIVE_LOSSES,
                risk_level,
                f"连续亏损次数过多: {risk_metrics.consecutive_losses} >= {thresholds.consecutive_losses_threshold}",
                {'consecutive_losses': risk_metrics.consecutive_losses},
                ["暂停交易", "检查策略有效性", "降低决策频率"]
            )
            alerts.append(alert)
        
        # 胜率预警
        if risk_metrics.win_rate < thresholds.win_rate_threshold:
            alert = self._create_alert(
                AlertType.PERFORMANCE_DEGRADATION,
                risk_level,
                f"胜率过低: {risk_metrics.win_rate:.3f} < {thresholds.win_rate_threshold:.3f}",
                {'win_rate': risk_metrics.win_rate},
                ["重新训练模型", "调整策略参数", "增加数据质量检查"]
            )
            alerts.append(alert)
        
        # 综合风险评分预警
        if risk_metrics.risk_score > thresholds.risk_score_threshold:
            alert = self._create_alert(
                AlertType.RISK_THRESHOLD,
                risk_level,
                f"综合风险评分过高: {risk_metrics.risk_score:.3f} > {thresholds.risk_score_threshold:.3f}",
                {'risk_score': risk_metrics.risk_score},
                ["全面风险控制", "降低所有风险敞口", "启动应急预案"]
            )
            alerts.append(alert)
        
        # 自适应调整阈值
        if self.adaptive_thresholds:
            self._adjust_thresholds(risk_metrics)
        
        # 记录预警
        for alert in alerts:
            self.active_alerts[alert.alert_id] = alert
            self.alert_history.append(alert)
        
        self.last_assessment_time = time.time()
        
        return alerts
    
    def _check_anomaly(self, decision_data: Dict[str, Any]) -> Optional[RiskAlert]:
        """检查异常"""
        features = {
            'return_value': decision_data.get('return_value', 0.0),
            'confidence': decision_data.get('confidence', 0.5),
            'prediction': decision_data.get('prediction', 0.5)
        }
        
        if self.risk_assessor.detect_anomaly(features):
            return self._create_alert(
                AlertType.ANOMALY_DETECTED,
                RiskLevel.HIGH,
                "检测到异常决策模式",
                features,
                ["检查数据质量", "验证模型状态", "人工审核决策"]
            )
        
        return None
    
    def _create_alert(self, alert_type: AlertType, risk_level: RiskLevel,
                     message: str, metrics: Dict[str, float],
                     recommendations: List[str]) -> RiskAlert:
        """创建风险预警"""
        alert_id = f"{alert_type.value}_{int(time.time() * 1000)}"
        
        return RiskAlert(
            alert_id=alert_id,
            alert_type=alert_type,
            risk_level=risk_level,
            message=message,
            metrics=metrics,
            recommendations=recommendations,
            timestamp=time.time()
        )
    
    def _adjust_thresholds(self, risk_metrics: RiskMetrics):
        """自适应调整风险阈值"""
        # 基于历史表现调整阈值
        adjustment = self.threshold_adjustment_factor
        
        # 如果系统表现良好，可以适当放宽阈值
        if (risk_metrics.win_rate > 0.6 and 
            risk_metrics.sharpe_ratio > 1.0 and 
            risk_metrics.max_drawdown < 0.1):
            
            self.risk_assessor.thresholds.volatility_threshold *= (1 + adjustment)
            self.risk_assessor.thresholds.risk_score_threshold *= (1 + adjustment)
            
        # 如果系统表现不佳，收紧阈值
        elif (risk_metrics.win_rate < 0.4 or 
              risk_metrics.consecutive_losses > 3 or 
              risk_metrics.max_drawdown > 0.15):
            
            self.risk_assessor.thresholds.volatility_threshold *= (1 - adjustment)
            self.risk_assessor.thresholds.risk_score_threshold *= (1 - adjustment)
        
        # 确保阈值在合理范围内
        self.risk_assessor.thresholds.volatility_threshold = np.clip(
            self.risk_assessor.thresholds.volatility_threshold, 0.1, 1.0
        )
        self.risk_assessor.thresholds.risk_score_threshold = np.clip(
            self.risk_assessor.thresholds.risk_score_threshold, 0.3, 0.9
        )
    
    def get_current_risk_status(self) -> Dict[str, Any]:
        """获取当前风险状态"""
        risk_metrics = self.risk_assessor.assess_risk({})
        risk_level = self.risk_assessor.get_risk_level(risk_metrics.risk_score)
        
        active_alerts_count = len([a for a in self.active_alerts.values() if a.is_active])
        
        return {
            'risk_level': risk_level.value,
            'risk_score': risk_metrics.risk_score,
            'risk_metrics': {
                'volatility': risk_metrics.volatility,
                'max_drawdown': risk_metrics.max_drawdown,
                'var_95': risk_metrics.var_95,
                'sharpe_ratio': risk_metrics.sharpe_ratio,
                'consecutive_losses': risk_metrics.consecutive_losses,
                'win_rate': risk_metrics.win_rate
            },
            'active_alerts': active_alerts_count,
            'monitoring_active': self.monitoring_active,
            'last_assessment': self.last_assessment_time,
            'thresholds': {
                'volatility': self.risk_assessor.thresholds.volatility_threshold,
                'max_drawdown': self.risk_assessor.thresholds.max_drawdown_threshold,
                'risk_score': self.risk_assessor.thresholds.risk_score_threshold
            }
        }
    
    def resolve_alert(self, alert_id: str):
        """解决预警"""
        if alert_id in self.active_alerts:
            self.active_alerts[alert_id].is_active = False
            self.logger.info(f"预警 {alert_id} 已解决")
    
    def get_active_alerts(self) -> List[RiskAlert]:
        """获取活跃预警"""
        return [alert for alert in self.active_alerts.values() if alert.is_active]
    
    def get_alert_history(self) -> List[RiskAlert]:
        """获取预警历史"""
        return list(self.alert_history)


class IntelligentRiskManager:
    """智能风险管理器"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化智能风险管理器

        Args:
            config: 配置字典
        """
        self.config = config.get('intelligent_risk', {})
        self.logger = logging.getLogger(__name__)

        # 风险监控器
        self.risk_monitor = RiskMonitor(self.config)

        # 风险控制状态
        self.risk_control_active = True
        self.emergency_mode = False
        self.position_scaling_factor = 1.0

        # 决策拦截
        self.decision_blocking_enabled = True
        self.blocked_decisions_count = 0

        # 性能跟踪
        self.performance_tracker = deque(maxlen=1000)
        self.risk_adjusted_returns = deque(maxlen=500)

        # 风险预算
        self.daily_risk_budget = self.config.get('daily_risk_budget', 0.02)  # 2%
        self.current_risk_usage = 0.0

        self.logger.info("智能风险管理器初始化完成")

    def evaluate_decision_risk(self, decision_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估决策风险

        Args:
            decision_data: 决策数据

        Returns:
            风险评估结果
        """
        # 获取当前风险状态
        risk_status = self.risk_monitor.get_current_risk_status()

        # 计算决策风险
        decision_risk = self._calculate_decision_risk(decision_data, risk_status)

        # 检查是否应该拦截决策
        should_block = self._should_block_decision(decision_risk, risk_status)

        # 计算建议的仓位调整
        position_adjustment = self._calculate_position_adjustment(decision_risk, risk_status)

        # 生成风险建议
        recommendations = self._generate_risk_recommendations(decision_risk, risk_status)

        return {
            'decision_risk_score': decision_risk,
            'current_risk_level': risk_status['risk_level'],
            'should_block_decision': should_block,
            'position_adjustment': position_adjustment,
            'recommendations': recommendations,
            'risk_budget_usage': self.current_risk_usage / self.daily_risk_budget,
            'emergency_mode': self.emergency_mode
        }

    def _calculate_decision_risk(self, decision_data: Dict[str, Any],
                               risk_status: Dict[str, Any]) -> float:
        """计算单次决策的风险评分"""
        # 基础风险评分
        base_risk = 0.5

        # 置信度调整
        confidence = decision_data.get('confidence', 0.5)
        confidence_risk = (1 - confidence) * 0.3

        # 当前系统风险状态调整
        system_risk = risk_status.get('risk_score', 0.5) * 0.4

        # 连续亏损调整
        consecutive_losses = risk_status['risk_metrics'].get('consecutive_losses', 0)
        loss_risk = min(consecutive_losses / 10, 0.3)

        # 波动率调整
        volatility = risk_status['risk_metrics'].get('volatility', 0.2)
        volatility_risk = min(volatility / 0.5, 0.2) * 0.2

        # 综合风险评分
        total_risk = base_risk + confidence_risk + system_risk + loss_risk + volatility_risk

        return min(total_risk, 1.0)

    def _should_block_decision(self, decision_risk: float,
                             risk_status: Dict[str, Any]) -> bool:
        """判断是否应该拦截决策"""
        if not self.decision_blocking_enabled:
            return False

        # 紧急模式下拦截所有决策
        if self.emergency_mode:
            return True

        # 高风险决策拦截
        if decision_risk > 0.8:
            return True

        # 系统风险过高时拦截
        if risk_status.get('risk_score', 0) > 0.7:
            return True

        # 活跃预警过多时拦截
        if risk_status.get('active_alerts', 0) >= 3:
            return True

        # 风险预算耗尽时拦截
        if self.current_risk_usage >= self.daily_risk_budget:
            return True

        return False

    def _calculate_position_adjustment(self, decision_risk: float,
                                     risk_status: Dict[str, Any]) -> float:
        """计算仓位调整系数"""
        # 基础仓位系数
        base_factor = 1.0

        # 根据决策风险调整
        risk_adjustment = 1.0 - (decision_risk * 0.5)

        # 根据系统风险调整
        system_risk = risk_status.get('risk_score', 0.5)
        system_adjustment = 1.0 - (system_risk * 0.3)

        # 根据胜率调整
        win_rate = risk_status['risk_metrics'].get('win_rate', 0.5)
        if win_rate < 0.4:
            winrate_adjustment = 0.5
        elif win_rate > 0.6:
            winrate_adjustment = 1.2
        else:
            winrate_adjustment = 1.0

        # 综合调整系数
        adjustment_factor = base_factor * risk_adjustment * system_adjustment * winrate_adjustment

        # 应用全局缩放因子
        final_factor = adjustment_factor * self.position_scaling_factor

        return max(0.1, min(final_factor, 2.0))  # 限制在0.1-2.0之间

    def _generate_risk_recommendations(self, decision_risk: float,
                                     risk_status: Dict[str, Any]) -> List[str]:
        """生成风险管理建议"""
        recommendations = []

        # 基于决策风险的建议
        if decision_risk > 0.7:
            recommendations.append("决策风险较高，建议降低仓位或跳过此次决策")
        elif decision_risk > 0.5:
            recommendations.append("决策风险中等，建议适当降低仓位")

        # 基于系统风险的建议
        system_risk = risk_status.get('risk_score', 0.5)
        if system_risk > 0.7:
            recommendations.append("系统整体风险偏高，建议暂停交易或大幅降低仓位")
        elif system_risk > 0.5:
            recommendations.append("系统风险中等，建议谨慎操作")

        # 基于具体指标的建议
        metrics = risk_status.get('risk_metrics', {})

        if metrics.get('consecutive_losses', 0) >= 3:
            recommendations.append("连续亏损较多，建议暂停交易并检查策略")

        if metrics.get('win_rate', 0.5) < 0.4:
            recommendations.append("胜率偏低，建议重新评估和优化策略")

        if metrics.get('max_drawdown', 0) > 0.1:
            recommendations.append("回撤较大，建议立即止损并降低风险敞口")

        if metrics.get('volatility', 0.2) > 0.3:
            recommendations.append("波动率过高，建议降低仓位并提高止损频率")

        # 风险预算建议
        budget_usage = self.current_risk_usage / self.daily_risk_budget
        if budget_usage > 0.8:
            recommendations.append("风险预算即将耗尽，建议暂停交易")
        elif budget_usage > 0.6:
            recommendations.append("风险预算使用较多，建议降低交易频率")

        return recommendations

    def process_decision_feedback(self, decision_id: str, decision_data: Dict[str, Any],
                                actual_result: int) -> List[RiskAlert]:
        """
        处理决策反馈

        Args:
            decision_id: 决策ID
            decision_data: 决策数据
            actual_result: 实际结果

        Returns:
            生成的风险预警列表
        """
        # 计算收益
        prediction = decision_data.get('prediction', 0)
        return_value = 1.0 if prediction == actual_result else -1.0

        # 更新风险预算使用
        risk_used = abs(return_value) * decision_data.get('position_size', 1.0)
        self.current_risk_usage += risk_used

        # 更新性能跟踪
        self.performance_tracker.append({
            'decision_id': decision_id,
            'return': return_value,
            'risk_adjusted_return': return_value / max(decision_data.get('risk_score', 0.5), 0.1),
            'timestamp': time.time()
        })

        # 风险监控
        enhanced_decision_data = decision_data.copy()
        enhanced_decision_data.update({
            'return_value': return_value,
            'actual_result': actual_result,
            'decision_count': len(self.performance_tracker)
        })

        alerts = self.risk_monitor.monitor_decision(enhanced_decision_data, actual_result)

        # 检查是否需要启动紧急模式
        self._check_emergency_mode(alerts)

        # 动态调整仓位缩放因子
        self._adjust_position_scaling()

        return alerts

    def _check_emergency_mode(self, alerts: List[RiskAlert]):
        """检查是否需要启动紧急模式"""
        # 统计高风险预警
        high_risk_alerts = [a for a in alerts if a.risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]]

        # 获取当前风险状态
        risk_status = self.risk_monitor.get_current_risk_status()

        # 紧急模式触发条件
        emergency_triggers = [
            len(high_risk_alerts) >= 2,  # 同时出现2个以上高风险预警
            risk_status.get('risk_score', 0) > 0.9,  # 风险评分超过90%
            risk_status['risk_metrics'].get('consecutive_losses', 0) >= 8,  # 连续8次亏损
            risk_status['risk_metrics'].get('max_drawdown', 0) > 0.2,  # 回撤超过20%
            self.current_risk_usage > self.daily_risk_budget * 1.5  # 风险预算超支50%
        ]

        if any(emergency_triggers) and not self.emergency_mode:
            self.emergency_mode = True
            self.position_scaling_factor = 0.1  # 紧急模式下大幅降低仓位
            self.logger.critical("启动紧急风险控制模式")

            # 创建紧急预警
            emergency_alert = RiskAlert(
                alert_id=f"emergency_{int(time.time() * 1000)}",
                alert_type=AlertType.RISK_THRESHOLD,
                risk_level=RiskLevel.CRITICAL,
                message="系统风险过高，已启动紧急模式",
                metrics={'emergency_triggers': sum(emergency_triggers)},
                recommendations=["立即停止所有交易", "全面风险评估", "人工干预"],
                timestamp=time.time()
            )
            alerts.append(emergency_alert)

        # 紧急模式退出条件
        elif self.emergency_mode:
            exit_conditions = [
                risk_status.get('risk_score', 1.0) < 0.5,
                risk_status['risk_metrics'].get('consecutive_losses', 10) == 0,
                len(self.risk_monitor.get_active_alerts()) == 0
            ]

            if all(exit_conditions):
                self.emergency_mode = False
                self.position_scaling_factor = 0.5  # 逐步恢复仓位
                self.logger.info("退出紧急风险控制模式")

    def _adjust_position_scaling(self):
        """动态调整仓位缩放因子"""
        if self.emergency_mode:
            return  # 紧急模式下不调整

        # 基于最近表现调整
        if len(self.performance_tracker) >= 10:
            recent_returns = [p['return'] for p in list(self.performance_tracker)[-10:]]
            recent_performance = sum(recent_returns) / len(recent_returns)

            if recent_performance > 0.2:  # 表现良好
                self.position_scaling_factor = min(self.position_scaling_factor * 1.05, 1.5)
            elif recent_performance < -0.2:  # 表现不佳
                self.position_scaling_factor = max(self.position_scaling_factor * 0.95, 0.3)

    def reset_daily_risk_budget(self):
        """重置每日风险预算"""
        self.current_risk_usage = 0.0
        self.logger.info("每日风险预算已重置")

    def get_risk_management_status(self) -> Dict[str, Any]:
        """获取风险管理状态"""
        risk_status = self.risk_monitor.get_current_risk_status()
        active_alerts = self.risk_monitor.get_active_alerts()

        # 计算风险调整收益
        if self.performance_tracker:
            recent_performance = list(self.performance_tracker)[-50:]
            avg_return = np.mean([p['return'] for p in recent_performance])
            avg_risk_adjusted = np.mean([p['risk_adjusted_return'] for p in recent_performance])
        else:
            avg_return = 0.0
            avg_risk_adjusted = 0.0

        return {
            'risk_control_active': self.risk_control_active,
            'emergency_mode': self.emergency_mode,
            'position_scaling_factor': self.position_scaling_factor,
            'blocked_decisions_count': self.blocked_decisions_count,
            'risk_budget': {
                'daily_budget': self.daily_risk_budget,
                'current_usage': self.current_risk_usage,
                'usage_percentage': (self.current_risk_usage / self.daily_risk_budget) * 100
            },
            'performance': {
                'avg_return': avg_return,
                'avg_risk_adjusted_return': avg_risk_adjusted,
                'total_decisions': len(self.performance_tracker)
            },
            'current_risk': risk_status,
            'active_alerts': [
                {
                    'alert_id': alert.alert_id,
                    'type': alert.alert_type.value,
                    'level': alert.risk_level.value,
                    'message': alert.message,
                    'recommendations': alert.recommendations
                }
                for alert in active_alerts
            ]
        }

    def force_emergency_mode(self, enable: bool):
        """强制启用/禁用紧急模式"""
        self.emergency_mode = enable
        if enable:
            self.position_scaling_factor = 0.1
            self.logger.warning("手动启动紧急风险控制模式")
        else:
            self.position_scaling_factor = 0.5
            self.logger.info("手动退出紧急风险控制模式")

    def adjust_risk_thresholds(self, new_thresholds: Dict[str, float]):
        """调整风险阈值"""
        for key, value in new_thresholds.items():
            if hasattr(self.risk_monitor.risk_assessor.thresholds, key):
                setattr(self.risk_monitor.risk_assessor.thresholds, key, value)
                self.logger.info(f"风险阈值 {key} 调整为 {value}")

    def resolve_alert(self, alert_id: str):
        """解决风险预警"""
        self.risk_monitor.resolve_alert(alert_id)

    def get_risk_recommendations(self, decision_data: Dict[str, Any]) -> List[str]:
        """获取风险管理建议"""
        risk_evaluation = self.evaluate_decision_risk(decision_data)
        return risk_evaluation['recommendations']
