#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V8系统模型训练脚本

使用V6的23,175条真实历史数据训练所有ML模型
"""

import sys
import os
import time
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from main import SimpleFusionV8
    from utils.data_processor import DataProcessor
    from core.ml_models import XGBoostModel, NeuralNetworkModel
    print("✅ 成功导入所需模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


class ModelTrainer:
    """模型训练器"""
    
    def __init__(self, config_path="config/config.yaml"):
        """初始化训练器"""
        self.system = SimpleFusionV8(config_path)
        self.system.initialize()
        
        self.data_processor = self.system.data_processor
        self.ml_models = self.system.ml_models
        
        # 训练统计
        self.training_stats = {}
        
    def load_training_data(self, limit=None):
        """
        加载训练数据
        
        Args:
            limit: 限制数据量，None表示加载全部
            
        Returns:
            (X, y, df): 特征矩阵、标签向量、原始数据
        """
        print(f"\n📊 加载训练数据...")
        
        # 加载历史数据
        df = self.data_processor.load_historical_data(limit=limit)
        
        if df.empty:
            raise ValueError("无法加载训练数据")
        
        print(f"✅ 成功加载 {len(df)} 条历史数据")
        
        # 数据质量检查
        quality_report = self.data_processor.validate_data_quality(df)
        print(f"📋 数据质量: {quality_report['status']}")
        
        if quality_report['status'] == 'error':
            print(f"❌ 数据质量问题: {quality_report['issues']}")
            raise ValueError("数据质量不符合要求")
        
        # 准备训练数据
        X, y = self.data_processor.prepare_training_data(df)
        
        print(f"📈 训练数据统计:")
        print(f"   样本数量: {len(X)}")
        print(f"   特征维度: {X.shape[1] if len(X) > 0 else 0}")
        print(f"   正样本比例: {np.mean(y):.4f}")
        print(f"   负样本比例: {1 - np.mean(y):.4f}")
        
        return X, y, df
    
    def create_enhanced_features(self, df):
        """
        创建增强特征
        
        Args:
            df: 原始数据
            
        Returns:
            增强特征矩阵
        """
        print(f"\n🔬 创建增强特征...")
        
        enhanced_features = []
        
        for idx, row in df.iterrows():
            # 模拟策略输出
            strategy_outputs = {
                'strategy_1': type('obj', (object,), {
                    'prediction': int(row['strategy_1']),
                    'confidence': 0.6 + np.random.random() * 0.3  # 模拟置信度
                })(),
                'strategy_2': type('obj', (object,), {
                    'prediction': int(row['strategy_2']),
                    'confidence': 0.5 + np.random.random() * 0.4
                })(),
                'strategy_6': type('obj', (object,), {
                    'prediction': int(row['strategy_6']),
                    'confidence': 0.4 + np.random.random() * 0.4
                })()
            }
            
            # 创建历史上下文（简化版）
            history = []
            if idx > 0:
                # 获取前面的几条记录作为历史
                start_idx = max(0, idx - 10)
                for hist_idx in range(start_idx, idx):
                    if hist_idx < len(df):
                        hist_row = df.iloc[hist_idx]
                        history.append({
                            'result': int(hist_row['actual_result'])
                        })
            
            # 使用特征工程层提取特征
            features = self.system.feature_engineering.extract_features(strategy_outputs, history)
            
            # 转换为特征向量
            feature_vector = self._features_to_vector(features)
            enhanced_features.append(feature_vector)
        
        enhanced_X = np.array(enhanced_features)
        
        print(f"✅ 增强特征创建完成:")
        print(f"   特征维度: {enhanced_X.shape[1]}")
        print(f"   样本数量: {enhanced_X.shape[0]}")
        
        return enhanced_X
    
    def _features_to_vector(self, features):
        """将特征字典转换为向量 (包含策略级连胜连败特征)"""
        # 定义完整特征顺序 (包含新的策略级特征)
        feature_names = [
            # 基础特征
            'consensus_ratio', 'weighted_consensus', 'avg_confidence',
            'min_confidence', 'max_confidence', 'confidence_std',
            'total_divergence', 'max_divergence',
            'strategy_1_prediction', 'strategy_2_prediction', 'strategy_6_prediction',
            'strategy_1_confidence', 'strategy_2_confidence', 'strategy_6_confidence',
            'win_rate_10', 'win_rate_5', 'current_streak',
            'strategy_1_weight', 'strategy_2_weight', 'strategy_6_weight',

            # 策略级连胜连败特征 (8个策略)
        ]

        # 动态添加8个策略的特征
        for i in range(1, 9):
            strategy = f'strategy_{i}'
            feature_names.extend([
                f'{strategy}_current_streak', f'{strategy}_max_win_streak', f'{strategy}_max_loss_streak',
                f'{strategy}_winrate_3', f'{strategy}_winrate_5', f'{strategy}_winrate_10', f'{strategy}_winrate_20',
                f'{strategy}_winrate_trend', f'{strategy}_weighted_winrate',
                f'{strategy}_stability_3', f'{strategy}_stability_5', f'{strategy}_stability_10'
            ])

        # 策略比较特征
        feature_names.extend([
            'best_winrate_strategy', 'best_winrate_value', 'winrate_range', 'winrate_std',
            'best_winrate_10_strategy', 'winrate_10_range', 'best_weighted_strategy', 'weighted_winrate_range'
        ])

        feature_vector = []
        for name in feature_names:
            value = features.get(name, 0.0)
            # 处理字符串类型的特征 (如best_winrate_strategy)
            if isinstance(value, str):
                # 将策略名称转换为数值
                if 'strategy_1' in value:
                    value = 1.0
                elif 'strategy_2' in value:
                    value = 2.0
                elif 'strategy_6' in value:
                    value = 6.0
                else:
                    value = 0.0
            feature_vector.append(float(value))

        return feature_vector
    
    def train_xgboost_model(self, X, y):
        """训练XGBoost模型"""
        print(f"\n🌳 训练XGBoost模型...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 获取XGBoost模型
        xgb_model = self.ml_models.models.get('xgboost')
        if not xgb_model:
            print("❌ XGBoost模型不可用")
            return None
        
        # 训练模型
        start_time = time.time()
        xgb_model.train(X_train, y_train)
        training_time = time.time() - start_time
        
        # 评估模型
        train_score = xgb_model.model.score(X_train, y_train) if hasattr(xgb_model.model, 'score') else 0
        test_score = xgb_model.model.score(X_test, y_test) if hasattr(xgb_model.model, 'score') else 0
        
        # 预测
        if hasattr(xgb_model.model, 'predict'):
            y_pred = xgb_model.model.predict(X_test)
            test_accuracy = accuracy_score(y_test, y_pred)
        else:
            test_accuracy = 0
        
        print(f"✅ XGBoost训练完成:")
        print(f"   训练时间: {training_time:.2f}秒")
        print(f"   训练准确率: {train_score:.4f}")
        print(f"   测试准确率: {test_accuracy:.4f}")
        
        # 保存统计
        self.training_stats['xgboost'] = {
            'training_time': training_time,
            'train_accuracy': train_score,
            'test_accuracy': test_accuracy,
            'training_samples': len(X_train),
            'test_samples': len(X_test)
        }
        
        return xgb_model
    
    def train_neural_network(self, X, y):
        """训练神经网络模型"""
        print(f"\n🧠 训练神经网络模型...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 获取神经网络模型
        nn_model = self.ml_models.models.get('neural_network')
        if not nn_model:
            print("❌ 神经网络模型不可用")
            return None
        
        # 训练模型
        start_time = time.time()
        nn_model.train(X_train, y_train)
        training_time = time.time() - start_time
        
        print(f"✅ 神经网络训练完成:")
        print(f"   训练时间: {training_time:.2f}秒")
        
        # 保存统计
        self.training_stats['neural_network'] = {
            'training_time': training_time,
            'training_samples': len(X_train),
            'test_samples': len(X_test)
        }
        
        return nn_model

    def train_strategy_selector(self, X, y, df):
        """训练策略选择模型 (核心新功能)"""
        print(f"\n🎯 训练策略选择模型...")

        # 准备策略选择训练数据
        strategy_X, strategy_y = self._prepare_strategy_selection_data(df)

        if len(strategy_X) == 0:
            print("❌ 策略选择训练数据不足")
            return None

        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            strategy_X, strategy_y, test_size=0.2, random_state=42, stratify=strategy_y
        )

        # 获取策略选择模型
        strategy_model = self.ml_models.models.get('strategy_selector')
        if not strategy_model:
            print("❌ 策略选择模型不可用")
            return None

        # 训练模型
        start_time = time.time()
        strategy_model.train(X_train, y_train)
        training_time = time.time() - start_time

        # 评估模型
        if hasattr(strategy_model.model, 'score'):
            train_score = strategy_model.model.score(X_train, y_train)
            test_score = strategy_model.model.score(X_test, y_test)
        else:
            train_score = 0
            test_score = 0

        print(f"✅ 策略选择模型训练完成:")
        print(f"   训练时间: {training_time:.2f}秒")
        print(f"   训练准确率: {train_score:.4f}")
        print(f"   测试准确率: {test_score:.4f}")

        # 保存统计
        self.training_stats['strategy_selector'] = {
            'training_time': training_time,
            'train_accuracy': train_score,
            'test_accuracy': test_score,
            'training_samples': len(X_train),
            'test_samples': len(X_test)
        }

        return strategy_model

    def _prepare_strategy_selection_data(self, df):
        """准备策略选择训练数据 (使用8个策略的连胜连败特征)"""
        print(f"   准备策略选择训练数据 (8个策略)...")

        strategy_features = []
        strategy_labels = []

        # 使用所有8个策略
        all_strategies = [f'strategy_{i}' for i in range(1, 9)]
        window_sizes = [3, 5, 10, 20]

        for idx, row in df.iterrows():
            # 跳过前30行，确保有足够的历史数据计算移动窗口
            if idx < 30:
                continue

            # 获取历史数据
            history_start = max(0, idx - 30)
            history_data = df.iloc[history_start:idx]

            # 计算每个策略的完整连胜连败特征
            strategy_performance = {}
            features = []

            for strategy in all_strategies:
                if strategy in history_data.columns:
                    predictions = history_data[strategy].values
                    actuals = history_data['actual_result'].values

                    # 计算连胜连败
                    current_streak = self._calculate_streak(predictions, actuals)
                    max_win_streak = self._calculate_max_streak(predictions, actuals, target=True)
                    max_loss_streak = self._calculate_max_streak(predictions, actuals, target=False)

                    # 计算多个移动窗口胜率
                    winrates = {}
                    for window in window_sizes:
                        if len(predictions) >= window:
                            recent_preds = predictions[-window:]
                            recent_actuals = actuals[-window:]
                            winrates[window] = (recent_preds == recent_actuals).mean()
                        else:
                            winrates[window] = (predictions == actuals).mean() if len(predictions) > 0 else 0.5

                    # 计算加权胜率 (近期权重更高)
                    if len(predictions) > 0:
                        weights = [0.9 ** i for i in range(len(predictions))]
                        weights.reverse()
                        weighted_correct = sum((predictions == actuals) * weights)
                        weighted_winrate = weighted_correct / sum(weights)
                    else:
                        weighted_winrate = 0.5

                    # 计算胜率趋势
                    if len(predictions) >= 10:
                        mid_point = len(predictions) // 2
                        early_winrate = (predictions[:mid_point] == actuals[:mid_point]).mean()
                        late_winrate = (predictions[mid_point:] == actuals[mid_point:]).mean()
                        winrate_trend = late_winrate - early_winrate
                    else:
                        winrate_trend = 0

                    strategy_performance[strategy] = {
                        'current_streak': current_streak,
                        'max_win_streak': max_win_streak,
                        'max_loss_streak': max_loss_streak,
                        'winrates': winrates,
                        'weighted_winrate': weighted_winrate,
                        'winrate_trend': winrate_trend,
                        'overall_accuracy': (predictions == actuals).mean() if len(predictions) > 0 else 0.5
                    }

                    # 构造该策略的特征向量
                    strategy_features_single = [
                        current_streak,
                        max_win_streak,
                        max_loss_streak,
                        winrates[3],
                        winrates[5],
                        winrates[10],
                        winrates[20],
                        weighted_winrate,
                        winrate_trend
                    ]
                    features.extend(strategy_features_single)

                else:
                    # 策略不存在，使用默认值
                    default_features = [0, 0, 0, 0.5, 0.5, 0.5, 0.5, 0.5, 0]
                    features.extend(default_features)
                    strategy_performance[strategy] = {'overall_accuracy': 0.5}

            # 确定最佳策略作为标签
            actual_result = int(row['actual_result'])
            best_strategy = None
            best_accuracy = -1

            for strategy in all_strategies:
                if strategy in row and int(row[strategy]) == actual_result:
                    # 策略预测正确
                    current_accuracy = strategy_performance[strategy]['overall_accuracy']
                    if current_accuracy > best_accuracy:
                        best_accuracy = current_accuracy
                        best_strategy = strategy

            if best_strategy:
                # 创建策略映射 (8个策略)
                strategy_mapping = {f'strategy_{i}': i-1 for i in range(1, 9)}
                strategy_features.append(features)
                strategy_labels.append(strategy_mapping[best_strategy])

        print(f"   策略选择数据: {len(strategy_features)} 样本")
        print(f"   特征维度: {len(features) if features else 0} (8策略 × 9特征)")

        if len(strategy_labels) > 0:
            unique, counts = np.unique(strategy_labels, return_counts=True)
            for i, count in zip(unique, counts):
                strategy_name = f'strategy_{i+1}'
                print(f"     {strategy_name}: {count} 样本")

        return np.array(strategy_features), np.array(strategy_labels)

    def _calculate_max_streak(self, predictions, actuals, target=True):
        """计算最大连胜或连败"""
        if len(predictions) == 0:
            return 0

        correct_sequence = (predictions == actuals)
        max_streak = 0
        current_streak = 0

        for is_correct in correct_sequence:
            if is_correct == target:
                current_streak += 1
                max_streak = max(max_streak, current_streak)
            else:
                current_streak = 0

        return max_streak

    def _calculate_streak(self, predictions, actuals):
        """计算连胜连败"""
        if len(predictions) == 0:
            return 0

        correct_sequence = (predictions == actuals)
        current_streak = 0
        last_result = correct_sequence[-1]

        for i in range(len(correct_sequence) - 1, -1, -1):
            if correct_sequence[i] == last_result:
                current_streak += 1
            else:
                break

        return current_streak if last_result else -current_streak

    def evaluate_all_models(self, X, y):
        """评估所有模型"""
        print(f"\n📊 评估所有模型...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        evaluation_results = {}
        
        for model_name, model in self.ml_models.models.items():
            print(f"\n   评估 {model_name}...")
            
            try:
                # 创建完整的模拟特征字典 (包含策略级特征)
                sample_features = {
                    # 基础特征
                    'consensus_ratio': 0.6,
                    'weighted_consensus': 0.65,
                    'avg_confidence': 0.55,
                    'min_confidence': 0.4,
                    'max_confidence': 0.8,
                    'confidence_std': 0.1,
                    'total_divergence': 1.0,
                    'max_divergence': 0.5,
                    'strategy_1_prediction': 1,
                    'strategy_2_prediction': 0,
                    'strategy_6_prediction': 1,
                    'strategy_1_confidence': 0.7,
                    'strategy_2_confidence': 0.5,
                    'strategy_6_confidence': 0.6,
                    'win_rate_10': 0.55,
                    'win_rate_5': 0.6,
                    'current_streak': 2,
                    'strategy_1_weight': 0.4,
                    'strategy_2_weight': 0.3,
                    'strategy_6_weight': 0.3,

                    # 策略级连胜连败特征
                    'strategy_1_current_streak': 3,
                    'strategy_1_max_win_streak': 5,
                    'strategy_1_max_loss_streak': 2,
                    'strategy_2_current_streak': -1,
                    'strategy_2_max_win_streak': 3,
                    'strategy_2_max_loss_streak': 4,
                    'strategy_6_current_streak': 1,
                    'strategy_6_max_win_streak': 4,
                    'strategy_6_max_loss_streak': 3,

                    # 策略级移动窗口胜率
                    'strategy_1_winrate_3': 0.67,
                    'strategy_1_winrate_5': 0.6,
                    'strategy_1_winrate_10': 0.55,
                    'strategy_1_winrate_20': 0.52,
                    'strategy_2_winrate_3': 0.33,
                    'strategy_2_winrate_5': 0.4,
                    'strategy_2_winrate_10': 0.45,
                    'strategy_2_winrate_20': 0.48,
                    'strategy_6_winrate_3': 0.67,
                    'strategy_6_winrate_5': 0.6,
                    'strategy_6_winrate_10': 0.5,
                    'strategy_6_winrate_20': 0.49,

                    # 策略级趋势和稳定性
                    'strategy_1_winrate_trend': 0.1,
                    'strategy_1_weighted_winrate': 0.58,
                    'strategy_2_winrate_trend': -0.05,
                    'strategy_2_weighted_winrate': 0.42,
                    'strategy_6_winrate_trend': 0.02,
                    'strategy_6_weighted_winrate': 0.51,

                    # 策略级稳定性
                    'strategy_1_stability_3': 0.8,
                    'strategy_1_stability_5': 0.75,
                    'strategy_1_stability_10': 0.7,
                    'strategy_2_stability_3': 0.6,
                    'strategy_2_stability_5': 0.65,
                    'strategy_2_stability_10': 0.68,
                    'strategy_6_stability_3': 0.75,
                    'strategy_6_stability_5': 0.72,
                    'strategy_6_stability_10': 0.69,

                    # 策略比较特征
                    'best_winrate_strategy': 'strategy_1',
                    'best_winrate_value': 0.67,
                    'winrate_range': 0.34,
                    'winrate_std': 0.15
                }
                
                # 获取预测
                prediction = model.predict(sample_features)
                
                evaluation_results[model_name] = {
                    'available': True,
                    'prediction_type': type(prediction).__name__,
                    'sample_prediction': prediction.prediction if hasattr(prediction, 'prediction') else 'N/A',
                    'sample_confidence': prediction.confidence if hasattr(prediction, 'confidence') else 'N/A'
                }
                
                print(f"     ✅ {model_name}: 可用")
                
            except Exception as e:
                evaluation_results[model_name] = {
                    'available': False,
                    'error': str(e)
                }
                print(f"     ❌ {model_name}: {str(e)}")
        
        return evaluation_results
    
    def save_training_results(self):
        """保存训练结果"""
        print(f"\n💾 保存训练结果...")
        
        try:
            # 保存模型
            self.system.save_models()
            
            # 保存训练统计
            import json
            stats_file = PROJECT_ROOT / "logs" / "training_stats.json"
            stats_file.parent.mkdir(exist_ok=True)
            
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.training_stats, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 训练结果已保存")
            
        except Exception as e:
            print(f"❌ 保存失败: {str(e)}")


def main():
    """主训练函数"""
    print("🚀 V8系统模型训练开始")
    print("=" * 60)
    
    try:
        # 创建训练器
        trainer = ModelTrainer()
        
        # 加载训练数据（使用全部数据）
        X_basic, y, df = trainer.load_training_data()
        
        # 创建增强特征
        X_enhanced = trainer.create_enhanced_features(df)
        
        # 训练XGBoost模型
        xgb_model = trainer.train_xgboost_model(X_enhanced, y)
        
        # 训练神经网络模型
        nn_model = trainer.train_neural_network(X_enhanced, y)

        # 训练策略选择模型 (核心新功能)
        strategy_model = trainer.train_strategy_selector(X_enhanced, y, df)

        # 评估所有模型
        evaluation_results = trainer.evaluate_all_models(X_enhanced, y)
        
        # 保存训练结果
        trainer.save_training_results()
        
        # 显示最终结果
        print(f"\n" + "=" * 60)
        print("🎉 模型训练完成！")
        
        print(f"\n📊 训练统计:")
        for model_name, stats in trainer.training_stats.items():
            print(f"   {model_name}:")
            for key, value in stats.items():
                print(f"     {key}: {value}")
        
        print(f"\n📋 模型评估:")
        for model_name, result in evaluation_results.items():
            if result['available']:
                print(f"   ✅ {model_name}: 可用")
            else:
                print(f"   ❌ {model_name}: {result.get('error', '不可用')}")
        
        return trainer
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    trainer = main()
