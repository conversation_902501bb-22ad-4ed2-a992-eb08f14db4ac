#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型持久化

负责模型的保存和加载
"""

import logging
import pickle
import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime


class ModelPersistence:
    """模型持久化管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 模型保存路径
        self.model_dir = Path("V8/models/saved")
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("模型持久化管理器初始化完成")
    
    def save_models(self, ml_models):
        """
        保存所有模型
        
        Args:
            ml_models: ML模型层实例
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_dir = self.model_dir / f"models_{timestamp}"
            save_dir.mkdir(exist_ok=True)
            
            saved_models = {}
            
            for name, model in ml_models.models.items():
                model_file = save_dir / f"{name}.pkl"
                
                # 保存模型
                with open(model_file, 'wb') as f:
                    pickle.dump(model, f)
                
                saved_models[name] = {
                    'file': str(model_file),
                    'is_trained': model.is_trained,
                    'training_data_size': len(model.training_data)
                }
                
                self.logger.info(f"保存模型: {name}")
            
            # 保存模型元信息
            meta_file = save_dir / "models_meta.json"
            with open(meta_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': timestamp,
                    'models': saved_models,
                    'config': self.config
                }, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"所有模型已保存到: {save_dir}")
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {str(e)}")
            raise
    
    def load_models(self, ml_models, timestamp: Optional[str] = None):
        """
        加载模型
        
        Args:
            ml_models: ML模型层实例
            timestamp: 指定时间戳，如果为None则加载最新的
        """
        try:
            if timestamp:
                load_dir = self.model_dir / f"models_{timestamp}"
            else:
                # 找到最新的模型目录
                model_dirs = [d for d in self.model_dir.iterdir() if d.is_dir() and d.name.startswith("models_")]
                if not model_dirs:
                    self.logger.warning("未找到保存的模型")
                    return False
                
                load_dir = max(model_dirs, key=lambda x: x.name)
            
            if not load_dir.exists():
                self.logger.warning(f"模型目录不存在: {load_dir}")
                return False
            
            # 加载模型元信息
            meta_file = load_dir / "models_meta.json"
            if meta_file.exists():
                with open(meta_file, 'r', encoding='utf-8') as f:
                    meta_info = json.load(f)
                self.logger.info(f"加载模型元信息: {meta_info['timestamp']}")
            
            # 加载各个模型
            loaded_count = 0
            for name in ml_models.models.keys():
                model_file = load_dir / f"{name}.pkl"
                
                if model_file.exists():
                    with open(model_file, 'rb') as f:
                        loaded_model = pickle.load(f)
                    
                    ml_models.models[name] = loaded_model
                    loaded_count += 1
                    self.logger.info(f"加载模型: {name}")
                else:
                    self.logger.warning(f"模型文件不存在: {model_file}")
            
            self.logger.info(f"成功加载 {loaded_count} 个模型")
            return loaded_count > 0
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {str(e)}")
            return False
    
    def has_saved_models(self) -> bool:
        """检查是否有保存的模型"""
        model_dirs = [d for d in self.model_dir.iterdir() if d.is_dir() and d.name.startswith("models_")]
        return len(model_dirs) > 0
    
    def list_saved_models(self) -> List[Dict[str, Any]]:
        """列出所有保存的模型"""
        models = []
        
        for model_dir in self.model_dir.iterdir():
            if model_dir.is_dir() and model_dir.name.startswith("models_"):
                meta_file = model_dir / "models_meta.json"
                
                if meta_file.exists():
                    try:
                        with open(meta_file, 'r', encoding='utf-8') as f:
                            meta_info = json.load(f)
                        
                        models.append({
                            'timestamp': meta_info['timestamp'],
                            'path': str(model_dir),
                            'models': list(meta_info['models'].keys()),
                            'size': sum(f.stat().st_size for f in model_dir.iterdir() if f.is_file())
                        })
                    except Exception as e:
                        self.logger.warning(f"读取模型元信息失败: {e}")
        
        return sorted(models, key=lambda x: x['timestamp'], reverse=True)
    
    def cleanup_old_models(self, keep_count: int = 5):
        """清理旧模型，只保留最新的几个"""
        try:
            models = self.list_saved_models()
            
            if len(models) <= keep_count:
                return
            
            # 删除旧模型
            for model in models[keep_count:]:
                model_path = Path(model['path'])
                if model_path.exists():
                    import shutil
                    shutil.rmtree(model_path)
                    self.logger.info(f"删除旧模型: {model['timestamp']}")
            
            self.logger.info(f"清理完成，保留了最新的 {keep_count} 个模型")
            
        except Exception as e:
            self.logger.error(f"模型清理失败: {str(e)}")
    
    def export_model_config(self, output_file: str):
        """导出模型配置"""
        try:
            config_data = {
                'system_config': self.config,
                'export_time': datetime.now().isoformat(),
                'saved_models': self.list_saved_models()
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"模型配置已导出到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"配置导出失败: {str(e)}")
            raise
