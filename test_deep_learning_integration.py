#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试深度学习模型集成

验证LSTM、GRU、Transformer等深度学习模型的集成效果
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from main import SimpleFusionV8
    from core.deep_learning_models import DeepLearningModelLayer
    print("✅ 成功导入深度学习模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_deep_learning_models():
    """测试深度学习模型"""
    print("\n🧪 测试深度学习模型...")
    
    # 创建深度学习模型层
    config = {
        'deep_learning': {
            'sequence_length': 10,
            'feature_size': 20,
            'lstm': {'enabled': True, 'hidden_size': 32, 'num_layers': 2},
            'gru': {'enabled': True, 'hidden_size': 32, 'num_layers': 2},
            'transformer': {'enabled': True, 'd_model': 32, 'nhead': 4}
        }
    }
    
    dl_layer = DeepLearningModelLayer(config)
    
    # 获取模型信息
    model_info = dl_layer.get_model_info()
    print(f"📊 深度学习模型信息:")
    print(f"   总模型数: {model_info['total_models']}")
    print(f"   PyTorch可用: {model_info['pytorch_available']}")
    print(f"   设备: {model_info['device']}")
    print(f"   序列长度: {model_info['sequence_length']}")
    print(f"   特征维度: {model_info['feature_size']}")
    
    for model_name, info in model_info['models'].items():
        print(f"   {model_name}: {info['type']}, 参数数量: {info['parameters']}")
    
    # 创建模拟特征历史
    features_history = []
    for i in range(15):
        features = {
            'consensus_ratio': 0.6 + np.random.random() * 0.3,
            'weighted_consensus': 0.5 + np.random.random() * 0.4,
            'avg_confidence': 0.4 + np.random.random() * 0.4,
            'min_confidence': 0.3 + np.random.random() * 0.3,
            'max_confidence': 0.6 + np.random.random() * 0.3,
            'confidence_std': np.random.random() * 0.2,
            'total_divergence': np.random.random() * 2,
            'max_divergence': np.random.random() * 1,
            'strategy_1_prediction': np.random.choice([0, 1]),
            'strategy_2_prediction': np.random.choice([0, 1]),
            'strategy_6_prediction': np.random.choice([0, 1]),
            'strategy_1_confidence': 0.4 + np.random.random() * 0.4,
            'strategy_2_confidence': 0.4 + np.random.random() * 0.4,
            'strategy_6_confidence': 0.4 + np.random.random() * 0.4,
            'win_rate_10': 0.3 + np.random.random() * 0.4,
            'win_rate_5': 0.3 + np.random.random() * 0.4,
            'current_streak': np.random.randint(-5, 6),
            'strategy_1_weight': 0.2 + np.random.random() * 0.3,
            'strategy_2_weight': 0.2 + np.random.random() * 0.3,
            'strategy_6_weight': 0.2 + np.random.random() * 0.3
        }
        features_history.append(features)
    
    # 测试各个模型
    model_results = {}
    
    for model_name in dl_layer.models.keys():
        print(f"\n   测试 {model_name}...")
        try:
            prediction = dl_layer.predict(model_name, features_history)
            
            model_results[model_name] = {
                'success': True,
                'prediction': prediction.prediction,
                'confidence': prediction.confidence,
                'reasoning': prediction.reasoning,
                'has_attention': prediction.attention_weights is not None
            }
            
            print(f"     ✅ 预测: {prediction.prediction:.4f}")
            print(f"     ✅ 置信度: {prediction.confidence:.4f}")
            print(f"     ✅ 注意力权重: {'有' if prediction.attention_weights else '无'}")
            
        except Exception as e:
            model_results[model_name] = {
                'success': False,
                'error': str(e)
            }
            print(f"     ❌ 失败: {str(e)}")
    
    # 测试集成预测
    print(f"\n   测试集成预测...")
    try:
        ensemble_pred = dl_layer.get_ensemble_prediction(features_history)
        
        print(f"     ✅ 集成预测: {ensemble_pred.prediction:.4f}")
        print(f"     ✅ 集成置信度: {ensemble_pred.confidence:.4f}")
        print(f"     ✅ 集成理由: {ensemble_pred.reasoning}")
        
        model_results['ensemble'] = {
            'success': True,
            'prediction': ensemble_pred.prediction,
            'confidence': ensemble_pred.confidence
        }
        
    except Exception as e:
        print(f"     ❌ 集成预测失败: {str(e)}")
        model_results['ensemble'] = {'success': False, 'error': str(e)}
    
    return model_results


def test_v8_system_with_deep_learning():
    """测试V8系统与深度学习的集成"""
    print("\n🧪 测试V8系统深度学习集成...")
    
    # 创建V8系统
    system = SimpleFusionV8()
    system.initialize()
    
    print(f"✅ V8系统初始化完成")
    
    # 获取深度学习模型信息
    dl_info = system.deep_learning_models.get_model_info()
    print(f"📊 集成的深度学习模型: {dl_info['total_models']} 个")
    
    # 进行多次决策测试
    results = []
    
    for i in range(10):
        try:
            # 模拟外部数据
            external_data = {
                'decision_id': f"dl_test_{i}",
                'current_boot': {'boot_id': 100 + i, 'position': i % 40},
                'timestamp': time.time()
            }
            
            # 处理决策
            decision = system.process_decision(external_data)
            
            # 模拟实际结果
            actual_result = np.random.choice([0, 1])
            
            # 更新反馈
            system.update_feedback(decision.decision_id, actual_result)
            
            # 记录结果
            is_correct = (decision.prediction == actual_result)
            results.append({
                'decision_id': decision.decision_id,
                'prediction': decision.prediction,
                'actual': actual_result,
                'correct': is_correct,
                'confidence': decision.confidence,
                'reasoning': decision.reasoning
            })
            
            print(f"   决策 {i+1}: 预测={decision.prediction}, 实际={actual_result}, "
                  f"正确={is_correct}, 置信度={decision.confidence:.3f}")
            
        except Exception as e:
            print(f"   ❌ 决策 {i+1} 失败: {str(e)}")
    
    # 计算统计
    if results:
        accuracy = sum(r['correct'] for r in results) / len(results)
        avg_confidence = sum(r['confidence'] for r in results) / len(results)
        
        print(f"\n📊 深度学习集成测试结果:")
        print(f"   总决策数: {len(results)}")
        print(f"   准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        print(f"   平均置信度: {avg_confidence:.4f}")
        
        # 显示系统状态
        status = system.get_system_status()
        print(f"   系统决策数: {status['decision_count']}")
        print(f"   系统健康: {status['system_health']['status']}")
    
    return results


def test_sequence_data_processing():
    """测试序列数据处理"""
    print("\n🧪 测试序列数据处理...")
    
    config = {
        'deep_learning': {
            'sequence_length': 8,
            'feature_size': 20
        }
    }
    
    dl_layer = DeepLearningModelLayer(config)
    
    # 测试不同长度的特征历史
    test_cases = [
        {'length': 5, 'description': '历史不足'},
        {'length': 8, 'description': '历史刚好'},
        {'length': 12, 'description': '历史过多'}
    ]
    
    for case in test_cases:
        print(f"\n   测试 {case['description']} (长度: {case['length']})...")
        
        # 创建特征历史
        features_history = []
        for i in range(case['length']):
            features = {f'feature_{j}': np.random.random() for j in range(20)}
            features_history.append(features)
        
        # 处理序列数据
        try:
            sequence_data = dl_layer.prepare_sequence_data(features_history)
            
            print(f"     ✅ 序列形状: {sequence_data.shape}")
            print(f"     ✅ 期望形状: ({dl_layer.sequence_length}, {dl_layer.feature_size})")
            
            # 验证形状
            expected_shape = (dl_layer.sequence_length, dl_layer.feature_size)
            if sequence_data.shape == expected_shape:
                print(f"     ✅ 形状正确")
            else:
                print(f"     ❌ 形状错误")
                
        except Exception as e:
            print(f"     ❌ 处理失败: {str(e)}")


def main():
    """主测试函数"""
    print("🚀 深度学习模型集成测试")
    print("=" * 60)
    
    # 1. 测试深度学习模型
    model_results = test_deep_learning_models()
    
    # 2. 测试V8系统集成
    v8_results = test_v8_system_with_deep_learning()
    
    # 3. 测试序列数据处理
    test_sequence_data_processing()
    
    print("\n" + "=" * 60)
    print("🎉 深度学习集成测试完成！")
    
    # 总结
    print(f"\n📋 测试总结:")
    
    # 模型可用性
    successful_models = sum(1 for r in model_results.values() if r.get('success', False))
    total_models = len(model_results)
    print(f"   ✅ 模型可用性: {successful_models}/{total_models}")
    
    # V8系统集成
    if v8_results:
        accuracy = sum(r['correct'] for r in v8_results) / len(v8_results)
        print(f"   ✅ V8集成准确率: {accuracy:.4f}")
    
    # 深度学习特色功能
    attention_models = sum(1 for r in model_results.values() 
                          if r.get('success') and r.get('has_attention'))
    print(f"   ✅ 注意力机制模型: {attention_models} 个")
    
    return model_results, v8_results


if __name__ == "__main__":
    model_results, v8_results = main()
