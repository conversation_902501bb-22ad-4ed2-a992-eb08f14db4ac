#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SimpleFusion V8 基础测试

测试V8系统的基础架构和核心功能
"""

import sys
import os
import time
import uuid
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from main import SimpleFusionV8
    print("✅ 成功导入 SimpleFusionV8")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_system_initialization():
    """测试系统初始化"""
    print("\n🧪 测试系统初始化...")
    
    try:
        # 创建系统实例
        system = SimpleFusionV8()
        print("✅ 系统实例创建成功")
        
        # 初始化系统
        system.initialize()
        print("✅ 系统初始化成功")
        
        # 检查系统状态
        status = system.get_system_status()
        print(f"📊 系统状态: {status}")
        
        return system
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def test_basic_decision_flow(system):
    """测试基础决策流程"""
    print("\n🧪 测试基础决策流程...")
    
    try:
        # 模拟外部数据
        external_data = {
            'decision_id': f"test_{uuid.uuid4().hex[:8]}",
            'current_boot': {'boot_id': 1, 'position': 10},
            'timestamp': time.time()
        }
        
        # 处理决策
        start_time = time.time()
        decision = system.process_decision(external_data)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # 毫秒
        
        print(f"✅ 决策处理成功")
        print(f"📊 决策结果:")
        print(f"   决策ID: {decision.decision_id}")
        print(f"   预测: {decision.prediction}")
        print(f"   置信度: {decision.confidence:.4f}")
        print(f"   风险等级: {decision.risk_level}")
        print(f"   是否执行: {decision.should_act}")
        print(f"   响应时间: {response_time:.2f}ms")
        print(f"   决策理由: {decision.reasoning}")
        
        return decision
        
    except Exception as e:
        print(f"❌ 决策处理失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def test_feedback_mechanism(system, decision):
    """测试反馈机制"""
    print("\n🧪 测试反馈机制...")
    
    try:
        # 模拟实际结果
        actual_result = 1  # 假设实际结果为1
        
        # 更新反馈
        system.update_feedback(decision.decision_id, actual_result)
        print(f"✅ 反馈更新成功")
        
        # 检查性能指标
        metrics = system.metrics_collector.get_current_metrics()
        print(f"📊 当前指标:")
        print(f"   总决策数: {metrics['total_decisions']}")
        print(f"   正确决策数: {metrics['correct_decisions']}")
        print(f"   准确率: {metrics['accuracy']:.4f}")
        print(f"   连续错误: {metrics['consecutive_errors']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 反馈更新失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_multiple_decisions(system, count=5):
    """测试多次决策"""
    print(f"\n🧪 测试多次决策 ({count}次)...")
    
    decisions = []
    
    try:
        for i in range(count):
            # 模拟外部数据
            external_data = {
                'decision_id': f"test_multi_{i}_{uuid.uuid4().hex[:8]}",
                'current_boot': {'boot_id': 1, 'position': 10 + i},
                'timestamp': time.time()
            }
            
            # 处理决策
            decision = system.process_decision(external_data)
            decisions.append(decision)
            
            # 模拟反馈 (随机结果)
            import random
            actual_result = random.choice([0, 1])
            system.update_feedback(decision.decision_id, actual_result)
            
            print(f"   决策 {i+1}: 预测={decision.prediction}, 置信度={decision.confidence:.3f}, 实际={actual_result}")
            
            # 短暂延迟
            time.sleep(0.1)
        
        print(f"✅ 完成 {count} 次决策")
        
        # 显示最终统计
        metrics = system.metrics_collector.get_current_metrics()
        print(f"📊 最终统计:")
        print(f"   总决策数: {metrics['total_decisions']}")
        print(f"   准确率: {metrics['accuracy']:.4f}")
        print(f"   平均置信度: {metrics['avg_confidence']:.4f}")
        print(f"   最大连错: {metrics['max_consecutive_errors']}")
        
        return decisions
        
    except Exception as e:
        print(f"❌ 多次决策测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []


def test_strategy_performance(system):
    """测试策略性能"""
    print("\n🧪 测试策略性能...")
    
    try:
        # 获取策略性能摘要
        performance = system.base_strategies.get_performance_summary()
        
        print(f"📊 策略性能摘要:")
        for strategy_name, stats in performance.items():
            print(f"   {strategy_name}:")
            print(f"     总决策数: {stats['total_decisions']}")
            print(f"     准确率: {stats['accuracy']:.4f}")
            print(f"     近期准确率: {stats['recent_accuracy']:.4f}")
            print(f"     当前权重: {stats['current_weight']:.3f}")
            print(f"     连续错误: {stats['consecutive_errors']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略性能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_model_status(system):
    """测试模型状态"""
    print("\n🧪 测试模型状态...")
    
    try:
        # 获取模型状态
        model_status = system.ml_models.get_model_status()
        
        print(f"📊 模型状态:")
        for model_name, status in model_status.items():
            print(f"   {model_name}:")
            print(f"     已训练: {status['is_trained']}")
            print(f"     训练数据量: {status['training_data_size']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型状态测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 SimpleFusion V8 基础测试开始")
    print("=" * 60)
    
    # 1. 测试系统初始化
    system = test_system_initialization()
    if not system:
        print("❌ 系统初始化失败，终止测试")
        return
    
    # 2. 测试基础决策流程
    decision = test_basic_decision_flow(system)
    if not decision:
        print("❌ 基础决策流程失败，终止测试")
        return
    
    # 3. 测试反馈机制
    feedback_success = test_feedback_mechanism(system, decision)
    if not feedback_success:
        print("⚠️ 反馈机制测试失败，但继续其他测试")
    
    # 4. 测试多次决策
    decisions = test_multiple_decisions(system, 10)
    if not decisions:
        print("⚠️ 多次决策测试失败，但继续其他测试")
    
    # 5. 测试策略性能
    strategy_success = test_strategy_performance(system)
    if not strategy_success:
        print("⚠️ 策略性能测试失败，但继续其他测试")
    
    # 6. 测试模型状态
    model_success = test_model_status(system)
    if not model_success:
        print("⚠️ 模型状态测试失败")
    
    # 生成性能报告
    try:
        report = system.metrics_collector.generate_performance_report()
        print("\n📋 性能报告:")
        print(report)
    except Exception as e:
        print(f"⚠️ 性能报告生成失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 SimpleFusion V8 基础测试完成！")
    
    # 返回系统实例供进一步测试
    return system


if __name__ == "__main__":
    system = main()
