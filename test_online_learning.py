#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试在线学习机制

验证增量学习、概念漂移检测等在线学习功能
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from core.online_learning import OnlineLearningManager, IncrementalLearner, ConceptDriftDetector
    from main import SimpleFusionV8
    print("✅ 成功导入在线学习模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def create_mock_data(num_samples: int = 100, drift_point: int = None):
    """创建模拟数据，可选择性地包含概念漂移"""
    data = []
    
    for i in range(num_samples):
        # 基础特征
        features = {
            'consensus_ratio': 0.4 + np.random.random() * 0.4,
            'weighted_consensus': 0.3 + np.random.random() * 0.5,
            'avg_confidence': 0.4 + np.random.random() * 0.4,
            'min_confidence': 0.3 + np.random.random() * 0.3,
            'max_confidence': 0.6 + np.random.random() * 0.3,
            'confidence_std': np.random.random() * 0.2,
            'total_divergence': np.random.random() * 2,
            'max_divergence': np.random.random() * 1,
            'strategy_1_prediction': np.random.choice([0, 1]),
            'strategy_2_prediction': np.random.choice([0, 1]),
            'strategy_6_prediction': np.random.choice([0, 1]),
            'strategy_1_confidence': 0.4 + np.random.random() * 0.4,
            'strategy_2_confidence': 0.4 + np.random.random() * 0.4,
            'strategy_6_confidence': 0.4 + np.random.random() * 0.4,
            'win_rate_10': 0.3 + np.random.random() * 0.4,
            'win_rate_5': 0.3 + np.random.random() * 0.4,
            'current_streak': np.random.randint(-5, 6),
            'strategy_1_weight': 0.2 + np.random.random() * 0.3,
            'strategy_2_weight': 0.2 + np.random.random() * 0.3,
            'strategy_6_weight': 0.2 + np.random.random() * 0.3
        }
        
        # 模拟模型预测
        model_predictions = {
            'voting': 0.3 + np.random.random() * 0.4,
            'xgboost': 0.2 + np.random.random() * 0.6,
            'neural_network': 0.25 + np.random.random() * 0.5,
            'meta_learner': 0.35 + np.random.random() * 0.3
        }
        
        # 生成标签
        if drift_point and i >= drift_point:
            # 概念漂移后：反转标签生成逻辑
            avg_pred = sum(model_predictions.values()) / len(model_predictions)
            label = 1 if (avg_pred + np.random.normal(0, 0.2)) < 0.5 else 0  # 反转
        else:
            # 正常情况
            avg_pred = sum(model_predictions.values()) / len(model_predictions)
            label = 1 if (avg_pred + np.random.normal(0, 0.1)) > 0.5 else 0
        
        data.append({
            'features': features,
            'model_predictions': model_predictions,
            'actual_result': label
        })
    
    return data


def test_incremental_learner():
    """测试增量学习器"""
    print("\n🧪 测试增量学习器...")
    
    # 创建配置
    config = {
        'incremental': {
            'model_type': 'sgd',
            'learning_rate': 0.01,
            'performance_window': 50,
            'batch_size': 10
        }
    }
    
    # 创建增量学习器
    learner = IncrementalLearner('test_model', config)
    
    print(f"📊 学习器初始化: {learner.model_name}")
    
    # 创建训练数据
    training_data = create_mock_data(100)
    
    # 增量学习过程
    accuracies = []
    
    for i, data_point in enumerate(training_data):
        # 增量更新
        update_record = learner.partial_fit(data_point['features'], data_point['actual_result'])
        
        # 记录准确率
        accuracies.append(update_record.accuracy_after)
        
        # 显示进度
        if i % 20 == 0:
            print(f"   更新 {i+1}: 准确率 {update_record.accuracy_before:.3f} -> {update_record.accuracy_after:.3f}")
    
    # 测试预测
    test_data = create_mock_data(10)
    correct_predictions = 0
    
    for data_point in test_data:
        prediction = learner.predict(data_point['features'])
        predicted_class = 1 if prediction >= 0.5 else 0
        actual_class = data_point['actual_result']
        
        if predicted_class == actual_class:
            correct_predictions += 1
    
    test_accuracy = correct_predictions / len(test_data)
    
    # 获取学习统计
    stats = learner.get_learning_stats()
    
    print(f"✅ 增量学习测试完成:")
    print(f"   最终训练准确率: {accuracies[-1]:.4f}")
    print(f"   测试准确率: {test_accuracy:.4f}")
    print(f"   总更新次数: {stats['total_updates']}")
    print(f"   模型已训练: {stats['is_fitted']}")
    
    return learner, accuracies


def test_concept_drift_detector():
    """测试概念漂移检测器"""
    print("\n🧪 测试概念漂移检测器...")
    
    # 创建配置
    config = {
        'drift_detection': {
            'window_size': 50,
            'drift_threshold': 0.1,
            'warning_threshold': 0.05
        }
    }
    
    # 创建概念漂移检测器
    drift_detector = ConceptDriftDetector(config)
    
    print(f"📊 漂移检测器初始化完成")
    
    # 创建包含概念漂移的数据
    data_with_drift = create_mock_data(200, drift_point=100)
    
    drift_detections = []
    
    for i, data_point in enumerate(data_with_drift):
        # 模拟预测
        avg_prediction = sum(data_point['model_predictions'].values()) / len(data_point['model_predictions'])
        
        # 添加性能数据点
        drift_detector.add_performance_point(avg_prediction, data_point['actual_result'])
        
        # 检测漂移
        if i >= 50:  # 有足够数据后开始检测
            detection = drift_detector.detect_drift()
            drift_detections.append({
                'step': i,
                'detection': detection
            })
            
            # 显示重要的检测结果
            if detection.drift_detected or detection.drift_type == 'warning':
                print(f"   步骤 {i}: {detection.drift_type}, 得分: {detection.drift_score:.4f}, "
                      f"置信度: {detection.confidence:.3f}")
    
    # 分析检测结果
    drift_detected_count = sum(1 for d in drift_detections if d['detection'].drift_detected)
    warning_count = sum(1 for d in drift_detections if d['detection'].drift_type == 'warning')
    
    print(f"✅ 概念漂移检测测试完成:")
    print(f"   总检测次数: {len(drift_detections)}")
    print(f"   检测到漂移: {drift_detected_count} 次")
    print(f"   警告次数: {warning_count} 次")
    print(f"   漂移检测率: {drift_detected_count/len(drift_detections)*100:.1f}%")
    
    return drift_detector, drift_detections


def test_online_learning_manager():
    """测试在线学习管理器"""
    print("\n🧪 测试在线学习管理器...")
    
    # 创建配置
    config = {
        'online_learning': {
            'batch_size': 20,
            'batch_interval': 10,  # 10秒用于测试
            'voting': {'enabled': True},
            'xgboost': {'enabled': True},
            'neural_network': {'enabled': True},
            'meta_learner': {'enabled': True}
        },
        'drift_detection': {
            'window_size': 30,
            'drift_threshold': 0.08,
            'warning_threshold': 0.04
        }
    }
    
    # 创建在线学习管理器
    manager = OnlineLearningManager(config)
    
    print(f"📊 在线学习管理器初始化: {len(manager.learners)} 个学习器")
    
    # 创建训练数据
    training_data = create_mock_data(100, drift_point=60)
    
    # 模拟在线学习过程
    for i, data_point in enumerate(training_data):
        # 添加反馈
        manager.add_feedback(
            data_point['model_predictions'],
            data_point['features'],
            data_point['actual_result']
        )
        
        # 定期显示状态
        if i % 25 == 0:
            status = manager.get_learning_status()
            print(f"   步骤 {i}: 队列大小={status['queue_size']}, "
                  f"批量更新={status['batch_updates_count']}")
            
            # 显示漂移检测状态
            drift_status = status['drift_detection']
            if drift_status['drift_detected']:
                print(f"     ⚠️ 检测到漂移: {drift_status['drift_type']}")
    
    # 等待一段时间让批量更新完成
    time.sleep(2)
    
    # 测试在线预测
    test_data = create_mock_data(10)
    online_predictions = []
    
    for data_point in test_data:
        predictions = manager.get_online_predictions(data_point['features'])
        online_predictions.append(predictions)
    
    # 获取最终状态
    final_status = manager.get_learning_status()
    
    print(f"✅ 在线学习管理器测试完成:")
    print(f"   学习器数量: {final_status['total_learners']}")
    print(f"   总批量更新: {final_status['batch_updates_count']}")
    print(f"   漂移检测: {final_status['drift_detection']['drift_type']}")
    
    # 显示学习器统计
    for name, stats in final_status['learner_stats'].items():
        print(f"   {name}: 准确率={stats['current_accuracy']:.3f}, 更新={stats['total_updates']}")
    
    # 停止在线学习
    manager.stop_online_learning()
    
    return manager, online_predictions


def test_v8_integration():
    """测试V8系统集成"""
    print("\n🧪 测试V8系统在线学习集成...")
    
    # 创建V8系统
    system = SimpleFusionV8()
    system.initialize()
    
    print("✅ V8系统初始化完成")
    
    # 进行多次决策和反馈
    results = []
    
    for i in range(20):
        try:
            # 模拟外部数据
            external_data = {
                'decision_id': f"online_test_{i}",
                'current_boot': {'boot_id': 300 + i, 'position': i % 40}
            }
            
            # 处理决策
            decision = system.process_decision(external_data)
            
            # 模拟实际结果
            actual_result = np.random.choice([0, 1])
            
            # 更新反馈（这会触发在线学习）
            system.update_feedback(decision.decision_id, actual_result)
            
            results.append({
                'decision_id': decision.decision_id,
                'prediction': decision.prediction,
                'actual': actual_result,
                'correct': decision.prediction == actual_result
            })
            
            # 定期显示状态
            if i % 10 == 0:
                status = system.get_system_status()
                if 'online_learning' in status:
                    ol_status = status['online_learning']
                    print(f"   决策 {i+1}: 在线学习器={ol_status['total_learners']}, "
                          f"队列={ol_status['queue_size']}")
            
        except Exception as e:
            print(f"   ❌ 决策 {i+1} 失败: {str(e)}")
    
    # 获取最终系统状态
    final_status = system.get_system_status()
    
    print(f"✅ V8在线学习集成测试完成:")
    print(f"   总决策数: {len(results)}")
    
    if results:
        accuracy = sum(r['correct'] for r in results) / len(results)
        print(f"   准确率: {accuracy:.4f}")
    
    if 'online_learning' in final_status:
        ol_status = final_status['online_learning']
        print(f"   在线学习状态: {'运行中' if ol_status['is_running'] else '已停止'}")
        print(f"   概念漂移: {ol_status['drift_detection']['drift_type']}")
    
    return system, results


def main():
    """主测试函数"""
    print("🚀 在线学习机制测试")
    print("=" * 60)
    
    # 1. 测试增量学习器
    learner, accuracies = test_incremental_learner()
    
    # 2. 测试概念漂移检测器
    drift_detector, drift_detections = test_concept_drift_detector()
    
    # 3. 测试在线学习管理器
    manager, online_predictions = test_online_learning_manager()
    
    # 4. 测试V8系统集成
    system, v8_results = test_v8_integration()
    
    print("\n" + "=" * 60)
    print("🎉 在线学习机制测试完成！")
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"   ✅ 增量学习器: 最终准确率 {accuracies[-1]:.4f}")
    
    drift_count = sum(1 for d in drift_detections if d['detection'].drift_detected)
    print(f"   ✅ 概念漂移检测: 检测到 {drift_count} 次漂移")
    
    print(f"   ✅ 在线学习管理器: {len(manager.learners)} 个学习器")
    
    if v8_results:
        v8_accuracy = sum(r['correct'] for r in v8_results) / len(v8_results)
        print(f"   ✅ V8系统集成: 准确率 {v8_accuracy:.4f}")
    
    return {
        'learner': learner,
        'drift_detector': drift_detector,
        'manager': manager,
        'system': system,
        'results': {
            'accuracies': accuracies,
            'drift_detections': drift_detections,
            'online_predictions': online_predictions,
            'v8_results': v8_results
        }
    }


if __name__ == "__main__":
    test_results = main()
