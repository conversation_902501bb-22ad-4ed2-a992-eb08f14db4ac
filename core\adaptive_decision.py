#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应决策层 (Layer 4)

实现智能决策机制：
1. 置信度评估：基于多维度信息评估决策置信度
2. 风险控制：动态阈值调整、连错预测、止损机制
3. 在线学习：实时参数更新和环境适应
4. 异常检测：识别和处理异常模式
"""

import logging
import numpy as np
import time
from typing import Dict, List, Any, Optional, Tuple
from collections import deque
from dataclasses import dataclass


@dataclass
class FinalDecision:
    """最终决策结果"""
    decision_id: str
    prediction: int           # 最终预测 (0 或 1)
    confidence: float         # 综合置信度
    risk_level: str          # 风险等级: low, medium, high
    should_act: bool         # 是否应该执行决策
    reasoning: str           # 决策理由
    strategy_outputs: Dict   # 策略输出
    ml_predictions: Dict     # ML预测
    features: Dict           # 特征值
    timestamp: float         # 时间戳
    explanation: Any = None  # 决策解释 (可选)


class ConfidenceEvaluator:
    """置信度评估器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('adaptive_decision', {}).get('confidence', {})
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def evaluate_confidence(self, strategy_outputs: Dict, features: Dict, ml_predictions: Dict) -> float:
        """
        评估综合置信度
        
        Args:
            strategy_outputs: 策略输出
            features: 特征
            ml_predictions: ML预测
            
        Returns:
            综合置信度 (0-1)
        """
        confidence_factors = []
        
        # 1. 策略一致性置信度
        strategy_confidence = self._evaluate_strategy_consensus(strategy_outputs, features)
        confidence_factors.append(strategy_confidence)
        
        # 2. 历史准确率置信度
        historical_confidence = self._evaluate_historical_accuracy(features)
        confidence_factors.append(historical_confidence)
        
        # 3. 模型不确定性置信度
        model_confidence = self._evaluate_model_uncertainty(ml_predictions)
        confidence_factors.append(model_confidence)
        
        # 加权平均
        weights = self.config.get('weights', [0.4, 0.4, 0.2])
        if len(weights) != len(confidence_factors):
            weights = [1.0 / len(confidence_factors)] * len(confidence_factors)
        
        final_confidence = sum(w * c for w, c in zip(weights, confidence_factors))
        
        self.logger.debug(f"置信度评估: 策略={strategy_confidence:.3f}, 历史={historical_confidence:.3f}, "
                         f"模型={model_confidence:.3f}, 综合={final_confidence:.3f}")
        
        return max(0.0, min(1.0, final_confidence))
    
    def _evaluate_strategy_consensus(self, strategy_outputs: Dict, features: Dict) -> float:
        """评估策略一致性置信度"""
        consensus_ratio = features.get('consensus_ratio', 0.5)
        avg_confidence = features.get('avg_confidence', 0.5)
        
        # 一致性越高，置信度越高
        consensus_strength = abs(consensus_ratio - 0.5) * 2  # 0-1
        
        # 结合策略自身的置信度
        return (consensus_strength + avg_confidence) / 2
    
    def _evaluate_historical_accuracy(self, features: Dict) -> float:
        """评估历史准确率置信度"""
        # 基于近期胜率
        recent_win_rate = features.get('win_rate_10', 0.5)
        
        # 转换为置信度 (50%为基准)
        if recent_win_rate >= 0.5:
            return min(1.0, (recent_win_rate - 0.5) * 4 + 0.5)
        else:
            return max(0.0, recent_win_rate * 2)
    
    def _evaluate_model_uncertainty(self, ml_predictions: Dict) -> float:
        """评估模型不确定性置信度"""
        if not ml_predictions:
            return 0.5
        
        # 计算模型预测的一致性
        predictions = [pred.prediction for pred in ml_predictions.values()]
        confidences = [pred.confidence for pred in ml_predictions.values()]
        
        if not predictions:
            return 0.5
        
        # 模型一致性
        prediction_std = np.std(predictions)
        consistency = max(0.0, 1.0 - prediction_std * 2)
        
        # 平均置信度
        avg_model_confidence = np.mean(confidences)
        
        return (consistency + avg_model_confidence) / 2


class RiskController:
    """风险控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('adaptive_decision', {}).get('risk_control', {})
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 风险状态
        self.consecutive_errors = 0
        self.error_history = deque(maxlen=100)
        self.last_error_time = 0
        self.cooldown_until = 0
        
    def assess_risk(self, confidence: float, features: Dict, decision_count: int) -> str:
        """
        评估风险等级
        
        Returns:
            风险等级: 'low', 'medium', 'high'
        """
        risk_factors = []
        
        # 1. 置信度风险
        if confidence < 0.3:
            risk_factors.append('low_confidence')
        elif confidence < 0.5:
            risk_factors.append('medium_confidence')
        
        # 2. 连错风险
        current_streak = abs(features.get('current_streak', 0))
        if current_streak < 0:  # 连败
            if abs(current_streak) >= 5:
                risk_factors.append('high_error_streak')
            elif abs(current_streak) >= 3:
                risk_factors.append('medium_error_streak')
        
        # 3. 波动性风险
        volatility = features.get('volatility_10', 0)
        if volatility > 0.4:
            risk_factors.append('high_volatility')
        
        # 4. 分歧风险
        total_divergence = features.get('total_divergence', 0)
        if total_divergence > 2:
            risk_factors.append('high_divergence')
        
        # 综合评估
        if len(risk_factors) >= 3 or 'high_error_streak' in risk_factors:
            return 'high'
        elif len(risk_factors) >= 1:
            return 'medium'
        else:
            return 'low'
    
    def should_act(self, confidence: float, risk_level: str, decision_count: int) -> bool:
        """
        判断是否应该执行决策
        
        Returns:
            是否应该执行决策
        """
        current_time = time.time()
        
        # 检查冷却期
        if current_time < self.cooldown_until:
            self.logger.info(f"处于冷却期，跳过决策")
            return False
        
        # 基于风险等级和置信度决策
        min_confidence = self.config.get('min_confidence', 0.3)
        
        if risk_level == 'high':
            # 高风险时需要更高的置信度
            return confidence >= min_confidence + 0.2
        elif risk_level == 'medium':
            # 中等风险时需要标准置信度
            return confidence >= min_confidence + 0.1
        else:
            # 低风险时使用基础置信度
            return confidence >= min_confidence
    
    def update_error_feedback(self, is_correct: bool):
        """更新错误反馈"""
        current_time = time.time()
        
        if is_correct:
            self.consecutive_errors = 0
        else:
            self.consecutive_errors += 1
            self.last_error_time = current_time
            
            # 检查是否需要进入冷却期
            max_errors = self.config.get('max_consecutive_errors', 8)
            if self.consecutive_errors >= max_errors:
                cooldown_period = self.config.get('cooldown_period', 5) * 60  # 5分钟
                self.cooldown_until = current_time + cooldown_period
                self.logger.warning(f"连续错误达到{self.consecutive_errors}次，进入{cooldown_period/60}分钟冷却期")
        
        self.error_history.append({
            'is_correct': is_correct,
            'timestamp': current_time
        })


class OnlineLearner:
    """在线学习器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('adaptive_decision', {}).get('online_learning', {})
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 学习参数
        self.learning_rate = self.config.get('learning_rate', 0.01)
        self.adaptation_speed = self.config.get('adaptation_speed', 'medium')
        
        # 性能历史
        self.performance_history = deque(maxlen=self.config.get('memory_size', 1000))
        
    def adapt_parameters(self):
        """自适应参数调整"""
        if len(self.performance_history) < 50:
            return
        
        # 分析近期性能
        recent_performance = list(self.performance_history)[-50:]
        accuracy = sum(1 for p in recent_performance if p['is_correct']) / len(recent_performance)
        
        # 根据性能调整参数
        if accuracy < 0.45:
            # 性能下降，增加学习率
            self.learning_rate = min(0.05, self.learning_rate * 1.1)
            self.logger.info(f"性能下降，增加学习率至 {self.learning_rate:.4f}")
        elif accuracy > 0.6:
            # 性能良好，降低学习率
            self.learning_rate = max(0.001, self.learning_rate * 0.9)
            self.logger.info(f"性能良好，降低学习率至 {self.learning_rate:.4f}")
    
    def update_performance(self, decision_result: Dict):
        """更新性能记录"""
        self.performance_history.append(decision_result)


class AnomalyDetector:
    """异常检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('adaptive_decision', {}).get('anomaly_detection', {})
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 异常检测参数
        self.sensitivity = self.config.get('sensitivity', 0.1)
        self.baseline_metrics = {}
        
    def detect_anomaly(self, features: Dict, ml_predictions: Dict) -> bool:
        """
        检测异常模式
        
        Returns:
            是否检测到异常
        """
        anomalies = []
        
        # 1. 特征异常检测
        if self._detect_feature_anomaly(features):
            anomalies.append('feature_anomaly')
        
        # 2. 预测异常检测
        if self._detect_prediction_anomaly(ml_predictions):
            anomalies.append('prediction_anomaly')
        
        # 3. 性能异常检测
        if self._detect_performance_anomaly(features):
            anomalies.append('performance_anomaly')
        
        if anomalies:
            self.logger.warning(f"检测到异常: {anomalies}")
            return True
        
        return False
    
    def _detect_feature_anomaly(self, features: Dict) -> bool:
        """检测特征异常"""
        # 简化的异常检测
        extreme_values = 0
        
        for key, value in features.items():
            if isinstance(value, (int, float)):
                if abs(value) > 10:  # 极端值检测
                    extreme_values += 1
        
        return extreme_values > 3
    
    def _detect_prediction_anomaly(self, ml_predictions: Dict) -> bool:
        """检测预测异常"""
        if not ml_predictions:
            return False
        
        # 检查预测值是否在合理范围内
        for pred in ml_predictions.values():
            if pred.prediction < 0 or pred.prediction > 1:
                return True
            if pred.confidence < 0 or pred.confidence > 1:
                return True
        
        return False
    
    def _detect_performance_anomaly(self, features: Dict) -> bool:
        """检测性能异常"""
        # 检查是否有极端的连错
        current_streak = features.get('current_streak', 0)
        if current_streak < -10:  # 连续10次错误
            return True
        
        return False


class AdaptiveDecisionLayer:
    """
    自适应决策层管理器

    整合置信度评估、风险控制、在线学习和异常检测
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

        # 初始化各个组件
        self.confidence_evaluator = ConfidenceEvaluator(config)
        self.risk_controller = RiskController(config)
        self.online_learner = OnlineLearner(config)
        self.anomaly_detector = AnomalyDetector(config)

        # 决策历史
        self.decision_history = deque(maxlen=1000)

        # 初始化权重和状态
        self.current_weights = {
            'strategy_1': 0.33,
            'strategy_2': 0.33,
            'strategy_6': 0.34
        }
        self.decision_count = 0
        self.last_decision_time = 0
        self.optimization_history = []

        self.logger.info("自适应决策层初始化完成")
    
    def make_decision(self, strategy_outputs: Dict, features: Dict,
                     ml_predictions: Dict, decision_count: int, dl_predictions=None,
                     optimization_context: Optional[Dict] = None) -> FinalDecision:
        """
        做出最终决策

        Args:
            strategy_outputs: 策略输出
            features: 特征
            ml_predictions: ML预测
            decision_count: 决策计数
            dl_predictions: 深度学习预测 (可选)
            optimization_context: 多目标优化上下文 (可选)

        Returns:
            最终决策
        """
        decision_id = f"decision_{decision_count}_{int(time.time())}"

        # 1. 评估置信度
        confidence = self.confidence_evaluator.evaluate_confidence(
            strategy_outputs, features, ml_predictions
        )

        # 应用多目标优化参数
        if optimization_context:
            # 使用优化后的置信度阈值
            confidence_threshold = optimization_context.get('confidence_threshold', 0.5)
            if confidence < confidence_threshold:
                confidence = confidence * 0.8  # 降低置信度

            # 使用优化后的策略权重
            strategy_weights = optimization_context.get('strategy_weights', {})
            if strategy_weights:
                self._apply_strategy_weights(strategy_outputs, strategy_weights)

        # 2. 评估风险
        risk_level = self.risk_controller.assess_risk(confidence, features, decision_count)

        # 应用风险容忍度调整
        if optimization_context:
            risk_tolerance = optimization_context.get('risk_tolerance', 0.5)
            if risk_tolerance > 0.7:
                # 高风险容忍度，降低风险等级
                if risk_level == 'high':
                    risk_level = 'medium'
                elif risk_level == 'medium':
                    risk_level = 'low'

        # 3. 异常检测
        is_anomaly = self.anomaly_detector.detect_anomaly(features, ml_predictions)

        # 4. 决定是否执行
        should_act = self.risk_controller.should_act(confidence, risk_level, decision_count)

        if is_anomaly:
            should_act = False
            risk_level = 'high'

        # 5. 生成最终预测
        final_prediction = self._generate_final_prediction(
            strategy_outputs, ml_predictions, confidence, dl_predictions
        )

        # 6. 生成决策理由
        reasoning = self._generate_reasoning(
            strategy_outputs, ml_predictions, confidence, risk_level, should_act
        )

        # 创建最终决策
        final_decision = FinalDecision(
            decision_id=decision_id,
            prediction=final_prediction,
            confidence=confidence,
            risk_level=risk_level,
            should_act=should_act,
            reasoning=reasoning,
            strategy_outputs=strategy_outputs,
            ml_predictions=ml_predictions,
            features=features,
            timestamp=time.time()
        )

        # 记录决策
        self.decision_history.append(final_decision)

        # 训练期间不打印决策详情，只记录到历史
        # 可以通过设置环境变量 VERBOSE_DECISIONS=1 来启用详细日志
        import os
        if os.getenv('VERBOSE_DECISIONS') == '1' and (self.decision_count % 100 == 0 or self.decision_count <= 5):
            self.logger.info(f"决策 {decision_id}: 预测={final_prediction}, 置信度={confidence:.3f}, "
                            f"风险={risk_level}, 执行={should_act}")

        return final_decision
    
    def _generate_final_prediction(self, strategy_outputs: Dict, ml_predictions: Dict,
                                 confidence: float, dl_predictions=None) -> int:
        """生成最终预测"""
        # 综合策略和ML预测
        strategy_consensus = sum(output.prediction for output in strategy_outputs.values())
        strategy_prediction = 1 if strategy_consensus >= len(strategy_outputs) / 2 else 0

        # 收集所有预测
        predictions = []
        weights = []

        # 策略预测
        predictions.append(strategy_prediction)
        weights.append(0.3)  # 策略权重

        # ML预测
        if ml_predictions:
            ml_consensus = sum(pred.prediction for pred in ml_predictions.values())
            ml_prediction = 1 if ml_consensus >= len(ml_predictions) / 2 else 0
            predictions.append(ml_prediction)
            weights.append(0.4)  # ML权重

        # 深度学习预测
        if dl_predictions and hasattr(dl_predictions, 'prediction'):
            dl_prediction = 1 if dl_predictions.prediction >= 0.5 else 0
            predictions.append(dl_prediction)
            weights.append(0.3)  # 深度学习权重

        # 加权投票
        if len(predictions) > 1:
            # 归一化权重
            total_weight = sum(weights[:len(predictions)])
            normalized_weights = [w / total_weight for w in weights[:len(predictions)]]

            # 加权平均
            weighted_sum = sum(p * w for p, w in zip(predictions, normalized_weights))
            final_prediction = 1 if weighted_sum >= 0.5 else 0

            return final_prediction
        else:
            return strategy_prediction
    
    def _generate_reasoning(self, strategy_outputs: Dict, ml_predictions: Dict,
                          confidence: float, risk_level: str, should_act: bool) -> str:
        """生成决策理由"""
        reasons = []
        
        # 策略一致性
        consensus = sum(output.prediction for output in strategy_outputs.values())
        if consensus == len(strategy_outputs):
            reasons.append("策略完全一致")
        elif consensus == 0:
            reasons.append("策略完全分歧")
        else:
            reasons.append(f"策略部分一致({consensus}/{len(strategy_outputs)})")
        
        # 置信度
        if confidence > 0.7:
            reasons.append("高置信度")
        elif confidence < 0.4:
            reasons.append("低置信度")
        
        # 风险等级
        reasons.append(f"风险等级: {risk_level}")
        
        # 执行决策
        if not should_act:
            reasons.append("风险过高，建议观望")
        
        return "; ".join(reasons)
    
    def update_feedback(self, decision_id: str, actual_result: int):
        """更新决策反馈"""
        # 查找对应的决策
        for decision in self.decision_history:
            if decision.decision_id == decision_id:
                is_correct = (decision.prediction == actual_result)

                # 更新风险控制器
                self.risk_controller.update_error_feedback(is_correct)

                # 更新在线学习器
                self.online_learner.update_performance({
                    'decision_id': decision_id,
                    'is_correct': is_correct,
                    'confidence': decision.confidence,
                    'risk_level': decision.risk_level
                })

                break
    
    def adapt_parameters(self):
        """自适应参数调整"""
        self.online_learner.adapt_parameters()

    def _apply_strategy_weights(self, strategy_outputs: Dict, strategy_weights: Dict):
        """应用策略权重调整"""
        # 根据优化后的权重调整策略输出的置信度
        for strategy_name, output in strategy_outputs.items():
            weight_key = f'strategy_{strategy_name.split("_")[-1]}'  # 提取策略编号
            if weight_key in strategy_weights:
                weight = strategy_weights[weight_key]
                # 调整置信度
                output.confidence = output.confidence * (0.5 + weight)
                self.logger.debug(f"应用权重 {weight:.3f} 到策略 {strategy_name}")

        self.logger.info(f"应用策略权重: {strategy_weights}")

    def get_current_weights(self) -> Dict[str, float]:
        """获取当前权重"""
        return self.current_weights.copy()

    def update_weights(self, new_weights: Dict[str, float]):
        """更新权重"""
        self.current_weights.update(new_weights)
        self.logger.info(f"权重已更新: {new_weights}")

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'decision_count': self.decision_count,
            'last_decision_time': self.last_decision_time,
            'current_weights': self.current_weights.copy(),
            'optimization_history': len(self.optimization_history),
            'risk_controller_status': self.risk_controller.get_status() if hasattr(self.risk_controller, 'get_status') else None
        }
