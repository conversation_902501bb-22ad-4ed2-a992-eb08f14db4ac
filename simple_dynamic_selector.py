#!/usr/bin/env python3
"""
简化动态策略选择器
专注于核心功能，实现动态策略选择
"""

import sys
import os
import logging
import warnings
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SimpleDynamicSelector:
    """简化动态策略选择器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def create_simple_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建简单但有效的特征"""
        self.logger.info("🔧 创建简单特征...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. Boot内位置特征
        df['position_in_boot'] = df.groupby('boot_id').cumcount() + 1
        df['boot_size'] = df.groupby('boot_id')['boot_id'].transform('count')
        df['boot_progress'] = df['position_in_boot'] / df['boot_size']
        
        # Boot阶段
        df['boot_stage'] = pd.cut(df['boot_progress'], 
                                 bins=[0, 0.25, 0.5, 0.75, 1.0], 
                                 labels=[0, 1, 2, 3],
                                 include_lowest=True).astype(int)
        
        # 2. 策略统计特征
        df['strategy_sum'] = df[strategy_cols].sum(axis=1)
        df['strategy_mean'] = df[strategy_cols].mean(axis=1)
        df['strategy_std'] = df[strategy_cols].std(axis=1).fillna(0)
        
        # 3. 简单历史特征
        for window in [3, 5, 10]:
            for strategy in strategy_cols:
                df[f'{strategy}_ma_{window}'] = df[strategy].rolling(window=window, min_periods=1).mean()
        
        # 4. 策略组合特征
        df['high_corr_sum'] = df['strategy_1'] + df['strategy_8']
        df['mid_corr_sum'] = df['strategy_3'] + df['strategy_5'] + df['strategy_7']
        df['low_corr_sum'] = df['strategy_2'] + df['strategy_4'] + df['strategy_6']
        
        # 5. 全局位置
        df['global_position'] = range(len(df))
        df['global_progress'] = df['global_position'] / len(df)
        
        return df
    
    def create_best_strategy_labels(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建最佳策略标签"""
        self.logger.info("🎯 创建最佳策略标签...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        best_strategies = []
        for _, row in df.iterrows():
            # 找出正确的策略
            correct_strategies = []
            for strategy in strategy_cols:
                if row[strategy] == row['actual_result']:
                    correct_strategies.append(strategy)
            
            # 如果有正确的策略，选择优先级最高的
            if correct_strategies:
                priority_order = ['strategy_1', 'strategy_8', 'strategy_3', 'strategy_5', 
                                'strategy_7', 'strategy_2', 'strategy_4', 'strategy_6']
                best_strategy = min(correct_strategies, key=lambda x: priority_order.index(x))
            else:
                # 如果都错误，选择strategy_1作为默认
                best_strategy = 'strategy_1'
            
            best_strategies.append(best_strategy)
        
        df['best_strategy'] = best_strategies
        
        # 编码为数字
        strategy_to_id = {f'strategy_{i}': i-1 for i in range(1, 9)}
        df['best_strategy_id'] = df['best_strategy'].map(strategy_to_id)
        
        return df
    
    def train_strategy_selector(self, df: pd.DataFrame) -> Dict[str, Any]:
        """训练策略选择器"""
        try:
            from sklearn.model_selection import train_test_split
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.metrics import accuracy_score
            from sklearn.preprocessing import StandardScaler
            
            self.logger.info("🤖 训练策略选择器...")
            
            # 准备特征
            exclude_cols = ['id', 'boot_id', 'actual_result', 'best_strategy', 'best_strategy_id'] + [f'strategy_{i}' for i in range(1, 9)]
            feature_cols = [col for col in df.columns if col not in exclude_cols]
            
            X = df[feature_cols].values
            y = df['best_strategy_id'].values
            
            self.logger.info(f"   📊 特征数: {len(feature_cols)}")
            self.logger.info(f"   📊 样本数: {len(X)}")
            
            # 特征标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 按Boot分割数据
            unique_boots = df['boot_id'].unique()
            train_boots, test_boots = train_test_split(unique_boots, test_size=0.2, random_state=42)
            
            train_mask = df['boot_id'].isin(train_boots)
            test_mask = df['boot_id'].isin(test_boots)
            
            X_train, X_test = X_scaled[train_mask], X_scaled[test_mask]
            y_train, y_test = y[train_mask], y[test_mask]
            
            self.logger.info(f"   📊 训练样本: {len(X_train)}, 测试样本: {len(X_test)}")
            
            # 训练随机森林选择器
            selector = RandomForestClassifier(
                n_estimators=200, 
                max_depth=10, 
                min_samples_split=10,
                min_samples_leaf=5, 
                random_state=42
            )
            
            selector.fit(X_train, y_train)
            y_pred = selector.predict(X_test)
            selector_accuracy = accuracy_score(y_test, y_pred)
            
            self.logger.info(f"   🏆 策略选择器准确率: {selector_accuracy:.3f}")
            
            return {
                'selector': selector,
                'scaler': scaler,
                'feature_names': feature_cols,
                'selector_accuracy': selector_accuracy,
                'test_data': (X_test, y_test, df[test_mask])
            }
            
        except Exception as e:
            self.logger.error(f"❌ 策略选择器训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def evaluate_dynamic_system(self, df: pd.DataFrame, selector_result: Dict[str, Any]) -> Dict[str, Any]:
        """评估动态策略选择系统"""
        self.logger.info("📈 评估动态策略选择系统...")
        
        try:
            selector = selector_result['selector']
            scaler = selector_result['scaler']
            X_test, y_test, test_df = selector_result['test_data']
            
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            
            # 预测最佳策略
            predicted_strategy_ids = selector.predict(X_test)
            
            # 计算动态选择的准确率
            correct_predictions = 0
            total_predictions = len(test_df)
            
            strategy_usage = {f'strategy_{i}': 0 for i in range(1, 9)}
            
            for idx, (_, row) in enumerate(test_df.iterrows()):
                predicted_strategy_id = predicted_strategy_ids[idx]
                predicted_strategy = f'strategy_{predicted_strategy_id + 1}'
                
                # 使用预测的策略进行决策
                predicted_decision = row[predicted_strategy]
                actual_result = row['actual_result']
                
                if predicted_decision == actual_result:
                    correct_predictions += 1
                
                strategy_usage[predicted_strategy] += 1
            
            dynamic_accuracy = correct_predictions / total_predictions
            
            # 对比基准
            baseline_accuracy = 0.568  # strategy_1的准确率
            improvement = dynamic_accuracy - baseline_accuracy
            
            # 计算理论上限（完美选择）
            perfect_correct = 0
            for _, row in test_df.iterrows():
                best_strategy = row['best_strategy']
                perfect_decision = row[best_strategy]
                if perfect_decision == row['actual_result']:
                    perfect_correct += 1
            
            perfect_accuracy = perfect_correct / len(test_df)
            
            # 计算各个策略单独的准确率
            individual_accuracies = {}
            for strategy in strategy_cols:
                strategy_correct = (test_df[strategy] == test_df['actual_result']).sum()
                individual_accuracies[strategy] = strategy_correct / len(test_df)
            
            self.logger.info(f"   📊 动态选择准确率: {dynamic_accuracy:.3f}")
            self.logger.info(f"   📊 基准准确率 (strategy_1): {baseline_accuracy:.3f}")
            self.logger.info(f"   📊 提升幅度: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
            self.logger.info(f"   📊 理论上限: {perfect_accuracy:.3f}")
            
            if perfect_accuracy > baseline_accuracy:
                realization_ratio = (dynamic_accuracy - baseline_accuracy) / (perfect_accuracy - baseline_accuracy)
                self.logger.info(f"   📊 潜力实现: {realization_ratio*100:.1f}%")
            else:
                realization_ratio = 0
            
            self.logger.info(f"\n   🎯 策略使用分布:")
            for strategy, count in strategy_usage.items():
                percentage = count / total_predictions * 100
                individual_acc = individual_accuracies[strategy]
                self.logger.info(f"     {strategy}: {count} ({percentage:.1f}%) - 个体准确率: {individual_acc:.3f}")
            
            return {
                'dynamic_accuracy': dynamic_accuracy,
                'baseline_accuracy': baseline_accuracy,
                'improvement': improvement,
                'perfect_accuracy': perfect_accuracy,
                'realization_ratio': realization_ratio,
                'strategy_usage': strategy_usage,
                'individual_accuracies': individual_accuracies
            }
            
        except Exception as e:
            self.logger.error(f"❌ 动态系统评估失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def run_optimization(self):
        """运行优化"""
        self.logger.info("🚀 开始简化动态策略选择优化")
        self.logger.info("🎯 目标：突破56.8%基准，向62.4%理论上限迈进")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，优化终止")
            return
        
        # 2. 创建特征
        df = self.create_simple_features(df)
        
        # 3. 创建标签
        df = self.create_best_strategy_labels(df)
        
        # 4. 训练选择器
        selector_result = self.train_strategy_selector(df)
        
        if not selector_result:
            self.logger.error("❌ 策略选择器训练失败")
            return
        
        # 5. 评估系统
        evaluation_result = self.evaluate_dynamic_system(df, selector_result)
        
        # 6. 生成报告
        total_time = time.time() - start_time
        self.generate_final_report(evaluation_result, total_time)
    
    def generate_final_report(self, evaluation_result: Dict[str, Any], total_time: float):
        """生成最终报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 简化动态策略选择最终报告")
        self.logger.info("="*80)
        
        if not evaluation_result:
            self.logger.error("❌ 没有评估结果")
            return
        
        dynamic_accuracy = evaluation_result.get('dynamic_accuracy', 0)
        baseline_accuracy = evaluation_result.get('baseline_accuracy', 0.568)
        improvement = evaluation_result.get('improvement', 0)
        perfect_accuracy = evaluation_result.get('perfect_accuracy', 0.624)
        realization_ratio = evaluation_result.get('realization_ratio', 0)
        
        self.logger.info(f"\n📊 性能对比:")
        self.logger.info(f"   - 基准准确率 (strategy_1): {baseline_accuracy:.3f}")
        self.logger.info(f"   - 动态选择准确率: {dynamic_accuracy:.3f}")
        self.logger.info(f"   - 理论上限: {perfect_accuracy:.3f}")
        self.logger.info(f"   - 实际提升: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        self.logger.info(f"   - 潜力实现: {realization_ratio*100:.1f}%")
        
        target_accuracy = 0.60
        if dynamic_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 成功达到目标准确率 {target_accuracy:.1%}!")
            self.logger.info("✅ 动态策略选择系统已准备好投入生产！")
        else:
            gap = target_accuracy - dynamic_accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f}")
            
            if improvement > 0.005:  # 提升超过0.5%
                self.logger.info("✅ 动态选择有效果！证明其他策略确实有价值")
                self.logger.info("💡 建议：继续优化选择器，探索更好的上下文特征")
            else:
                self.logger.info("⚠️ 动态选择效果有限")
                self.logger.info("💡 建议：可能需要改进基础策略算法本身")
        
        # 关键结论
        self.logger.info(f"\n💡 关键结论:")
        if improvement > 0:
            self.logger.info("   ✅ 其他7个策略确实有价值！不应该被忽略")
            self.logger.info("   ✅ 动态选择比静态权重更有效")
            self.logger.info("   ✅ 问题在于我们之前的特征工程方式不对")
        else:
            self.logger.info("   ⚠️ 当前上下文特征可能不足以有效选择策略")
            self.logger.info("   💡 需要探索更好的上下文特征或改进基础策略")
        
        self.logger.info(f"\n⏱️ 总优化时间: {total_time:.1f}秒")
        self.logger.info("="*80)

if __name__ == "__main__":
    optimizer = SimpleDynamicSelector()
    optimizer.run_optimization()
