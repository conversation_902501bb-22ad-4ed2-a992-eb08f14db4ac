#!/usr/bin/env python3
"""
基于移动窗口的策略选择器
利用各个策略的移动窗口表现来动态选择最佳策略
"""

import sys
import os
import logging
import warnings
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class WindowBasedSelector:
    """基于移动窗口的策略选择器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_moving_window_performance(self, df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
        """计算各策略的移动窗口表现"""
        self.logger.info("📈 计算移动窗口表现...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        for window in window_sizes:
            self.logger.info(f"   🔄 计算窗口大小 {window}...")
            
            for strategy in strategy_cols:
                # 计算移动窗口准确率
                df[f'{strategy}_acc_{window}'] = df[strategy].rolling(window=window, min_periods=1).apply(
                    lambda x: (x == df['actual_result'].iloc[x.index]).mean()
                ).fillna(0.5)
                
                # 计算移动窗口趋势
                df[f'{strategy}_trend_{window}'] = df[f'{strategy}_acc_{window}'].diff().fillna(0)
                
                # 计算移动窗口稳定性
                df[f'{strategy}_stability_{window}'] = df[f'{strategy}_acc_{window}'].rolling(window=5, min_periods=1).std().fillna(0)
        
        return df
    
    def create_window_based_selector(self, df: pd.DataFrame, window_sizes: List[int]) -> pd.DataFrame:
        """创建基于窗口的策略选择器"""
        self.logger.info("🎯 创建基于窗口的策略选择器...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 为每个样本选择最佳策略
        best_strategies = []
        selection_confidences = []
        
        for idx, row in df.iterrows():
            if idx % 5000 == 0:
                self.logger.info(f"   处理进度: {idx}/{len(df)}")
            
            # 收集各策略在不同窗口下的表现
            strategy_scores = {}
            
            for strategy in strategy_cols:
                scores = []
                
                for window in window_sizes:
                    acc_col = f'{strategy}_acc_{window}'
                    trend_col = f'{strategy}_trend_{window}'
                    stability_col = f'{strategy}_stability_{window}'
                    
                    if acc_col in df.columns:
                        accuracy = row[acc_col]
                        trend = row[trend_col]
                        stability = 1 / (1 + row[stability_col])  # 稳定性越高越好
                        
                        # 综合评分：准确率 + 趋势奖励 + 稳定性奖励
                        score = accuracy + trend * 0.1 + stability * 0.05
                        scores.append(score)
                
                # 计算策略的综合得分
                if scores:
                    strategy_scores[strategy] = np.mean(scores)
                else:
                    strategy_scores[strategy] = 0.5
            
            # 选择得分最高的策略
            best_strategy = max(strategy_scores, key=strategy_scores.get)
            best_score = strategy_scores[best_strategy]
            
            # 计算选择置信度
            sorted_scores = sorted(strategy_scores.values(), reverse=True)
            if len(sorted_scores) > 1:
                confidence = (sorted_scores[0] - sorted_scores[1]) / sorted_scores[0]
            else:
                confidence = 0.5
            
            best_strategies.append(best_strategy)
            selection_confidences.append(confidence)
        
        df['selected_strategy'] = best_strategies
        df['selection_confidence'] = selection_confidences
        
        return df
    
    def evaluate_window_selector(self, df: pd.DataFrame) -> Dict[str, Any]:
        """评估窗口选择器"""
        self.logger.info("📊 评估窗口选择器...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 计算窗口选择器的准确率
        correct_selections = 0
        total_selections = len(df)
        
        strategy_usage = {strategy: 0 for strategy in strategy_cols}
        confidence_levels = []
        
        for _, row in df.iterrows():
            selected_strategy = row['selected_strategy']
            confidence = row['selection_confidence']
            
            # 使用选择的策略进行预测
            prediction = row[selected_strategy]
            actual = row['actual_result']
            
            if prediction == actual:
                correct_selections += 1
            
            strategy_usage[selected_strategy] += 1
            confidence_levels.append(confidence)
        
        window_accuracy = correct_selections / total_selections
        
        # 对比基准
        baseline_accuracy = (df['strategy_1'] == df['actual_result']).mean()
        improvement = window_accuracy - baseline_accuracy
        
        # 计算理论上限
        perfect_selections = 0
        for _, row in df.iterrows():
            # 找出正确的策略
            for strategy in strategy_cols:
                if row[strategy] == row['actual_result']:
                    perfect_selections += 1
                    break
        
        perfect_accuracy = perfect_selections / len(df)
        
        # 计算各置信度水平的表现
        confidence_bins = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5]
        confidence_performance = {}
        
        for i in range(len(confidence_bins) - 1):
            low, high = confidence_bins[i], confidence_bins[i + 1]
            mask = (df['selection_confidence'] >= low) & (df['selection_confidence'] < high)
            
            if mask.sum() > 0:
                bin_data = df[mask]
                bin_correct = 0
                for _, row in bin_data.iterrows():
                    if row[row['selected_strategy']] == row['actual_result']:
                        bin_correct += 1
                
                confidence_performance[f'{low:.1f}-{high:.1f}'] = {
                    'accuracy': bin_correct / len(bin_data),
                    'count': len(bin_data)
                }
        
        self.logger.info(f"   📊 窗口选择器准确率: {window_accuracy:.3f}")
        self.logger.info(f"   📊 基准准确率: {baseline_accuracy:.3f}")
        self.logger.info(f"   📊 提升幅度: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        self.logger.info(f"   📊 理论上限: {perfect_accuracy:.3f}")
        
        if perfect_accuracy > baseline_accuracy:
            realization_ratio = (window_accuracy - baseline_accuracy) / (perfect_accuracy - baseline_accuracy)
            self.logger.info(f"   📊 潜力实现: {realization_ratio*100:.1f}%")
        else:
            realization_ratio = 0
        
        self.logger.info(f"\n   🎯 策略使用分布:")
        for strategy, count in strategy_usage.items():
            percentage = count / total_selections * 100
            self.logger.info(f"     {strategy}: {count} ({percentage:.1f}%)")
        
        self.logger.info(f"\n   🎯 置信度水平表现:")
        for level, perf in confidence_performance.items():
            self.logger.info(f"     置信度 {level}: 准确率={perf['accuracy']:.3f}, 样本数={perf['count']}")
        
        return {
            'window_accuracy': window_accuracy,
            'baseline_accuracy': baseline_accuracy,
            'improvement': improvement,
            'perfect_accuracy': perfect_accuracy,
            'realization_ratio': realization_ratio,
            'strategy_usage': strategy_usage,
            'confidence_performance': confidence_performance,
            'mean_confidence': np.mean(confidence_levels)
        }
    
    def test_different_window_combinations(self, df: pd.DataFrame) -> Dict[str, Any]:
        """测试不同窗口组合"""
        self.logger.info("🔬 测试不同窗口组合...")
        
        window_combinations = [
            [5, 10, 20],
            [3, 7, 15],
            [10, 20, 50],
            [5, 15, 30],
            [3, 10, 25],
            [7, 14, 28]
        ]
        
        best_combination = None
        best_accuracy = 0
        results = {}
        
        for i, windows in enumerate(window_combinations):
            self.logger.info(f"   🔄 测试组合 {i+1}: {windows}")
            
            # 重新计算移动窗口表现
            df_test = df.copy()
            df_test = self.calculate_moving_window_performance(df_test, windows)
            
            # 创建选择器
            df_test = self.create_window_based_selector(df_test, windows)
            
            # 评估
            result = self.evaluate_window_selector(df_test)
            accuracy = result['window_accuracy']
            
            results[f'combination_{i+1}'] = {
                'windows': windows,
                'accuracy': accuracy,
                'improvement': result['improvement']
            }
            
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                best_combination = windows
            
            self.logger.info(f"      ✅ 准确率: {accuracy:.3f}")
        
        self.logger.info(f"\n   🏆 最佳窗口组合: {best_combination} (准确率: {best_accuracy:.3f})")
        
        return {
            'best_combination': best_combination,
            'best_accuracy': best_accuracy,
            'all_results': results
        }
    
    def run_window_optimization(self):
        """运行窗口优化"""
        self.logger.info("🚀 开始基于移动窗口的策略选择优化")
        self.logger.info("🎯 目标：利用98.5%的理论上限，实现显著性能提升")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，优化终止")
            return
        
        # 2. 测试不同窗口组合
        combination_results = self.test_different_window_combinations(df)
        
        # 3. 使用最佳组合进行最终评估
        best_windows = combination_results['best_combination']
        self.logger.info(f"\n🎯 使用最佳窗口组合 {best_windows} 进行最终评估...")
        
        df = self.calculate_moving_window_performance(df, best_windows)
        df = self.create_window_based_selector(df, best_windows)
        final_result = self.evaluate_window_selector(df)
        
        # 4. 生成最终报告
        total_time = time.time() - start_time
        self.generate_final_report(final_result, combination_results, total_time)
    
    def generate_final_report(self, final_result: Dict[str, Any], 
                            combination_results: Dict[str, Any], total_time: float):
        """生成最终报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 基于移动窗口的策略选择最终报告")
        self.logger.info("="*80)
        
        window_accuracy = final_result.get('window_accuracy', 0)
        baseline_accuracy = final_result.get('baseline_accuracy', 0.568)
        improvement = final_result.get('improvement', 0)
        perfect_accuracy = final_result.get('perfect_accuracy', 0.985)
        realization_ratio = final_result.get('realization_ratio', 0)
        
        self.logger.info(f"\n📊 最终性能:")
        self.logger.info(f"   - 基准准确率 (strategy_1): {baseline_accuracy:.3f}")
        self.logger.info(f"   - 窗口选择器准确率: {window_accuracy:.3f}")
        self.logger.info(f"   - 理论上限: {perfect_accuracy:.3f}")
        self.logger.info(f"   - 实际提升: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        self.logger.info(f"   - 潜力实现: {realization_ratio*100:.1f}%")
        self.logger.info(f"   - 最佳窗口组合: {combination_results['best_combination']}")
        
        target_accuracy = 0.60
        if window_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 成功达到目标准确率 {target_accuracy:.1%}!")
            self.logger.info("✅ 基于移动窗口的策略选择系统已准备好投入生产！")
        else:
            gap = target_accuracy - window_accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f}")
            
            if improvement > 0.02:  # 提升超过2%
                self.logger.info("✅ 移动窗口选择显著有效！")
                self.logger.info("💡 建议：继续优化窗口参数和选择算法")
            elif improvement > 0.005:  # 提升超过0.5%
                self.logger.info("✅ 移动窗口选择有一定效果")
                self.logger.info("💡 建议：探索更复杂的选择策略")
            else:
                self.logger.info("⚠️ 移动窗口选择效果有限")
                self.logger.info("💡 建议：可能需要更长的历史窗口或其他方法")
        
        # 关键结论
        self.logger.info(f"\n💡 关键结论:")
        if improvement > 0.01:
            self.logger.info("   ✅ 移动窗口方法证明了策略选择器的价值！")
            self.logger.info("   ✅ 98.5%的理论上限确实可以部分实现")
            self.logger.info("   ✅ 问题确实在于选择器，而不是策略本身")
        else:
            self.logger.info("   ⚠️ 当前移动窗口方法仍不足以有效利用策略多样性")
            self.logger.info("   💡 需要探索更智能的选择算法")
        
        self.logger.info(f"\n⏱️ 总优化时间: {total_time:.1f}秒")
        self.logger.info("="*80)

if __name__ == "__main__":
    optimizer = WindowBasedSelector()
    optimizer.run_window_optimization()
