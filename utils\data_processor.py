#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理器

负责数据的加载、预处理、清洗和转换
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import pymysql
import yaml


class DataProcessor:
    """数据处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 数据库配置
        self.db_config = config.get('database', {}).get('primary', {})
        
        self.logger.info("数据处理器初始化完成")
    
    def load_historical_data(self, limit: Optional[int] = None) -> pd.DataFrame:
        """
        加载历史数据
        
        Args:
            limit: 限制加载的数据量
            
        Returns:
            历史数据DataFrame
        """
        try:
            # 连接数据库
            conn = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['username'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4'
            )
            
            # 构建增强的查询，在SQL层面排除空值
            query = f"""
            SELECT id, strategy_1, strategy_2, strategy_3, strategy_4,
                   strategy_5, strategy_6, strategy_7, strategy_8,
                   true_label as actual_result, boot_id, created_at
            FROM {self.db_config['table']}
            WHERE true_label IS NOT NULL
              AND true_label != ''
              AND true_label != '-'
              AND true_label REGEXP '^[0-1]$'
            ORDER BY boot_id, id
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            # 加载数据
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            self.logger.info(f"加载了 {len(df)} 条历史数据")
            
            # 数据清洗
            df = self._clean_data(df)
            
            return df
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {str(e)}")
            return pd.DataFrame()
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        增强的数据清洗逻辑

        清洗规则：
        1. 排除基础策略为空值、null、'-'等无效值的数据
        2. 确保策略值在0-1范围内
        3. 处理actual_result的无效值
        4. 移除任何包含NaN的行
        """
        if df.empty:
            return df

        original_size = len(df)
        self.logger.info(f"开始数据清洗，原始数据量: {original_size}")

        # 核心策略列 (所有8个策略)
        strategy_columns = [f'strategy_{i}' for i in range(1, 9)]

        # 第一步：标记无效的策略值
        invalid_values = [None, '', '-', 'null', 'NULL', 'None', 'nan', 'NaN']

        for col in strategy_columns:
            if col in df.columns:
                # 记录原始无效值数量
                invalid_mask = df[col].isin(invalid_values) | df[col].isna()
                invalid_count = invalid_mask.sum()

                if invalid_count > 0:
                    self.logger.info(f"{col} 列发现 {invalid_count} 个无效值")

                # 将字符串类型的无效值转换为NaN
                df.loc[df[col].isin(invalid_values), col] = np.nan

                # 尝试转换为数字，无法转换的变为NaN
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 第二步：移除任何策略列包含NaN的行
        strategy_nan_mask = df[strategy_columns].isna().any(axis=1)
        strategy_nan_count = strategy_nan_mask.sum()

        if strategy_nan_count > 0:
            self.logger.info(f"移除 {strategy_nan_count} 条包含策略空值的数据")
            df = df[~strategy_nan_mask]

        # 第三步：确保策略值在有效范围内
        for col in strategy_columns:
            if col in df.columns:
                # 移除超出0-1范围的值
                out_of_range_mask = (df[col] < 0) | (df[col] > 1)
                out_of_range_count = out_of_range_mask.sum()

                if out_of_range_count > 0:
                    self.logger.info(f"{col} 列发现 {out_of_range_count} 个超出范围的值")
                    df = df[~out_of_range_mask]

                # 确保值为整数（0或1）
                df[col] = df[col].round().astype(int)

        # 第四步：处理actual_result列
        if 'actual_result' in df.columns:
            # 处理无效值
            invalid_result_mask = df['actual_result'].isin(invalid_values) | df['actual_result'].isna()
            invalid_result_count = invalid_result_mask.sum()

            if invalid_result_count > 0:
                self.logger.info(f"actual_result 列发现 {invalid_result_count} 个无效值")
                df = df[~invalid_result_mask]

            # 转换为数字
            df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')

            # 移除转换失败的行
            result_nan_mask = df['actual_result'].isna()
            result_nan_count = result_nan_mask.sum()

            if result_nan_count > 0:
                self.logger.info(f"移除 {result_nan_count} 条actual_result转换失败的数据")
                df = df[~result_nan_mask]

            # 确保值在0-1范围内
            out_of_range_result_mask = (df['actual_result'] < 0) | (df['actual_result'] > 1)
            out_of_range_result_count = out_of_range_result_mask.sum()

            if out_of_range_result_count > 0:
                self.logger.info(f"移除 {out_of_range_result_count} 条actual_result超出范围的数据")
                df = df[~out_of_range_result_mask]

            # 确保值为整数（0或1）
            df['actual_result'] = df['actual_result'].round().astype(int)

        # 第五步：最终检查，移除任何剩余的NaN
        final_nan_mask = df.isna().any(axis=1)
        final_nan_count = final_nan_mask.sum()

        if final_nan_count > 0:
            self.logger.info(f"最终清理：移除 {final_nan_count} 条包含NaN的数据")
            df = df[~final_nan_mask]

        # 重置索引
        df = df.reset_index(drop=True)

        cleaned_size = len(df)
        removed_count = original_size - cleaned_size
        retention_rate = (cleaned_size / original_size) * 100 if original_size > 0 else 0

        self.logger.info(f"数据清洗完成:")
        self.logger.info(f"  原始数据: {original_size} 条")
        self.logger.info(f"  清洗后: {cleaned_size} 条")
        self.logger.info(f"  移除: {removed_count} 条")
        self.logger.info(f"  保留率: {retention_rate:.2f}%")

        return df
    
    def prepare_training_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备训练数据
        
        Args:
            df: 原始数据
            
        Returns:
            (X, y) 训练数据
        """
        if df.empty:
            return np.array([]), np.array([])
        
        # 特征列 (所有8个策略)
        feature_columns = [f'strategy_{i}' for i in range(1, 9)]
        
        # 提取特征
        X = df[feature_columns].values
        
        # 提取标签
        y = df['actual_result'].values
        
        self.logger.info(f"准备了 {len(X)} 条训练数据，特征维度: {X.shape[1]}")
        
        return X, y
    
    def create_features_from_strategies(self, strategy_outputs: Dict) -> Dict[str, float]:
        """
        从策略输出创建特征
        
        Args:
            strategy_outputs: 策略输出
            
        Returns:
            特征字典
        """
        features = {}
        
        # 基础策略特征 (所有8个策略)
        strategy_names = [f'strategy_{i}' for i in range(1, 9)]
        predictions = []
        confidences = []
        
        for name in strategy_names:
            if name in strategy_outputs:
                output = strategy_outputs[name]
                features[f'{name}_prediction'] = float(output.prediction)
                features[f'{name}_confidence'] = float(output.confidence)
                
                predictions.append(output.prediction)
                confidences.append(output.confidence)
        
        # 聚合特征
        if predictions:
            features['consensus_count'] = sum(predictions)
            features['consensus_ratio'] = sum(predictions) / len(predictions)
            features['full_consensus'] = 1.0 if len(set(predictions)) == 1 else 0.0
        
        if confidences:
            features['avg_confidence'] = np.mean(confidences)
            features['min_confidence'] = np.min(confidences)
            features['max_confidence'] = np.max(confidences)
            features['confidence_std'] = np.std(confidences)
        
        return features
    
    def save_decision_record(self, decision_data: Dict):
        """
        保存决策记录
        
        Args:
            decision_data: 决策数据
        """
        try:
            # 这里可以实现决策记录的保存逻辑
            # 例如保存到数据库或文件
            self.logger.debug(f"保存决策记录: {decision_data.get('decision_id', 'unknown')}")
            
        except Exception as e:
            self.logger.error(f"保存决策记录失败: {str(e)}")
    
    def get_data_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Args:
            df: 数据DataFrame
            
        Returns:
            统计信息字典
        """
        if df.empty:
            return {}
        
        stats = {
            'total_records': len(df),
            'date_range': {
                'start': df['created_at'].min() if 'created_at' in df.columns else None,
                'end': df['created_at'].max() if 'created_at' in df.columns else None
            },
            'boot_count': df['boot_id'].nunique() if 'boot_id' in df.columns else 0,
            'strategy_stats': {}
        }
        
        # 策略统计 (所有8个策略)
        strategy_columns = [f'strategy_{i}' for i in range(1, 9)]
        for col in strategy_columns:
            if col in df.columns:
                stats['strategy_stats'][col] = {
                    'mean': float(df[col].mean()),
                    'std': float(df[col].std()),
                    'win_rate': float(df[col].mean()),  # 假设1表示胜利
                    'missing_count': int(df[col].isnull().sum())
                }
        
        # 结果统计
        if 'actual_result' in df.columns:
            stats['result_stats'] = {
                'win_rate': float(df['actual_result'].mean()),
                'total_wins': int(df['actual_result'].sum()),
                'total_losses': int(len(df) - df['actual_result'].sum())
            }
        
        return stats

    def validate_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        验证数据质量

        Args:
            df: 数据DataFrame

        Returns:
            数据质量报告
        """
        if df.empty:
            return {'status': 'empty', 'issues': ['数据为空']}

        quality_report = {
            'status': 'good',
            'total_records': len(df),
            'issues': [],
            'warnings': [],
            'statistics': {}
        }

        # 检查核心策略列 (所有8个策略)
        strategy_columns = [f'strategy_{i}' for i in range(1, 9)]

        for col in strategy_columns:
            if col not in df.columns:
                quality_report['issues'].append(f'缺少策略列: {col}')
                quality_report['status'] = 'error'
                continue

            # 检查空值
            null_count = df[col].isna().sum()
            if null_count > 0:
                quality_report['issues'].append(f'{col} 包含 {null_count} 个空值')
                quality_report['status'] = 'error'

            # 检查值范围
            if not df[col].empty:
                min_val = df[col].min()
                max_val = df[col].max()

                if min_val < 0 or max_val > 1:
                    quality_report['issues'].append(f'{col} 值超出范围 [0,1]: [{min_val}, {max_val}]')
                    quality_report['status'] = 'error'

                # 检查是否只包含0和1
                unique_values = set(df[col].unique())
                if not unique_values.issubset({0, 1}):
                    invalid_values = unique_values - {0, 1}
                    quality_report['warnings'].append(f'{col} 包含非0/1值: {invalid_values}')
                    if quality_report['status'] == 'good':
                        quality_report['status'] = 'warning'

            # 统计信息
            quality_report['statistics'][col] = {
                'null_count': int(null_count),
                'unique_values': list(df[col].unique()) if not df[col].empty else [],
                'value_counts': df[col].value_counts().to_dict() if not df[col].empty else {}
            }

        # 检查actual_result列
        if 'actual_result' in df.columns:
            null_count = df['actual_result'].isna().sum()
            if null_count > 0:
                quality_report['issues'].append(f'actual_result 包含 {null_count} 个空值')
                quality_report['status'] = 'error'

            if not df['actual_result'].empty:
                min_val = df['actual_result'].min()
                max_val = df['actual_result'].max()

                if min_val < 0 or max_val > 1:
                    quality_report['issues'].append(f'actual_result 值超出范围 [0,1]: [{min_val}, {max_val}]')
                    quality_report['status'] = 'error'

                unique_values = set(df['actual_result'].unique())
                if not unique_values.issubset({0, 1}):
                    invalid_values = unique_values - {0, 1}
                    quality_report['warnings'].append(f'actual_result 包含非0/1值: {invalid_values}')
                    if quality_report['status'] == 'good':
                        quality_report['status'] = 'warning'

            quality_report['statistics']['actual_result'] = {
                'null_count': int(null_count),
                'unique_values': list(df['actual_result'].unique()) if not df['actual_result'].empty else [],
                'value_counts': df['actual_result'].value_counts().to_dict() if not df['actual_result'].empty else {}
            }
        else:
            quality_report['issues'].append('缺少 actual_result 列')
            quality_report['status'] = 'error'

        # 检查数据量
        if len(df) < 100:
            quality_report['warnings'].append(f'数据量较少: {len(df)} 条')
            if quality_report['status'] == 'good':
                quality_report['status'] = 'warning'

        # 检查boot_id分布
        if 'boot_id' in df.columns:
            boot_count = df['boot_id'].nunique()
            quality_report['statistics']['boot_distribution'] = {
                'unique_boots': boot_count,
                'avg_records_per_boot': len(df) / boot_count if boot_count > 0 else 0,
                'boot_range': [int(df['boot_id'].min()), int(df['boot_id'].max())] if not df['boot_id'].empty else []
            }

            if boot_count < 10:
                quality_report['warnings'].append(f'靴数较少: {boot_count} 靴')
                if quality_report['status'] == 'good':
                    quality_report['status'] = 'warning'

        self.logger.info(f"数据质量检查完成: {quality_report['status']}")
        if quality_report['issues']:
            self.logger.warning(f"发现 {len(quality_report['issues'])} 个问题: {quality_report['issues']}")
        if quality_report['warnings']:
            self.logger.info(f"发现 {len(quality_report['warnings'])} 个警告: {quality_report['warnings']}")

        return quality_report
