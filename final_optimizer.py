#!/usr/bin/env python3
"""
最终优化器
稳定版本，专注于可靠的特征工程和超参数优化
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple
import json
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class FinalOptimizer:
    """最终优化器"""
    
    def __init__(self, target_accuracy: float = 0.60):
        self.logger = logging.getLogger(__name__)
        self.target_accuracy = target_accuracy
        self.current_best_accuracy = 0.568
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            # 添加全局序列号
            df['global_sequence'] = range(len(df))
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def create_stable_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建稳定的特征"""
        self.logger.info("🔧 创建稳定特征...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        df['strategy_sum'] = df[strategy_cols].sum(axis=1)
        
        # 1. 基础统计特征
        self.logger.info("   1️⃣ 基础统计特征...")
        df['strategy_mean'] = df[strategy_cols].mean(axis=1)
        df['strategy_std'] = df[strategy_cols].std(axis=1).fillna(0)
        df['strategy_min'] = df[strategy_cols].min(axis=1)
        df['strategy_max'] = df[strategy_cols].max(axis=1)
        df['strategy_range'] = df['strategy_max'] - df['strategy_min']
        df['strategy_median'] = df[strategy_cols].median(axis=1)
        
        # 投票特征
        df['majority_vote'] = (df['strategy_sum'] >= 4).astype(int)
        df['strong_majority'] = (df['strategy_sum'] >= 6).astype(int)
        df['unanimous_vote'] = (df['strategy_sum'] == 8).astype(int)
        df['consensus_strength'] = np.abs(df['strategy_sum'] - 4) / 4
        
        # 2. Boot内位置特征
        self.logger.info("   2️⃣ Boot内位置特征...")
        df['position_in_boot'] = df.groupby('boot_id').cumcount() + 1
        df['boot_size'] = df.groupby('boot_id')['boot_id'].transform('count')
        df['boot_progress'] = df['position_in_boot'] / df['boot_size']
        
        # 非线性位置特征
        df['boot_progress_squared'] = df['boot_progress'] ** 2
        df['boot_progress_sqrt'] = np.sqrt(df['boot_progress'])
        df['boot_progress_log'] = np.log1p(df['boot_progress'])
        
        # 3. 时间序列特征
        self.logger.info("   3️⃣ 时间序列特征...")
        
        # 指数加权移动平均
        alphas = [0.1, 0.2, 0.3]
        for alpha in alphas:
            for col in ['strategy_1', 'strategy_sum']:
                df[f'{col}_ewm_boot_{alpha}'] = df.groupby('boot_id')[col].ewm(alpha=alpha, adjust=False).mean().reset_index(0, drop=True)
                df[f'{col}_ewm_global_{alpha}'] = df[col].ewm(alpha=alpha, adjust=False).mean()
        
        # 滑动窗口特征
        windows = [3, 5, 10, 20]
        for window in windows:
            for col in ['strategy_1', 'strategy_sum']:
                df[f'{col}_ma_{window}'] = df[col].rolling(window=window, min_periods=1).mean()
                df[f'{col}_std_{window}'] = df[col].rolling(window=window, min_periods=1).std().fillna(0)
        
        # 4. 趋势特征
        self.logger.info("   4️⃣ 趋势特征...")
        
        trend_windows = [3, 5, 10]
        for window in trend_windows:
            for col in ['strategy_1', 'strategy_sum']:
                # 简单趋势
                df[f'{col}_trend_{window}'] = df.groupby('boot_id')[col].rolling(window=window, min_periods=1).apply(
                    lambda x: (x.iloc[-1] - x.iloc[0]) / len(x) if len(x) > 1 else 0
                ).reset_index(0, drop=True).fillna(0)
                
                # 动量
                df[f'{col}_momentum_{window}'] = df.groupby('boot_id')[col].rolling(window=window, min_periods=1).apply(
                    lambda x: x.iloc[-1] - x.iloc[0] if len(x) > 1 else 0
                ).reset_index(0, drop=True).fillna(0)
        
        # 5. 策略组合特征
        self.logger.info("   5️⃣ 策略组合特征...")
        
        # 基于相关性的分组
        high_corr = ['strategy_1', 'strategy_8']
        mid_corr = ['strategy_3', 'strategy_5', 'strategy_7']
        low_corr = ['strategy_2', 'strategy_4', 'strategy_6']
        
        df['high_corr_sum'] = df[high_corr].sum(axis=1)
        df['mid_corr_sum'] = df[mid_corr].sum(axis=1)
        df['low_corr_sum'] = df[low_corr].sum(axis=1)
        
        # 组间对比
        df['high_vs_low'] = df['high_corr_sum'] - df['low_corr_sum']
        df['high_vs_mid'] = df['high_corr_sum'] - df['mid_corr_sum']
        df['mid_vs_low'] = df['mid_corr_sum'] - df['low_corr_sum']
        
        # 6. 交互特征
        self.logger.info("   6️⃣ 交互特征...")
        
        # 位置交互
        df['progress_x_strategy_1'] = df['boot_progress'] * df['strategy_1']
        df['progress_x_strategy_sum'] = df['boot_progress'] * df['strategy_sum']
        df['progress_x_high_corr'] = df['boot_progress'] * df['high_corr_sum']
        
        # 趋势交互
        if 'strategy_1_trend_5' in df.columns:
            df['trend_x_progress'] = df['strategy_1_trend_5'] * df['boot_progress']
            df['trend_x_strategy'] = df['strategy_1_trend_5'] * df['strategy_1']
        
        # 7. 全局统计特征
        self.logger.info("   7️⃣ 全局统计特征...")
        
        global_windows = [50, 100, 200]
        for window in global_windows:
            for col in ['strategy_1', 'strategy_sum']:
                df[f'global_{col}_mean_{window}'] = df[col].rolling(window=window, min_periods=1).mean()
                df[f'global_{col}_std_{window}'] = df[col].rolling(window=window, min_periods=1).std().fillna(0)
        
        # 8. Boot级别统计
        self.logger.info("   8️⃣ Boot级别统计...")
        
        boot_stats = df.groupby('boot_id').agg({
            'strategy_1': ['mean', 'std'],
            'strategy_sum': ['mean', 'std'],
            'strategy_mean': ['mean', 'std']
        }).round(4)
        
        boot_stats.columns = ['_'.join(col).strip() for col in boot_stats.columns]
        boot_stats = boot_stats.add_prefix('boot_')
        
        df = df.merge(boot_stats, left_on='boot_id', right_index=True, how='left')
        
        # 9. 加权特征
        self.logger.info("   9️⃣ 加权特征...")
        
        # 基于相关性的权重
        weights = {
            'strategy_1': 0.147, 'strategy_8': 0.038, 'strategy_5': 0.014,
            'strategy_3': 0.007, 'strategy_7': 0.007, 'strategy_4': -0.016,
            'strategy_6': -0.005, 'strategy_2': -0.002
        }
        
        df['weighted_strategy_sum'] = sum(df[col] * weight for col, weight in weights.items())
        df['abs_weighted_strategy_sum'] = sum(df[col] * abs(weight) for col, weight in weights.items())
        
        # 10. 非线性变换
        self.logger.info("   🔟 非线性变换...")
        
        for col in ['strategy_1', 'strategy_sum']:
            df[f'{col}_squared'] = df[col] ** 2
            df[f'{col}_sqrt'] = np.sqrt(np.abs(df[col]))
            df[f'{col}_log'] = np.log1p(np.abs(df[col]))
        
        # 填充和清理
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(0)
        df[numeric_cols] = df[numeric_cols].replace([np.inf, -np.inf], 0)
        
        feature_count = len(df.columns) - len(strategy_cols) - 4
        self.logger.info(f"   ✅ 创建了 {feature_count} 个稳定特征")
        
        return df
    
    def comprehensive_optimization(self, df: pd.DataFrame) -> Dict[str, Any]:
        """综合优化"""
        try:
            from sklearn.model_selection import train_test_split, GridSearchCV, RandomizedSearchCV
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import accuracy_score, classification_report
            from sklearn.preprocessing import StandardScaler
            from sklearn.feature_selection import SelectKBest, f_classif
            import lightgbm as lgb
            
            self.logger.info("🎯 开始综合优化...")
            
            # 准备数据
            exclude_cols = ['id', 'boot_id', 'actual_result', 'global_sequence'] + [f'strategy_{i}' for i in range(1, 9)]
            feature_cols = [col for col in df.columns if col not in exclude_cols]
            
            # 移除可能的泄露特征
            clean_feature_cols = [col for col in feature_cols if 'actual_result' not in col.lower()]
            
            X = df[clean_feature_cols].values
            y = df['actual_result'].values
            
            self.logger.info(f"   📊 特征数: {len(clean_feature_cols)}")
            
            # 特征选择
            selector = SelectKBest(score_func=f_classif, k=min(50, len(clean_feature_cols)))
            X_selected = selector.fit_transform(X, y)
            selected_features = [clean_feature_cols[i] for i in selector.get_support(indices=True)]
            
            self.logger.info(f"   🎯 选择了 {len(selected_features)} 个最佳特征")
            
            # 特征标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_selected)
            
            # 按Boot分割数据
            unique_boots = df['boot_id'].unique()
            train_boots, test_boots = train_test_split(unique_boots, test_size=0.2, random_state=42)
            
            train_mask = df['boot_id'].isin(train_boots)
            test_mask = df['boot_id'].isin(test_boots)
            
            X_train, X_test = X_scaled[train_mask], X_scaled[test_mask]
            y_train, y_test = y[train_mask], y[test_mask]
            
            self.logger.info(f"   📊 训练样本: {len(X_train)}, 测试样本: {len(X_test)}")
            
            # 模型和参数网格
            models_params = {
                'logistic': {
                    'model': LogisticRegression(random_state=42, max_iter=2000),
                    'params': {
                        'C': [0.001, 0.01, 0.1, 1.0, 10.0],
                        'penalty': ['l1', 'l2'],
                        'solver': ['liblinear']
                    }
                },
                'random_forest': {
                    'model': RandomForestClassifier(random_state=42),
                    'params': {
                        'n_estimators': [100, 200, 300],
                        'max_depth': [5, 8, 10, None],
                        'min_samples_split': [2, 5, 10],
                        'min_samples_leaf': [1, 2, 5]
                    }
                },
                'lightgbm': {
                    'model': lgb.LGBMClassifier(random_state=42, verbose=-1),
                    'params': {
                        'n_estimators': [100, 200, 300],
                        'max_depth': [3, 5, 7],
                        'learning_rate': [0.01, 0.05, 0.1],
                        'subsample': [0.8, 0.9, 1.0],
                        'colsample_bytree': [0.8, 0.9, 1.0]
                    }
                }
            }
            
            best_models = {}
            best_scores = {}
            
            # 对每个模型进行网格搜索
            for name, config in models_params.items():
                self.logger.info(f"   🔄 优化 {name}...")
                
                # 使用随机搜索以节省时间
                search = RandomizedSearchCV(
                    config['model'],
                    config['params'],
                    n_iter=15,  # 减少迭代次数
                    cv=3,
                    scoring='accuracy',
                    random_state=42,
                    n_jobs=-1
                )
                
                search.fit(X_train, y_train)
                
                # 测试最佳模型
                best_model = search.best_estimator_
                y_pred = best_model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                best_models[name] = best_model
                best_scores[name] = accuracy
                
                self.logger.info(f"      ✅ {name}: {accuracy:.3f}")
                self.logger.info(f"         最佳参数: {search.best_params_}")
            
            # 创建集成模型
            self.logger.info("   🔗 创建优化集成...")
            
            # 选择最佳的2-3个模型进行集成
            sorted_models = sorted(best_scores.items(), key=lambda x: x[1], reverse=True)
            top_models = sorted_models[:3]
            
            ensemble = VotingClassifier(
                estimators=[(name, best_models[name]) for name, _ in top_models],
                voting='soft'
            )
            
            ensemble.fit(X_train, y_train)
            ensemble_pred = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
            
            self.logger.info(f"   🏆 集成模型: {ensemble_accuracy:.3f}")
            
            # 特征重要性分析
            feature_importance = None
            if 'lightgbm' in best_models:
                lgb_model = best_models['lightgbm']
                if hasattr(lgb_model, 'feature_importances_'):
                    importances = lgb_model.feature_importances_
                    feature_importance = dict(zip(selected_features, importances))
                    top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
                    
                    self.logger.info(f"\n   🎯 Top 10 重要特征:")
                    for i, (feature, importance) in enumerate(top_features):
                        self.logger.info(f"      {i+1:2d}. {feature}: {importance:.1f}")
            
            return {
                'best_models': best_models,
                'best_scores': best_scores,
                'ensemble': ensemble,
                'ensemble_accuracy': ensemble_accuracy,
                'selected_features': selected_features,
                'feature_importance': feature_importance,
                'top_models': [name for name, _ in top_models]
            }
            
        except Exception as e:
            self.logger.error(f"❌ 综合优化失败: {e}")
            import traceback
            traceback.print_exc()
            return {'ensemble_accuracy': 0.0, 'error': str(e)}
    
    def run_final_optimization(self):
        """运行最终优化"""
        self.logger.info("🚀 开始最终优化")
        self.logger.info(f"🎯 目标准确率: {self.target_accuracy:.1%}")
        self.logger.info(f"📊 当前基准: {self.current_best_accuracy:.3f}")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，优化终止")
            return
        
        # 2. 创建稳定特征
        df_features = self.create_stable_features(df)
        
        # 3. 综合优化
        result = self.comprehensive_optimization(df_features)
        
        # 4. 评估结果
        accuracy = result.get('ensemble_accuracy', 0.0)
        improvement = accuracy - self.current_best_accuracy
        
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 最终优化报告")
        self.logger.info("="*80)
        
        self.logger.info(f"\n📊 优化结果:")
        self.logger.info(f"   - 基准准确率: {self.current_best_accuracy:.3f}")
        self.logger.info(f"   - 最终准确率: {accuracy:.3f}")
        self.logger.info(f"   - 提升幅度: {improvement:.3f} ({improvement/self.current_best_accuracy*100:+.1f}%)")
        
        if 'best_scores' in result:
            self.logger.info(f"\n🤖 各模型表现:")
            for model_name, score in result['best_scores'].items():
                self.logger.info(f"   - {model_name}: {score:.3f}")
        
        if 'top_models' in result:
            self.logger.info(f"\n🏆 集成模型组合: {result['top_models']}")
        
        if accuracy >= self.target_accuracy:
            self.logger.info(f"\n🎉 成功达到目标准确率 {self.target_accuracy:.1%}!")
            self.logger.info("✅ V8系统已准备好投入生产！")
        else:
            gap = self.target_accuracy - accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f} ({gap/self.target_accuracy*100:.1f}%)")
            
            if improvement > 0:
                self.logger.info("✅ 优化有效果，建议继续探索更多特征工程方法")
            else:
                self.logger.info("⚠️ 可能已达到当前数据的信息上限")
        
        total_time = time.time() - start_time
        self.logger.info(f"\n⏱️ 总优化时间: {total_time:.1f}秒")
        
        # 保存最终结果
        result_file = Path("final_optimization_result.json")
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                'final_accuracy': accuracy,
                'improvement': improvement,
                'target_achieved': accuracy >= self.target_accuracy,
                'best_models': list(result.get('best_scores', {}).keys()),
                'ensemble_models': result.get('top_models', []),
                'feature_count': len(result.get('selected_features', [])),
                'optimization_time': total_time,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📄 最终结果已保存: {result_file}")
        self.logger.info("="*80)

if __name__ == "__main__":
    optimizer = FinalOptimizer(target_accuracy=0.60)
    optimizer.run_final_optimization()
