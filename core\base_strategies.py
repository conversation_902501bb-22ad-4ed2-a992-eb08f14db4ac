#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础策略层 (Layer 1)

实现V8系统的核心3策略：strategy_1, strategy_2, strategy_6
基于V6分析结果，这3个策略具有最佳的独立性和互补性
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from collections import deque
from dataclasses import dataclass
from abc import ABC, abstractmethod


@dataclass
class StrategyOutput:
    """策略输出数据结构"""
    strategy_name: str
    prediction: int          # 0 或 1
    confidence: float        # 置信度 0-1
    reasoning: str          # 决策理由
    timestamp: float        # 时间戳
    

@dataclass
class StrategyPerformance:
    """策略性能统计"""
    total_decisions: int = 0
    correct_decisions: int = 0
    accuracy: float = 0.0
    consecutive_errors: int = 0
    max_consecutive_errors: int = 0
    recent_accuracy: float = 0.0  # 近期准确率
    

class BaseStrategy(ABC):
    """基础策略抽象类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"Strategy.{name}")
        
        # 性能统计
        self.performance = StrategyPerformance()
        
        # 历史记录
        self.decision_history = deque(maxlen=1000)
        self.feedback_history = deque(maxlen=1000)
        
        # 权重 (会动态调整)
        self.current_weight = config.get('initial_weights', {}).get(name, 1.0)
        
    @abstractmethod
    def predict(self, context: Dict[str, Any]) -> StrategyOutput:
        """
        进行预测
        
        Args:
            context: 上下文信息
            
        Returns:
            策略输出
        """
        pass
    
    def update_feedback(self, decision_id: str, actual_result: int):
        """
        更新反馈信息
        
        Args:
            decision_id: 决策ID
            actual_result: 实际结果
        """
        # 查找对应的决策
        for decision in self.decision_history:
            if decision.get('decision_id') == decision_id:
                is_correct = (decision['prediction'] == actual_result)
                
                # 更新性能统计
                self.performance.total_decisions += 1
                if is_correct:
                    self.performance.correct_decisions += 1
                    self.performance.consecutive_errors = 0
                else:
                    self.performance.consecutive_errors += 1
                    self.performance.max_consecutive_errors = max(
                        self.performance.max_consecutive_errors,
                        self.performance.consecutive_errors
                    )
                
                # 更新准确率
                self.performance.accuracy = (
                    self.performance.correct_decisions / self.performance.total_decisions
                    if self.performance.total_decisions > 0 else 0.0
                )
                
                # 更新近期准确率 (最近100次)
                recent_decisions = list(self.feedback_history)[-100:]
                if recent_decisions:
                    recent_correct = sum(1 for d in recent_decisions if d['is_correct'])
                    self.performance.recent_accuracy = recent_correct / len(recent_decisions)
                
                # 记录反馈
                self.feedback_history.append({
                    'decision_id': decision_id,
                    'prediction': decision['prediction'],
                    'actual_result': actual_result,
                    'is_correct': is_correct,
                    'timestamp': decision['timestamp']
                })
                
                break
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            'name': self.name,
            'total_decisions': self.performance.total_decisions,
            'accuracy': self.performance.accuracy,
            'recent_accuracy': self.performance.recent_accuracy,
            'consecutive_errors': self.performance.consecutive_errors,
            'max_consecutive_errors': self.performance.max_consecutive_errors,
            'current_weight': self.current_weight
        }


class Strategy1(BaseStrategy):
    """
    Strategy 1 - 高胜率策略 (56.94%)
    
    基于V6分析，这是表现最好的策略，具有高胜率和强独立性
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("strategy_1", config)
        
        # Strategy 1 特有参数
        self.trend_sensitivity = 0.7
        self.pattern_memory = deque(maxlen=20)
        
    def predict(self, context: Dict[str, Any]) -> StrategyOutput:
        """Strategy 1 的预测逻辑"""
        import time
        
        # 获取历史数据
        history = context.get('history', [])
        current_boot = context.get('current_boot', {})
        
        # Strategy 1 的核心逻辑 (基于趋势分析)
        prediction = self._analyze_trend(history, current_boot)
        confidence = self._calculate_confidence(history)
        reasoning = f"基于趋势分析，预测结果为 {prediction}"
        
        # 创建输出
        output = StrategyOutput(
            strategy_name=self.name,
            prediction=prediction,
            confidence=confidence,
            reasoning=reasoning,
            timestamp=time.time()
        )
        
        # 记录决策
        decision_record = {
            'decision_id': context.get('decision_id', ''),
            'prediction': prediction,
            'confidence': confidence,
            'timestamp': output.timestamp
        }
        self.decision_history.append(decision_record)
        
        return output
    
    def _analyze_trend(self, history: List, current_boot: Dict) -> int:
        """分析趋势"""
        if not history:
            return np.random.choice([0, 1])  # 无历史数据时随机
        
        # 简化的趋势分析逻辑
        recent_results = history[-10:] if len(history) >= 10 else history
        
        if not recent_results:
            return np.random.choice([0, 1])
        
        # 计算最近结果的趋势
        ones_count = sum(1 for r in recent_results if r.get('result') == 1)
        trend_ratio = ones_count / len(recent_results)
        
        # 基于趋势反向预测 (反趋势策略)
        if trend_ratio > 0.6:
            return 0  # 如果最近1较多，预测0
        elif trend_ratio < 0.4:
            return 1  # 如果最近0较多，预测1
        else:
            return np.random.choice([0, 1])  # 平衡时随机
    
    def _calculate_confidence(self, history: List) -> float:
        """计算置信度"""
        base_confidence = 0.6  # Strategy 1 的基础置信度较高
        
        # 基于历史表现调整置信度
        if self.performance.recent_accuracy > 0.55:
            return min(0.9, base_confidence + 0.2)
        elif self.performance.recent_accuracy < 0.45:
            return max(0.3, base_confidence - 0.2)
        else:
            return base_confidence


class Strategy2(BaseStrategy):
    """
    Strategy 2 - 低相关性策略 (50.07%)
    
    基于V6分析，这个策略与其他策略相关性极低，具有很好的独立性
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("strategy_2", config)
        
        # Strategy 2 特有参数
        self.independence_factor = 0.8
        self.contrarian_tendency = 0.6
        
    def predict(self, context: Dict[str, Any]) -> StrategyOutput:
        """Strategy 2 的预测逻辑"""
        import time
        
        # 获取上下文
        history = context.get('history', [])
        other_strategies = context.get('other_strategies', {})
        
        # Strategy 2 的核心逻辑 (独立性分析)
        prediction = self._independent_analysis(history, other_strategies)
        confidence = self._calculate_confidence(other_strategies)
        reasoning = f"基于独立性分析，预测结果为 {prediction}"
        
        # 创建输出
        output = StrategyOutput(
            strategy_name=self.name,
            prediction=prediction,
            confidence=confidence,
            reasoning=reasoning,
            timestamp=time.time()
        )
        
        # 记录决策
        decision_record = {
            'decision_id': context.get('decision_id', ''),
            'prediction': prediction,
            'confidence': confidence,
            'timestamp': output.timestamp
        }
        self.decision_history.append(decision_record)
        
        return output
    
    def _independent_analysis(self, history: List, other_strategies: Dict) -> int:
        """独立性分析"""
        # Strategy 2 的特点是保持独立性，不受其他策略影响
        
        # 基于自身的逻辑进行预测
        if not history:
            return np.random.choice([0, 1])
        
        # 简化的独立分析逻辑
        recent_results = history[-5:] if len(history) >= 5 else history
        
        if not recent_results:
            return np.random.choice([0, 1])
        
        # 基于波动性的预测
        results = [r.get('result', 0) for r in recent_results]
        volatility = np.std(results) if len(results) > 1 else 0.5
        
        # 高波动时预测稳定，低波动时预测变化
        if volatility > 0.4:
            # 高波动，预测回归均值
            return 1 if np.mean(results) < 0.5 else 0
        else:
            # 低波动，预测突破
            return np.random.choice([0, 1])
    
    def _calculate_confidence(self, other_strategies: Dict) -> float:
        """计算置信度"""
        base_confidence = 0.5  # Strategy 2 的基础置信度中等
        
        # 基于与其他策略的分歧程度调整置信度
        if other_strategies:
            # 如果与其他策略分歧较大，置信度提高
            disagreement = self._calculate_disagreement(other_strategies)
            confidence_boost = disagreement * 0.2
            return min(0.8, base_confidence + confidence_boost)
        
        return base_confidence
    
    def _calculate_disagreement(self, other_strategies: Dict) -> float:
        """计算与其他策略的分歧程度"""
        if not other_strategies:
            return 0.0
        
        # 简化的分歧计算
        predictions = [s.get('prediction', 0) for s in other_strategies.values()]
        if not predictions:
            return 0.0
        
        avg_prediction = np.mean(predictions)
        # 分歧程度 = 与平均值的差异
        return abs(0.5 - avg_prediction)


class Strategy6(BaseStrategy):
    """
    Strategy 6 - 互补性策略 (49.98%)
    
    基于V6分析，虽然单独胜率不高，但与其他策略互补性很好
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("strategy_6", config)
        
        # Strategy 6 特有参数
        self.complementary_factor = 0.7
        self.adaptive_threshold = 0.5
        
    def predict(self, context: Dict[str, Any]) -> StrategyOutput:
        """Strategy 6 的预测逻辑"""
        import time
        
        # 获取上下文
        history = context.get('history', [])
        other_strategies = context.get('other_strategies', {})
        
        # Strategy 6 的核心逻辑 (互补性分析)
        prediction = self._complementary_analysis(history, other_strategies)
        confidence = self._calculate_confidence(other_strategies)
        reasoning = f"基于互补性分析，预测结果为 {prediction}"
        
        # 创建输出
        output = StrategyOutput(
            strategy_name=self.name,
            prediction=prediction,
            confidence=confidence,
            reasoning=reasoning,
            timestamp=time.time()
        )
        
        # 记录决策
        decision_record = {
            'decision_id': context.get('decision_id', ''),
            'prediction': prediction,
            'confidence': confidence,
            'timestamp': output.timestamp
        }
        self.decision_history.append(decision_record)
        
        return output
    
    def _complementary_analysis(self, history: List, other_strategies: Dict) -> int:
        """互补性分析"""
        # Strategy 6 的特点是在其他策略表现不佳时发挥作用
        
        if not other_strategies:
            return np.random.choice([0, 1])
        
        # 分析其他策略的一致性
        predictions = [s.get('prediction', 0) for s in other_strategies.values()]
        
        if not predictions:
            return np.random.choice([0, 1])
        
        # 如果其他策略高度一致，采用不同的预测
        consensus = np.mean(predictions)
        
        if consensus >= 0.8:  # 其他策略都预测1
            return 0  # 互补性预测0
        elif consensus <= 0.2:  # 其他策略都预测0
            return 1  # 互补性预测1
        else:
            # 其他策略分歧时，基于历史数据预测
            return self._historical_prediction(history)
    
    def _historical_prediction(self, history: List) -> int:
        """基于历史数据的预测"""
        if not history:
            return np.random.choice([0, 1])
        
        # 简单的历史模式分析
        recent_results = history[-8:] if len(history) >= 8 else history
        
        if not recent_results:
            return np.random.choice([0, 1])
        
        # 寻找模式
        results = [r.get('result', 0) for r in recent_results]
        
        # 简单的周期性检测
        if len(results) >= 4:
            # 检查是否有交替模式
            alternating = all(results[i] != results[i+1] for i in range(len(results)-1))
            if alternating:
                return 1 - results[-1]  # 继续交替模式
        
        # 默认预测
        return np.random.choice([0, 1])
    
    def _calculate_confidence(self, other_strategies: Dict) -> float:
        """计算置信度"""
        base_confidence = 0.45  # Strategy 6 的基础置信度较低
        
        # 基于互补性情况调整置信度
        if other_strategies:
            predictions = [s.get('prediction', 0) for s in other_strategies.values()]
            if predictions:
                consensus = np.mean(predictions)
                # 当其他策略高度一致时，互补性策略的置信度提高
                if consensus >= 0.8 or consensus <= 0.2:
                    return min(0.7, base_confidence + 0.2)
        
        return base_confidence


class BaseStrategyLayer:
    """
    基础策略层管理器
    
    管理3个核心策略的协调工作
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化3个核心策略
        self.strategies = {
            'strategy_1': Strategy1(config),
            'strategy_2': Strategy2(config),
            'strategy_6': Strategy6(config)
        }
        
        # 历史记录
        self.decision_history = deque(maxlen=1000)
        
        self.logger.info("基础策略层初始化完成")
    
    def get_strategy_outputs(self, external_data: Optional[Dict] = None) -> Dict[str, StrategyOutput]:
        """
        获取所有策略的输出
        
        Args:
            external_data: 外部数据
            
        Returns:
            策略输出字典
        """
        # 准备上下文信息
        context = {
            'history': list(self.decision_history),
            'decision_id': external_data.get('decision_id', '') if external_data else '',
            'current_boot': external_data.get('current_boot', {}) if external_data else {},
            'other_strategies': {}
        }
        
        outputs = {}
        
        # 依次获取每个策略的输出
        for name, strategy in self.strategies.items():
            # 为每个策略提供其他策略的信息
            context['other_strategies'] = {
                k: {'prediction': v.prediction} for k, v in outputs.items()
            }
            
            output = strategy.predict(context)
            outputs[name] = output
            
            self.logger.debug(f"{name} 预测: {output.prediction} (置信度: {output.confidence:.3f})")
        
        return outputs
    
    def update_feedback(self, decision_id: str, actual_result: int):
        """更新所有策略的反馈"""
        for strategy in self.strategies.values():
            strategy.update_feedback(decision_id, actual_result)
    
    def get_history(self) -> List[Dict]:
        """获取历史记录"""
        return list(self.decision_history)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {}
        for name, strategy in self.strategies.items():
            summary[name] = strategy.get_performance_stats()
        
        return summary
    
    def update_weights(self, new_weights: Dict[str, float]):
        """更新策略权重"""
        for name, weight in new_weights.items():
            if name in self.strategies:
                self.strategies[name].current_weight = weight
                self.logger.info(f"更新 {name} 权重为 {weight:.3f}")
