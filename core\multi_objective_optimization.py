#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多目标决策优化模块

实现多目标优化算法，平衡准确率、风险、收益等多个目标，支持帕累托最优解搜索
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
import time
from collections import deque
from abc import ABC, abstractmethod

try:
    from scipy.optimize import minimize
    from sklearn.metrics import accuracy_score, precision_score, recall_score
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False


@dataclass
class ObjectiveResult:
    """目标函数结果"""
    name: str
    value: float
    weight: float
    normalized_value: float
    is_maximization: bool


@dataclass
class MultiObjectiveSolution:
    """多目标优化解"""
    decision_variables: Dict[str, float]
    objectives: List[ObjectiveResult]
    total_score: float
    pareto_rank: int
    crowding_distance: float
    is_feasible: bool
    constraints_violation: float


@dataclass
class OptimizationConfig:
    """优化配置"""
    objectives: List[Dict[str, Any]]
    constraints: List[Dict[str, Any]]
    decision_variables: Dict[str, Dict[str, Any]]
    algorithm: str
    max_iterations: int
    population_size: int
    convergence_threshold: float


class ObjectiveFunction(ABC):
    """目标函数抽象基类"""
    
    def __init__(self, name: str, weight: float = 1.0, is_maximization: bool = True):
        self.name = name
        self.weight = weight
        self.is_maximization = is_maximization
        self.history = deque(maxlen=1000)
    
    @abstractmethod
    def evaluate(self, decision_variables: Dict[str, float], 
                context: Dict[str, Any]) -> float:
        """评估目标函数值"""
        pass
    
    def normalize(self, value: float) -> float:
        """归一化目标函数值"""
        if len(self.history) < 2:
            return value
        
        min_val = min(self.history)
        max_val = max(self.history)
        
        if max_val == min_val:
            return 0.5
        
        normalized = (value - min_val) / (max_val - min_val)
        return normalized if self.is_maximization else 1.0 - normalized


class AccuracyObjective(ObjectiveFunction):
    """准确率目标函数"""
    
    def __init__(self, weight: float = 1.0):
        super().__init__("accuracy", weight, is_maximization=True)
        self.prediction_history = deque(maxlen=100)
        self.actual_history = deque(maxlen=100)
    
    def evaluate(self, decision_variables: Dict[str, float], 
                context: Dict[str, Any]) -> float:
        """评估准确率"""
        # 基于历史预测准确率
        if len(self.prediction_history) < 10:
            return 0.5  # 默认准确率
        
        predictions = list(self.prediction_history)
        actuals = list(self.actual_history)
        
        # 计算准确率
        correct = sum(1 for p, a in zip(predictions, actuals) if p == a)
        accuracy = correct / len(predictions)
        
        # 考虑决策变量的影响
        confidence_threshold = decision_variables.get('confidence_threshold', 0.5)
        risk_tolerance = decision_variables.get('risk_tolerance', 0.5)
        
        # 调整准确率基于决策参数
        adjusted_accuracy = accuracy * (1 + 0.1 * confidence_threshold - 0.05 * risk_tolerance)
        
        self.history.append(adjusted_accuracy)
        return adjusted_accuracy
    
    def update_history(self, prediction: int, actual: int):
        """更新预测历史"""
        self.prediction_history.append(prediction)
        self.actual_history.append(actual)


class RiskObjective(ObjectiveFunction):
    """风险目标函数"""
    
    def __init__(self, weight: float = 1.0):
        super().__init__("risk", weight, is_maximization=False)
        self.loss_history = deque(maxlen=100)
        self.volatility_window = 20
    
    def evaluate(self, decision_variables: Dict[str, float], 
                context: Dict[str, Any]) -> float:
        """评估风险水平"""
        # 基于历史损失计算风险
        if len(self.loss_history) < 5:
            return 0.5  # 默认风险水平
        
        # 计算损失波动性
        recent_losses = list(self.loss_history)[-self.volatility_window:]
        volatility = np.std(recent_losses) if len(recent_losses) > 1 else 0.0
        
        # 计算最大回撤
        cumulative_returns = np.cumsum(recent_losses)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = running_max - cumulative_returns
        max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0.0
        
        # 决策变量影响
        risk_tolerance = decision_variables.get('risk_tolerance', 0.5)
        position_size = decision_variables.get('position_size', 0.5)
        
        # 综合风险评分
        risk_score = (volatility * 0.4 + max_drawdown * 0.4 + 
                     position_size * 0.2) * (2 - risk_tolerance)
        
        self.history.append(risk_score)
        return risk_score
    
    def update_loss(self, loss: float):
        """更新损失历史"""
        self.loss_history.append(loss)


class ProfitabilityObjective(ObjectiveFunction):
    """盈利能力目标函数"""
    
    def __init__(self, weight: float = 1.0):
        super().__init__("profitability", weight, is_maximization=True)
        self.return_history = deque(maxlen=100)
        self.win_rate_window = 50
    
    def evaluate(self, decision_variables: Dict[str, float], 
                context: Dict[str, Any]) -> float:
        """评估盈利能力"""
        if len(self.return_history) < 10:
            return 0.0  # 默认盈利能力
        
        # 计算胜率
        recent_returns = list(self.return_history)[-self.win_rate_window:]
        win_rate = sum(1 for r in recent_returns if r > 0) / len(recent_returns)
        
        # 计算平均收益
        avg_return = np.mean(recent_returns)
        
        # 计算夏普比率
        if np.std(recent_returns) > 0:
            sharpe_ratio = avg_return / np.std(recent_returns)
        else:
            sharpe_ratio = 0.0
        
        # 决策变量影响
        position_size = decision_variables.get('position_size', 0.5)
        confidence_threshold = decision_variables.get('confidence_threshold', 0.5)
        
        # 综合盈利能力评分
        profitability = (win_rate * 0.3 + avg_return * 0.4 + 
                        sharpe_ratio * 0.3) * position_size * confidence_threshold
        
        self.history.append(profitability)
        return profitability
    
    def update_return(self, return_value: float):
        """更新收益历史"""
        self.return_history.append(return_value)


class ConstraintFunction(ABC):
    """约束函数抽象基类"""
    
    def __init__(self, name: str, constraint_type: str = "inequality"):
        self.name = name
        self.constraint_type = constraint_type  # "equality" or "inequality"
    
    @abstractmethod
    def evaluate(self, decision_variables: Dict[str, float], 
                context: Dict[str, Any]) -> float:
        """评估约束函数值，返回值应该 <= 0 (不等式约束) 或 == 0 (等式约束)"""
        pass


class RiskLimitConstraint(ConstraintFunction):
    """风险限制约束"""
    
    def __init__(self, max_risk: float = 0.8):
        super().__init__("risk_limit", "inequality")
        self.max_risk = max_risk
    
    def evaluate(self, decision_variables: Dict[str, float], 
                context: Dict[str, Any]) -> float:
        """风险不能超过最大限制"""
        current_risk = context.get('current_risk', 0.5)
        risk_tolerance = decision_variables.get('risk_tolerance', 0.5)
        
        # 约束：当前风险 * (2 - 风险容忍度) <= 最大风险
        constraint_value = current_risk * (2 - risk_tolerance) - self.max_risk
        return constraint_value


class ConfidenceConstraint(ConstraintFunction):
    """置信度约束"""
    
    def __init__(self, min_confidence: float = 0.3):
        super().__init__("confidence_limit", "inequality")
        self.min_confidence = min_confidence
    
    def evaluate(self, decision_variables: Dict[str, float], 
                context: Dict[str, Any]) -> float:
        """置信度不能低于最小限制"""
        confidence_threshold = decision_variables.get('confidence_threshold', 0.5)
        
        # 约束：置信度阈值 >= 最小置信度
        constraint_value = self.min_confidence - confidence_threshold
        return constraint_value


class ParetoOptimizer:
    """帕累托优化器"""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 目标函数
        self.objectives = []
        self.constraints = []
        
        # 优化历史
        self.optimization_history = deque(maxlen=1000)
        self.pareto_front = []
        
        self._initialize_objectives()
        self._initialize_constraints()
    
    def _initialize_objectives(self):
        """初始化目标函数"""
        objective_map = {
            'accuracy': AccuracyObjective,
            'risk': RiskObjective,
            'profitability': ProfitabilityObjective
        }
        
        for obj_config in self.config.objectives:
            obj_type = obj_config['type']
            weight = obj_config.get('weight', 1.0)
            
            if obj_type in objective_map:
                objective = objective_map[obj_type](weight)
                self.objectives.append(objective)
            else:
                self.logger.warning(f"未知的目标函数类型: {obj_type}")
    
    def _initialize_constraints(self):
        """初始化约束函数"""
        constraint_map = {
            'risk_limit': RiskLimitConstraint,
            'confidence_limit': ConfidenceConstraint
        }
        
        for const_config in self.config.constraints:
            const_type = const_config['type']
            params = const_config.get('params', {})
            
            if const_type in constraint_map:
                constraint = constraint_map[const_type](**params)
                self.constraints.append(constraint)
            else:
                self.logger.warning(f"未知的约束函数类型: {const_type}")
    
    def evaluate_solution(self, decision_variables: Dict[str, float], 
                         context: Dict[str, Any]) -> MultiObjectiveSolution:
        """评估解的质量"""
        # 评估目标函数
        objective_results = []
        total_score = 0.0
        
        for objective in self.objectives:
            value = objective.evaluate(decision_variables, context)
            normalized_value = objective.normalize(value)
            
            objective_result = ObjectiveResult(
                name=objective.name,
                value=value,
                weight=objective.weight,
                normalized_value=normalized_value,
                is_maximization=objective.is_maximization
            )
            objective_results.append(objective_result)
            
            # 计算加权总分
            if objective.is_maximization:
                total_score += objective.weight * normalized_value
            else:
                total_score += objective.weight * (1.0 - normalized_value)
        
        # 评估约束
        constraints_violation = 0.0
        is_feasible = True
        
        for constraint in self.constraints:
            violation = constraint.evaluate(decision_variables, context)
            if violation > 0:
                constraints_violation += violation
                is_feasible = False
        
        # 创建解
        solution = MultiObjectiveSolution(
            decision_variables=decision_variables.copy(),
            objectives=objective_results,
            total_score=total_score,
            pareto_rank=0,  # 将在帕累托排序中设置
            crowding_distance=0.0,  # 将在拥挤距离计算中设置
            is_feasible=is_feasible,
            constraints_violation=constraints_violation
        )
        
        return solution
    
    def optimize(self, context: Dict[str, Any], 
                num_solutions: int = 50) -> List[MultiObjectiveSolution]:
        """执行多目标优化"""
        self.logger.info(f"开始多目标优化，生成 {num_solutions} 个解")
        
        # 生成初始解集
        solutions = self._generate_initial_solutions(num_solutions, context)
        
        # 进化优化（简化版NSGA-II）
        for iteration in range(self.config.max_iterations):
            # 评估所有解
            for solution in solutions:
                if solution.pareto_rank == 0:  # 重新评估
                    updated_solution = self.evaluate_solution(
                        solution.decision_variables, context
                    )
                    solution.objectives = updated_solution.objectives
                    solution.total_score = updated_solution.total_score
                    solution.is_feasible = updated_solution.is_feasible
                    solution.constraints_violation = updated_solution.constraints_violation
            
            # 帕累托排序
            solutions = self._pareto_ranking(solutions)
            
            # 拥挤距离计算
            solutions = self._crowding_distance(solutions)
            
            # 选择和变异（简化）
            if iteration < self.config.max_iterations - 1:
                solutions = self._evolve_solutions(solutions, context)
            
            if iteration % 10 == 0:
                pareto_count = sum(1 for s in solutions if s.pareto_rank == 1)
                self.logger.info(f"迭代 {iteration}: 帕累托前沿解数量 = {pareto_count}")
        
        # 更新帕累托前沿
        self.pareto_front = [s for s in solutions if s.pareto_rank == 1 and s.is_feasible]
        
        self.logger.info(f"优化完成，帕累托前沿包含 {len(self.pareto_front)} 个解")
        
        return self.pareto_front
    
    def _generate_initial_solutions(self, num_solutions: int, 
                                  context: Dict[str, Any]) -> List[MultiObjectiveSolution]:
        """生成初始解集"""
        solutions = []
        
        for _ in range(num_solutions):
            # 随机生成决策变量
            decision_variables = {}
            for var_name, var_config in self.config.decision_variables.items():
                min_val = var_config.get('min', 0.0)
                max_val = var_config.get('max', 1.0)
                value = np.random.uniform(min_val, max_val)
                decision_variables[var_name] = value
            
            # 评估解
            solution = self.evaluate_solution(decision_variables, context)
            solutions.append(solution)
        
        return solutions
    
    def _pareto_ranking(self, solutions: List[MultiObjectiveSolution]) -> List[MultiObjectiveSolution]:
        """帕累托排序"""
        # 简化的帕累托排序实现
        for i, solution_i in enumerate(solutions):
            rank = 1
            for j, solution_j in enumerate(solutions):
                if i != j and self._dominates(solution_j, solution_i):
                    rank += 1
            solution_i.pareto_rank = rank
        
        return sorted(solutions, key=lambda x: x.pareto_rank)
    
    def _dominates(self, solution_a: MultiObjectiveSolution, 
                  solution_b: MultiObjectiveSolution) -> bool:
        """判断解A是否支配解B"""
        if not solution_a.is_feasible and solution_b.is_feasible:
            return False
        if solution_a.is_feasible and not solution_b.is_feasible:
            return True
        
        better_in_any = False
        worse_in_any = False
        
        for obj_a, obj_b in zip(solution_a.objectives, solution_b.objectives):
            if obj_a.is_maximization:
                if obj_a.normalized_value > obj_b.normalized_value:
                    better_in_any = True
                elif obj_a.normalized_value < obj_b.normalized_value:
                    worse_in_any = True
            else:
                if obj_a.normalized_value < obj_b.normalized_value:
                    better_in_any = True
                elif obj_a.normalized_value > obj_b.normalized_value:
                    worse_in_any = True
        
        return better_in_any and not worse_in_any
    
    def _crowding_distance(self, solutions: List[MultiObjectiveSolution]) -> List[MultiObjectiveSolution]:
        """计算拥挤距离"""
        if len(solutions) <= 2:
            for solution in solutions:
                solution.crowding_distance = float('inf')
            return solutions
        
        # 按每个目标排序并计算拥挤距离
        for obj_idx in range(len(self.objectives)):
            solutions.sort(key=lambda x: x.objectives[obj_idx].normalized_value)
            
            # 边界解设置为无穷大
            solutions[0].crowding_distance = float('inf')
            solutions[-1].crowding_distance = float('inf')
            
            # 计算中间解的拥挤距离
            obj_range = (solutions[-1].objectives[obj_idx].normalized_value - 
                        solutions[0].objectives[obj_idx].normalized_value)
            
            if obj_range > 0:
                for i in range(1, len(solutions) - 1):
                    distance = (solutions[i+1].objectives[obj_idx].normalized_value - 
                              solutions[i-1].objectives[obj_idx].normalized_value) / obj_range
                    solutions[i].crowding_distance += distance
        
        return solutions
    
    def _evolve_solutions(self, solutions: List[MultiObjectiveSolution], 
                         context: Dict[str, Any]) -> List[MultiObjectiveSolution]:
        """进化解集（简化版）"""
        # 选择前50%的解
        num_keep = len(solutions) // 2
        selected = solutions[:num_keep]
        
        # 生成新解
        new_solutions = []
        for _ in range(len(solutions) - num_keep):
            # 随机选择两个父解
            parent1 = np.random.choice(selected)
            parent2 = np.random.choice(selected)
            
            # 交叉和变异
            child_vars = {}
            for var_name in self.config.decision_variables.keys():
                # 简单的算术交叉
                alpha = np.random.random()
                child_value = (alpha * parent1.decision_variables[var_name] + 
                             (1 - alpha) * parent2.decision_variables[var_name])
                
                # 变异
                if np.random.random() < 0.1:  # 10%变异概率
                    var_config = self.config.decision_variables[var_name]
                    min_val = var_config.get('min', 0.0)
                    max_val = var_config.get('max', 1.0)
                    mutation = np.random.normal(0, 0.1)
                    child_value = np.clip(child_value + mutation, min_val, max_val)
                
                child_vars[var_name] = child_value
            
            # 评估新解
            child_solution = self.evaluate_solution(child_vars, context)
            new_solutions.append(child_solution)
        
        return selected + new_solutions
    
    def get_best_solution(self, preference_weights: Optional[Dict[str, float]] = None) -> MultiObjectiveSolution:
        """根据偏好权重获取最佳解"""
        if not self.pareto_front:
            return None
        
        if preference_weights is None:
            # 返回总分最高的解
            return max(self.pareto_front, key=lambda x: x.total_score)
        
        # 根据偏好权重计算加权分数
        best_solution = None
        best_score = float('-inf')
        
        for solution in self.pareto_front:
            weighted_score = 0.0
            for objective in solution.objectives:
                weight = preference_weights.get(objective.name, 1.0)
                if objective.is_maximization:
                    weighted_score += weight * objective.normalized_value
                else:
                    weighted_score += weight * (1.0 - objective.normalized_value)
            
            if weighted_score > best_score:
                best_score = weighted_score
                best_solution = solution
        
        return best_solution
    
    def update_objectives_history(self, prediction: int, actual: int, 
                                 return_value: float, loss: float):
        """更新目标函数历史数据"""
        for objective in self.objectives:
            if isinstance(objective, AccuracyObjective):
                objective.update_history(prediction, actual)
            elif isinstance(objective, ProfitabilityObjective):
                objective.update_return(return_value)
            elif isinstance(objective, RiskObjective):
                objective.update_loss(loss)
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化总结"""
        if not self.pareto_front:
            return {'pareto_front_size': 0}
        
        # 统计帕累托前沿
        objective_stats = {}
        for obj_name in [obj.name for obj in self.objectives]:
            values = [sol.objectives[i].value for sol in self.pareto_front 
                     for i, obj in enumerate(sol.objectives) if obj.name == obj_name]
            if values:
                objective_stats[obj_name] = {
                    'min': min(values),
                    'max': max(values),
                    'mean': np.mean(values),
                    'std': np.std(values)
                }
        
        return {
            'pareto_front_size': len(self.pareto_front),
            'objective_statistics': objective_stats,
            'feasible_solutions': sum(1 for s in self.pareto_front if s.is_feasible),
            'optimization_iterations': self.config.max_iterations
        }


class MultiObjectiveDecisionManager:
    """多目标决策管理器"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化多目标决策管理器

        Args:
            config: 配置字典
        """
        self.config = config.get('multi_objective', {})
        self.logger = logging.getLogger(__name__)

        # 创建优化配置
        self.optimization_config = self._create_optimization_config()

        # 创建优化器
        self.optimizer = ParetoOptimizer(self.optimization_config)

        # 决策历史
        self.decision_history = deque(maxlen=1000)
        self.performance_history = deque(maxlen=500)

        # 当前最优解
        self.current_best_solution = None
        self.last_optimization_time = 0
        self.optimization_frequency = self.config.get('optimization_frequency', 100)

        self.logger.info("多目标决策管理器初始化完成")

    def _create_optimization_config(self) -> OptimizationConfig:
        """创建优化配置"""
        # 默认目标函数配置
        default_objectives = [
            {'type': 'accuracy', 'weight': 1.0},
            {'type': 'risk', 'weight': 0.8},
            {'type': 'profitability', 'weight': 1.2}
        ]

        # 默认约束配置
        default_constraints = [
            {'type': 'risk_limit', 'params': {'max_risk': 0.7}},
            {'type': 'confidence_limit', 'params': {'min_confidence': 0.3}}
        ]

        # 默认决策变量配置
        default_variables = {
            'confidence_threshold': {'min': 0.3, 'max': 0.9},
            'risk_tolerance': {'min': 0.1, 'max': 1.0},
            'position_size': {'min': 0.1, 'max': 1.0},
            'strategy_weight_1': {'min': 0.0, 'max': 1.0},
            'strategy_weight_2': {'min': 0.0, 'max': 1.0},
            'strategy_weight_6': {'min': 0.0, 'max': 1.0}
        }

        return OptimizationConfig(
            objectives=self.config.get('objectives', default_objectives),
            constraints=self.config.get('constraints', default_constraints),
            decision_variables=self.config.get('decision_variables', default_variables),
            algorithm=self.config.get('algorithm', 'nsga2'),
            max_iterations=self.config.get('max_iterations', 50),
            population_size=self.config.get('population_size', 30),
            convergence_threshold=self.config.get('convergence_threshold', 1e-6)
        )

    def should_optimize(self, decision_count: int) -> bool:
        """判断是否应该进行优化"""
        # 基于决策数量
        if decision_count - self.last_optimization_time >= self.optimization_frequency:
            return True

        # 基于性能下降
        if len(self.performance_history) >= 20:
            recent_performance = list(self.performance_history)[-10:]
            older_performance = list(self.performance_history)[-20:-10]

            recent_avg = np.mean(recent_performance)
            older_avg = np.mean(older_performance)

            # 如果性能下降超过10%，触发优化
            if recent_avg < older_avg - 0.1:
                return True

        return False

    def optimize_decision_parameters(self, context: Dict[str, Any]) -> MultiObjectiveSolution:
        """优化决策参数"""
        self.logger.info("开始多目标决策参数优化")

        # 更新上下文信息
        enhanced_context = self._enhance_context(context)

        # 执行优化
        pareto_front = self.optimizer.optimize(enhanced_context)

        if pareto_front:
            # 根据当前偏好选择最佳解
            preference_weights = self._get_current_preferences()
            best_solution = self.optimizer.get_best_solution(preference_weights)

            if best_solution:
                self.current_best_solution = best_solution
                self.last_optimization_time = time.time()

                self.logger.info(f"优化完成，选择解的总分: {best_solution.total_score:.4f}")
                self.logger.info(f"决策参数: {best_solution.decision_variables}")

                return best_solution

        self.logger.warning("优化失败，使用默认参数")
        return self._get_default_solution()

    def _enhance_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """增强上下文信息"""
        enhanced = context.copy()

        # 添加历史性能信息
        if self.performance_history:
            enhanced['recent_performance'] = np.mean(list(self.performance_history)[-10:])
            enhanced['performance_trend'] = self._calculate_performance_trend()

        # 添加当前风险水平
        enhanced['current_risk'] = self._calculate_current_risk()

        # 添加市场状态信息
        enhanced['market_volatility'] = self._estimate_market_volatility()

        return enhanced

    def _get_current_preferences(self) -> Dict[str, float]:
        """获取当前偏好权重"""
        # 基于最近性能动态调整偏好
        preferences = {'accuracy': 1.0, 'risk': 0.8, 'profitability': 1.2}

        if len(self.performance_history) >= 10:
            recent_performance = np.mean(list(self.performance_history)[-10:])

            if recent_performance < 0.5:
                # 性能较差时，更重视准确率
                preferences['accuracy'] = 1.5
                preferences['risk'] = 1.2
                preferences['profitability'] = 0.8
            elif recent_performance > 0.7:
                # 性能较好时，更重视盈利能力
                preferences['accuracy'] = 0.8
                preferences['risk'] = 0.6
                preferences['profitability'] = 1.5

        return preferences

    def _calculate_performance_trend(self) -> float:
        """计算性能趋势"""
        if len(self.performance_history) < 10:
            return 0.0

        recent_data = list(self.performance_history)[-10:]
        x = np.arange(len(recent_data))

        # 简单线性回归计算趋势
        slope = np.polyfit(x, recent_data, 1)[0]
        return slope

    def _calculate_current_risk(self) -> float:
        """计算当前风险水平"""
        if len(self.decision_history) < 5:
            return 0.5

        # 基于最近决策的风险评估
        recent_decisions = list(self.decision_history)[-10:]
        risk_scores = []

        for decision in recent_decisions:
            # 基于决策的置信度和结果计算风险
            confidence = decision.get('confidence', 0.5)
            actual_result = decision.get('actual_result')
            prediction = decision.get('prediction')

            if actual_result is not None and prediction is not None:
                is_correct = (prediction == actual_result)
                risk_score = (1 - confidence) * (0.5 if is_correct else 1.5)
                risk_scores.append(risk_score)

        return np.mean(risk_scores) if risk_scores else 0.5

    def _estimate_market_volatility(self) -> float:
        """估计市场波动性"""
        if len(self.performance_history) < 10:
            return 0.5

        # 基于性能历史估计波动性
        recent_performance = list(self.performance_history)[-20:]
        volatility = np.std(recent_performance)

        # 归一化到0-1范围
        return min(1.0, volatility * 2)

    def _get_default_solution(self) -> MultiObjectiveSolution:
        """获取默认解"""
        default_variables = {
            'confidence_threshold': 0.5,
            'risk_tolerance': 0.5,
            'position_size': 0.5,
            'strategy_weight_1': 0.33,
            'strategy_weight_2': 0.33,
            'strategy_weight_6': 0.34
        }

        # 创建默认目标结果
        default_objectives = [
            ObjectiveResult("accuracy", 0.5, 1.0, 0.5, True),
            ObjectiveResult("risk", 0.5, 0.8, 0.5, False),
            ObjectiveResult("profitability", 0.0, 1.2, 0.0, True)
        ]

        return MultiObjectiveSolution(
            decision_variables=default_variables,
            objectives=default_objectives,
            total_score=1.0,
            pareto_rank=1,
            crowding_distance=0.0,
            is_feasible=True,
            constraints_violation=0.0
        )

    def apply_solution(self, solution: MultiObjectiveSolution,
                      decision_context: Dict[str, Any]) -> Dict[str, Any]:
        """应用优化解到决策上下文"""
        if not solution:
            solution = self._get_default_solution()

        # 更新决策参数
        updated_context = decision_context.copy()

        # 应用决策变量
        updated_context['confidence_threshold'] = solution.decision_variables.get('confidence_threshold', 0.5)
        updated_context['risk_tolerance'] = solution.decision_variables.get('risk_tolerance', 0.5)
        updated_context['position_size'] = solution.decision_variables.get('position_size', 0.5)

        # 应用策略权重
        strategy_weights = {
            'strategy_1': solution.decision_variables.get('strategy_weight_1', 0.33),
            'strategy_2': solution.decision_variables.get('strategy_weight_2', 0.33),
            'strategy_6': solution.decision_variables.get('strategy_weight_6', 0.34)
        }

        # 归一化权重
        total_weight = sum(strategy_weights.values())
        if total_weight > 0:
            strategy_weights = {k: v/total_weight for k, v in strategy_weights.items()}

        updated_context['strategy_weights'] = strategy_weights
        updated_context['optimization_solution'] = solution

        return updated_context

    def update_feedback(self, decision_id: str, prediction: int, actual_result: int,
                       return_value: float = 0.0, loss: float = 0.0):
        """更新反馈信息"""
        # 更新决策历史
        decision_record = {
            'decision_id': decision_id,
            'prediction': prediction,
            'actual_result': actual_result,
            'return_value': return_value,
            'loss': loss,
            'timestamp': time.time()
        }
        self.decision_history.append(decision_record)

        # 更新性能历史
        is_correct = (prediction == actual_result)
        self.performance_history.append(1.0 if is_correct else 0.0)

        # 更新目标函数历史
        self.optimizer.update_objectives_history(prediction, actual_result, return_value, loss)

    def get_current_parameters(self) -> Dict[str, float]:
        """获取当前决策参数"""
        if self.current_best_solution:
            return self.current_best_solution.decision_variables.copy()
        else:
            return self._get_default_solution().decision_variables.copy()

    def get_optimization_status(self) -> Dict[str, Any]:
        """获取优化状态"""
        status = {
            'has_current_solution': self.current_best_solution is not None,
            'last_optimization_time': self.last_optimization_time,
            'optimization_frequency': self.optimization_frequency,
            'decision_history_length': len(self.decision_history),
            'performance_history_length': len(self.performance_history)
        }

        if self.current_best_solution:
            status['current_solution'] = {
                'total_score': self.current_best_solution.total_score,
                'pareto_rank': self.current_best_solution.pareto_rank,
                'is_feasible': self.current_best_solution.is_feasible,
                'decision_variables': self.current_best_solution.decision_variables,
                'objectives': [
                    {
                        'name': obj.name,
                        'value': obj.value,
                        'normalized_value': obj.normalized_value,
                        'weight': obj.weight
                    }
                    for obj in self.current_best_solution.objectives
                ]
            }

        # 添加优化器状态
        optimizer_summary = self.optimizer.get_optimization_summary()
        status['optimizer'] = optimizer_summary

        return status
