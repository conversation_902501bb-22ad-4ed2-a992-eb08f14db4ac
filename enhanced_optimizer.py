#!/usr/bin/env python3
"""
增强版自动优化器
第二轮优化，集成更多高级特征工程和超参数优化
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
import json
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class EnhancedOptimizer:
    """增强版自动优化器"""
    
    def __init__(self, target_accuracy: float = 0.60):
        self.logger = logging.getLogger(__name__)
        self.target_accuracy = target_accuracy
        self.current_best_accuracy = 0.568  # 从第一轮的最佳结果开始
        
        # 创建结果目录
        self.results_dir = Path("enhanced_optimization_results")
        self.results_dir.mkdir(exist_ok=True)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            # 添加全局序列号
            df['global_sequence'] = range(len(df))
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def create_ultra_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建超级特征工程"""
        self.logger.info("🚀 创建超级特征工程...")

        # 确保原始策略列存在
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        missing_cols = [col for col in strategy_cols if col not in df.columns]
        if missing_cols:
            self.logger.error(f"❌ 缺失关键策略列: {missing_cols}")
            return df

        df['strategy_sum'] = df[strategy_cols].sum(axis=1)
        
        # 1. 基础特征
        self.logger.info("   1️⃣ 基础特征...")
        df['strategy_mean'] = df[strategy_cols].mean(axis=1)
        df['strategy_std'] = df[strategy_cols].std(axis=1).fillna(0)
        df['strategy_skew'] = df[strategy_cols].skew(axis=1).fillna(0)
        df['strategy_kurt'] = df[strategy_cols].kurtosis(axis=1).fillna(0)
        
        # 投票特征
        df['majority_vote'] = (df['strategy_sum'] >= 4).astype(int)
        df['strong_majority'] = (df['strategy_sum'] >= 6).astype(int)
        df['consensus_strength'] = np.abs(df['strategy_sum'] - 4) / 4
        
        # 2. 高级时间序列特征
        self.logger.info("   2️⃣ 高级时间序列特征...")
        
        # Boot内位置特征
        df['position_in_boot'] = df.groupby('boot_id').cumcount() + 1
        df['boot_size'] = df.groupby('boot_id')['boot_id'].transform('count')
        df['boot_progress'] = df['position_in_boot'] / df['boot_size']
        
        # 多种指数加权移动平均
        alphas = [0.1, 0.2, 0.3, 0.5]
        for alpha in alphas:
            for col in ['strategy_1', 'strategy_sum']:
                df[f'{col}_ewm_boot_{alpha}'] = df.groupby('boot_id')[col].ewm(alpha=alpha, adjust=False).mean().reset_index(0, drop=True)
                df[f'{col}_ewm_global_{alpha}'] = df[col].ewm(alpha=alpha, adjust=False).mean()
        
        # 多窗口滑动特征
        windows = [3, 5, 7, 10, 15, 20]
        for window in windows:
            for col in ['strategy_1', 'strategy_sum']:
                df[f'{col}_ma_{window}'] = df[col].rolling(window=window, min_periods=1).mean()
                df[f'{col}_std_{window}'] = df[col].rolling(window=window, min_periods=1).std().fillna(0)
                df[f'{col}_min_{window}'] = df[col].rolling(window=window, min_periods=1).min()
                df[f'{col}_max_{window}'] = df[col].rolling(window=window, min_periods=1).max()
        
        # 3. 趋势和动量特征
        self.logger.info("   3️⃣ 趋势和动量特征...")
        
        trend_windows = [3, 5, 7, 10]
        for window in trend_windows:
            for col in ['strategy_1', 'strategy_sum']:
                # 线性趋势
                df[f'{col}_trend_{window}'] = df.groupby('boot_id')[col].rolling(window=window, min_periods=1).apply(
                    lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) > 1 else 0
                ).reset_index(0, drop=True).fillna(0)
                
                # 动量
                df[f'{col}_momentum_{window}'] = df.groupby('boot_id')[col].rolling(window=window, min_periods=1).apply(
                    lambda x: x.iloc[-1] - x.iloc[0] if len(x) > 1 else 0
                ).reset_index(0, drop=True).fillna(0)
                
                # 加速度
                df[f'{col}_accel_{window}'] = df.groupby('boot_id')[f'{col}_trend_{window}'].diff().fillna(0)
        
        # 4. 频域特征
        self.logger.info("   4️⃣ 频域特征...")
        
        try:
            from scipy.fft import fft
            from scipy.signal import periodogram
            
            # FFT特征
            def compute_fft_features(group, col_name):
                if len(group) >= 8:
                    fft_result = fft(group.values)
                    amplitudes = np.abs(fft_result[:4])
                    phases = np.angle(fft_result[:4])
                    
                    features = {}
                    for i in range(4):
                        features[f'{col_name}_fft_amp_{i}'] = amplitudes[i]
                        features[f'{col_name}_fft_phase_{i}'] = phases[i]
                    
                    # 功率谱密度
                    freqs, psd = periodogram(group.values)
                    features[f'{col_name}_psd_peak'] = np.max(psd) if len(psd) > 0 else 0
                    features[f'{col_name}_psd_mean'] = np.mean(psd) if len(psd) > 0 else 0
                    
                    return pd.Series(features)
                else:
                    # 返回零值
                    features = {}
                    for i in range(4):
                        features[f'{col_name}_fft_amp_{i}'] = 0
                        features[f'{col_name}_fft_phase_{i}'] = 0
                    features[f'{col_name}_psd_peak'] = 0
                    features[f'{col_name}_psd_mean'] = 0
                    return pd.Series(features)
            
            # 对每个Boot计算FFT特征
            for col in ['strategy_1', 'strategy_sum']:
                fft_features = df.groupby('boot_id')[col].apply(lambda x: compute_fft_features(x, col)).reset_index()
                df = df.merge(fft_features, on='boot_id', how='left')
                
        except ImportError:
            self.logger.warning("   ⚠️ scipy未安装，跳过频域特征")
        
        # 5. 策略组合和交互特征
        self.logger.info("   5️⃣ 策略组合和交互特征...")

        # 确保原始策略列存在
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        missing_cols = [col for col in strategy_cols if col not in df.columns]
        if missing_cols:
            self.logger.warning(f"   ⚠️ 缺失策略列: {missing_cols}")
            return df

        # 基于相关性的策略分组
        high_corr = ['strategy_1', 'strategy_8']
        mid_corr = ['strategy_3', 'strategy_5', 'strategy_7']
        low_corr = ['strategy_2', 'strategy_4', 'strategy_6']
        
        df['high_corr_sum'] = df[high_corr].sum(axis=1)
        df['mid_corr_sum'] = df[mid_corr].sum(axis=1)
        df['low_corr_sum'] = df[low_corr].sum(axis=1)
        
        # 组间对比
        df['high_vs_low'] = df['high_corr_sum'] - df['low_corr_sum']
        df['high_vs_mid'] = df['high_corr_sum'] - df['mid_corr_sum']
        df['mid_vs_low'] = df['mid_corr_sum'] - df['low_corr_sum']
        
        # 策略比率
        df['s1_ratio'] = df['strategy_1'] / (df['strategy_sum'] + 1e-8)
        df['high_ratio'] = df['high_corr_sum'] / (df['strategy_sum'] + 1e-8)
        
        # 6. 非线性变换
        self.logger.info("   6️⃣ 非线性变换...")
        
        for col in ['strategy_1', 'strategy_sum', 'boot_progress']:
            df[f'{col}_squared'] = df[col] ** 2
            df[f'{col}_sqrt'] = np.sqrt(np.abs(df[col]))
            df[f'{col}_log'] = np.log1p(np.abs(df[col]))
            df[f'{col}_exp'] = np.exp(df[col] / 10)  # 缩放避免溢出
        
        # 7. 交互特征
        self.logger.info("   7️⃣ 交互特征...")
        
        # 位置交互
        df['progress_x_s1'] = df['boot_progress'] * df['strategy_1']
        df['progress_x_sum'] = df['boot_progress'] * df['strategy_sum']
        df['progress_x_high'] = df['boot_progress'] * df['high_corr_sum']
        
        # 趋势交互
        if 'strategy_1_trend_5' in df.columns:
            df['trend_x_progress'] = df['strategy_1_trend_5'] * df['boot_progress']
            df['trend_x_s1'] = df['strategy_1_trend_5'] * df['strategy_1']
        
        # 8. 全局统计特征
        self.logger.info("   8️⃣ 全局统计特征...")
        
        global_windows = [30, 50, 100, 200, 300]
        for window in global_windows:
            for col in ['strategy_1', 'strategy_sum']:
                df[f'global_{col}_mean_{window}'] = df[col].rolling(window=window, min_periods=1).mean()
                df[f'global_{col}_std_{window}'] = df[col].rolling(window=window, min_periods=1).std().fillna(0)
                df[f'global_{col}_quantile25_{window}'] = df[col].rolling(window=window, min_periods=1).quantile(0.25)
                df[f'global_{col}_quantile75_{window}'] = df[col].rolling(window=window, min_periods=1).quantile(0.75)
        
        # 9. Boot级别统计
        self.logger.info("   9️⃣ Boot级别统计...")
        
        boot_stats = df.groupby('boot_id').agg({
            'strategy_1': ['mean', 'std', 'min', 'max', 'skew'],
            'strategy_sum': ['mean', 'std', 'min', 'max', 'skew'],
            'strategy_mean': ['mean', 'std']
        }).round(4)
        
        boot_stats.columns = ['_'.join(col).strip() for col in boot_stats.columns]
        boot_stats = boot_stats.add_prefix('boot_')
        
        df = df.merge(boot_stats, left_on='boot_id', right_index=True, how='left')
        
        # 10. 时间衰减和权重特征
        self.logger.info("   🔟 时间衰减和权重特征...")
        
        # 多种衰减率
        decay_rates = [0.05, 0.1, 0.2]
        for rate in decay_rates:
            df[f'time_decay_boot_{rate}'] = np.exp(-rate * (df['boot_size'] - df['position_in_boot']))
            df[f'time_weighted_s1_{rate}'] = df['strategy_1'] * df[f'time_decay_boot_{rate}']
        
        # 填充和清理
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(0)
        df[numeric_cols] = df[numeric_cols].replace([np.inf, -np.inf], 0)
        
        feature_count = len(df.columns) - len(strategy_cols) - 4
        self.logger.info(f"   ✅ 创建了 {feature_count} 个超级特征")
        
        return df
    
    def hyperparameter_optimization(self, df: pd.DataFrame) -> Dict[str, Any]:
        """超参数优化"""
        try:
            from sklearn.model_selection import train_test_split, RandomizedSearchCV
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import accuracy_score
            from sklearn.preprocessing import StandardScaler
            import lightgbm as lgb
            
            self.logger.info("🎯 开始超参数优化...")
            
            # 准备数据
            exclude_cols = ['id', 'boot_id', 'actual_result', 'global_sequence'] + [f'strategy_{i}' for i in range(1, 9)]
            feature_cols = [col for col in df.columns if col not in exclude_cols]
            
            # 移除可能的泄露特征
            clean_feature_cols = [col for col in feature_cols if 'actual_result' not in col.lower()]
            
            X = df[clean_feature_cols].values
            y = df['actual_result'].values
            
            # 特征标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 按Boot分割数据
            unique_boots = df['boot_id'].unique()
            train_boots, test_boots = train_test_split(unique_boots, test_size=0.2, random_state=42)
            
            train_mask = df['boot_id'].isin(train_boots)
            test_mask = df['boot_id'].isin(test_boots)
            
            X_train, X_test = X_scaled[train_mask], X_scaled[test_mask]
            y_train, y_test = y[train_mask], y[test_mask]
            
            self.logger.info(f"   📊 特征数: {len(clean_feature_cols)}, 训练样本: {len(X_train)}")
            
            # 定义超参数搜索空间
            param_distributions = {
                'logistic': {
                    'C': [0.001, 0.01, 0.1, 1.0, 10.0],
                    'penalty': ['l1', 'l2'],
                    'solver': ['liblinear', 'saga'],
                    'max_iter': [1000, 2000, 3000]
                },
                'random_forest': {
                    'n_estimators': [100, 200, 300, 500],
                    'max_depth': [5, 8, 10, 12, None],
                    'min_samples_split': [2, 5, 10, 20],
                    'min_samples_leaf': [1, 2, 5, 10],
                    'max_features': ['sqrt', 'log2', None]
                },
                'lightgbm': {
                    'n_estimators': [100, 200, 300, 500],
                    'max_depth': [3, 5, 7, 9],
                    'learning_rate': [0.01, 0.05, 0.1, 0.2],
                    'subsample': [0.7, 0.8, 0.9, 1.0],
                    'colsample_bytree': [0.7, 0.8, 0.9, 1.0],
                    'min_child_samples': [10, 20, 30, 50]
                }
            }
            
            # 基础模型
            base_models = {
                'logistic': LogisticRegression(random_state=42),
                'random_forest': RandomForestClassifier(random_state=42),
                'lightgbm': lgb.LGBMClassifier(random_state=42, verbose=-1)
            }
            
            best_models = {}
            best_scores = {}
            
            # 对每个模型进行随机搜索
            for name, model in base_models.items():
                self.logger.info(f"   🔄 优化 {name}...")
                
                random_search = RandomizedSearchCV(
                    model,
                    param_distributions[name],
                    n_iter=20,  # 减少迭代次数以节省时间
                    cv=3,
                    scoring='accuracy',
                    random_state=42,
                    n_jobs=-1
                )
                
                random_search.fit(X_train, y_train)
                
                # 测试最佳模型
                best_model = random_search.best_estimator_
                y_pred = best_model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                best_models[name] = best_model
                best_scores[name] = accuracy
                
                self.logger.info(f"      ✅ {name}: {accuracy:.3f} (参数: {random_search.best_params_})")
            
            # 选择最佳模型
            best_model_name = max(best_scores, key=best_scores.get)
            best_accuracy = best_scores[best_model_name]
            
            self.logger.info(f"   🏆 最佳模型: {best_model_name} ({best_accuracy:.3f})")
            
            return {
                'best_model': best_models[best_model_name],
                'best_model_name': best_model_name,
                'best_accuracy': best_accuracy,
                'all_scores': best_scores,
                'feature_count': len(clean_feature_cols)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 超参数优化失败: {e}")
            import traceback
            traceback.print_exc()
            return {'best_accuracy': 0.0, 'error': str(e)}
    
    def run_enhanced_optimization(self):
        """运行增强优化"""
        self.logger.info("🚀 开始增强版自动优化")
        self.logger.info(f"🎯 目标准确率: {self.target_accuracy:.1%}")
        self.logger.info(f"📊 当前基准: {self.current_best_accuracy:.3f}")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，优化终止")
            return
        
        # 2. 创建超级特征
        df_ultra = self.create_ultra_features(df)
        
        # 3. 超参数优化
        result = self.hyperparameter_optimization(df_ultra)
        
        # 4. 评估结果
        accuracy = result.get('best_accuracy', 0.0)
        improvement = accuracy - self.current_best_accuracy
        
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 增强优化最终报告")
        self.logger.info("="*80)
        
        self.logger.info(f"\n📊 优化结果:")
        self.logger.info(f"   - 基准准确率: {self.current_best_accuracy:.3f}")
        self.logger.info(f"   - 增强准确率: {accuracy:.3f}")
        self.logger.info(f"   - 提升幅度: {improvement:.3f} ({improvement/self.current_best_accuracy*100:+.1f}%)")
        self.logger.info(f"   - 特征数量: {result.get('feature_count', 0)}")
        
        if 'all_scores' in result:
            self.logger.info(f"\n🤖 各模型表现:")
            for model_name, score in result['all_scores'].items():
                self.logger.info(f"   - {model_name}: {score:.3f}")
        
        if accuracy >= self.target_accuracy:
            self.logger.info(f"\n🎉 成功达到目标准确率 {self.target_accuracy:.1%}!")
        else:
            gap = self.target_accuracy - accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f} ({gap/self.target_accuracy*100:.1f}%)")
        
        total_time = time.time() - start_time
        self.logger.info(f"\n⏱️ 总优化时间: {total_time:.1f}秒")
        
        # 保存结果
        result_file = self.results_dir / f"enhanced_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                'accuracy': accuracy,
                'improvement': improvement,
                'target_achieved': accuracy >= self.target_accuracy,
                'best_model': result.get('best_model_name', ''),
                'feature_count': result.get('feature_count', 0),
                'optimization_time': total_time
            }, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📄 结果已保存: {result_file}")
        self.logger.info("="*80)

if __name__ == "__main__":
    optimizer = EnhancedOptimizer(target_accuracy=0.60)
    optimizer.run_enhanced_optimization()
