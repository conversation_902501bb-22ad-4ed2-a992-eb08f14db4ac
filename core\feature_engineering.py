#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征工程层 (Layer 2)

基于3个核心策略生成各种特征：
1. 一致性特征：策略一致度、一致性强度等
2. 分歧特征：策略分歧模式、分歧强度等  
3. 历史特征：近期表现、趋势、稳定性等
4. 动态权重特征：基于表现的权重调整
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from collections import deque
from dataclasses import dataclass


@dataclass
class FeatureSet:
    """特征集合数据结构"""
    consistency_features: Dict[str, float]
    divergence_features: Dict[str, float]
    historical_features: Dict[str, float]
    dynamic_weight_features: Dict[str, float]
    timestamp: float


class FeatureEngineeringLayer:
    """
    特征工程层管理器
    
    负责从基础策略输出中提取各种有用的特征
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 特征工程配置
        self.feature_config = config.get('feature_engineering', {})
        
        # 历史特征缓存
        self.feature_history = deque(maxlen=1000)
        
        self.logger.info("特征工程层初始化完成")
    
    def extract_features(self, strategy_outputs: Dict, history: List[Dict]) -> Dict[str, float]:
        """
        提取所有特征
        
        Args:
            strategy_outputs: 策略输出
            history: 历史数据
            
        Returns:
            特征字典
        """
        import time
        
        features = {}
        
        # 1. 一致性特征
        if self.feature_config.get('consistency', {}).get('enabled', True):
            consistency_features = self._extract_consistency_features(strategy_outputs)
            features.update(consistency_features)
        
        # 2. 分歧特征
        if self.feature_config.get('divergence', {}).get('enabled', True):
            divergence_features = self._extract_divergence_features(strategy_outputs)
            features.update(divergence_features)
        
        # 3. 历史特征
        if self.feature_config.get('historical', {}).get('enabled', True):
            historical_features = self._extract_historical_features(history)
            features.update(historical_features)
        
        # 4. 动态权重特征
        if self.feature_config.get('dynamic_weights', {}).get('enabled', True):
            weight_features = self._extract_dynamic_weight_features(strategy_outputs, history)
            features.update(weight_features)

        # 5. 时序特征 (新增)
        if self.feature_config.get('temporal', {}).get('enabled', True):
            temporal_features = self._extract_temporal_features(history)
            features.update(temporal_features)

        # 6. 交互特征 (新增)
        if self.feature_config.get('interaction', {}).get('enabled', True):
            interaction_features = self._extract_interaction_features(strategy_outputs)
            features.update(interaction_features)

        # 7. 上下文特征 (新增)
        if self.feature_config.get('context', {}).get('enabled', True):
            context_features = self._extract_context_features(strategy_outputs, history)
            features.update(context_features)

        # 8. 统计特征 (新增)
        if self.feature_config.get('statistical', {}).get('enabled', True):
            statistical_features = self._extract_statistical_features(strategy_outputs, history)
            features.update(statistical_features)

        # 9. 策略级连胜连败特征 (核心新功能)
        if self.feature_config.get('strategy_streaks', {}).get('enabled', True):
            streak_features = self._extract_strategy_streak_features(strategy_outputs, history)
            features.update(streak_features)

        # 记录特征历史
        feature_record = {
            'features': features.copy(),
            'timestamp': time.time()
        }
        self.feature_history.append(feature_record)

        self.logger.debug(f"提取了 {len(features)} 个特征")

        return features
    
    def _extract_consistency_features(self, strategy_outputs: Dict) -> Dict[str, float]:
        """提取一致性特征"""
        features = {}
        
        # 获取策略预测
        predictions = [output.prediction for output in strategy_outputs.values()]
        confidences = [output.confidence for output in strategy_outputs.values()]
        
        if not predictions:
            return features
        
        # 基础一致性特征
        features['consensus_count'] = sum(predictions)  # 预测1的策略数量
        features['consensus_ratio'] = sum(predictions) / len(predictions)  # 一致性比例
        features['full_consensus'] = 1.0 if len(set(predictions)) == 1 else 0.0  # 完全一致
        
        # 置信度相关特征
        features['avg_confidence'] = np.mean(confidences)
        features['min_confidence'] = np.min(confidences)
        features['max_confidence'] = np.max(confidences)
        features['confidence_std'] = np.std(confidences)
        
        # 加权一致性 (考虑置信度)
        weighted_predictions = [p * c for p, c in zip(predictions, confidences)]
        features['weighted_consensus'] = sum(weighted_predictions) / sum(confidences) if sum(confidences) > 0 else 0.5
        
        return features
    
    def _extract_divergence_features(self, strategy_outputs: Dict) -> Dict[str, float]:
        """提取分歧特征"""
        features = {}
        
        # 获取各策略的预测
        strategy_names = ['strategy_1', 'strategy_2', 'strategy_6']  # 实际的3个策略
        predictions = {}
        
        for name in strategy_names:
            if name in strategy_outputs:
                predictions[name] = strategy_outputs[name].prediction
        
        if len(predictions) < 3:
            return features
        
        # 分歧模式特征
        s1, s2, s6 = predictions['strategy_1'], predictions['strategy_2'], predictions['strategy_6']
        
        # 两两分歧
        features['s1_vs_s2'] = abs(s1 - s2)  # strategy_1 vs strategy_2
        features['s1_vs_s6'] = abs(s1 - s6)  # strategy_1 vs strategy_6
        features['s2_vs_s6'] = abs(s2 - s6)  # strategy_2 vs strategy_6
        
        # 分歧模式
        features['s1_minority'] = 1.0 if s1 != s2 and s1 != s6 else 0.0  # strategy_1是少数
        features['s2_minority'] = 1.0 if s2 != s1 and s2 != s6 else 0.0  # strategy_2是少数
        features['s6_minority'] = 1.0 if s6 != s1 and s6 != s2 else 0.0  # strategy_6是少数
        
        # 总分歧度
        total_divergence = features['s1_vs_s2'] + features['s1_vs_s6'] + features['s2_vs_s6']
        features['total_divergence'] = total_divergence
        features['max_divergence'] = max(features['s1_vs_s2'], features['s1_vs_s6'], features['s2_vs_s6'])
        
        return features
    
    def _extract_historical_features(self, history: List[Dict]) -> Dict[str, float]:
        """提取历史特征"""
        features = {}
        
        if not history:
            return features
        
        # 不同时间窗口的特征
        window_sizes = self.feature_config.get('historical', {}).get('lookback_windows', [3, 5, 10, 20])
        
        for window in window_sizes:
            recent_history = history[-window:] if len(history) >= window else history
            
            if not recent_history:
                continue
            
            # 结果统计
            results = [h.get('result', 0) for h in recent_history if 'result' in h]
            if results:
                features[f'win_rate_{window}'] = sum(results) / len(results)
                features[f'volatility_{window}'] = np.std(results) if len(results) > 1 else 0.0
                
                # 趋势特征
                if len(results) >= 3:
                    # 简单的趋势检测
                    recent_trend = np.mean(results[-3:]) - np.mean(results[:-3]) if len(results) > 3 else 0
                    features[f'trend_{window}'] = recent_trend
        
        # 连胜连败特征
        if history:
            features['current_streak'] = self._calculate_current_streak(history)
            features['max_win_streak'] = self._calculate_max_streak(history, target=1)
            features['max_loss_streak'] = self._calculate_max_streak(history, target=0)
        
        return features
    
    def _extract_dynamic_weight_features(self, strategy_outputs: Dict, history: List[Dict]) -> Dict[str, float]:
        """提取动态权重特征"""
        features = {}
        
        # 基于近期表现的权重
        strategy_names = [f'strategy_{i}' for i in range(1, 9)]  # 8个策略
        
        for name in strategy_names:
            if name in strategy_outputs:
                # 当前权重
                current_weight = getattr(strategy_outputs[name], 'current_weight', 1.0)
                features[f'{name}_weight'] = current_weight
                
                # 基于置信度的调整权重
                confidence = strategy_outputs[name].confidence
                features[f'{name}_confidence_weight'] = current_weight * confidence
        
        # 权重分布特征
        weights = [features.get(f'{name}_weight', 1.0) for name in strategy_names]
        if weights:
            features['weight_entropy'] = self._calculate_entropy(weights)
            features['weight_concentration'] = max(weights) / sum(weights) if sum(weights) > 0 else 0
        
        return features
    
    def _calculate_current_streak(self, history: List[Dict]) -> int:
        """计算当前连胜/连败"""
        if not history:
            return 0
        
        current_result = history[-1].get('result')
        if current_result is None:
            return 0
        
        streak = 1
        for i in range(len(history) - 2, -1, -1):
            if history[i].get('result') == current_result:
                streak += 1
            else:
                break
        
        return streak if current_result == 1 else -streak
    
    def _calculate_max_streak(self, history: List[Dict], target: int) -> int:
        """计算最大连胜/连败"""
        if not history:
            return 0
        
        max_streak = 0
        current_streak = 0
        
        for record in history:
            if record.get('result') == target:
                current_streak += 1
                max_streak = max(max_streak, current_streak)
            else:
                current_streak = 0
        
        return max_streak
    
    def _calculate_entropy(self, weights: List[float]) -> float:
        """计算权重分布的熵"""
        if not weights or sum(weights) == 0:
            return 0.0
        
        # 归一化权重
        total = sum(weights)
        probs = [w / total for w in weights]
        
        # 计算熵
        entropy = 0.0
        for p in probs:
            if p > 0:
                entropy -= p * np.log2(p)
        
        return entropy

    def _extract_strategy_streak_features(self, strategy_outputs: Dict, history: List[Dict]) -> Dict[str, float]:
        """
        提取策略级连胜连败特征 (核心功能)

        为每个策略计算：
        1. 连胜连败次数
        2. 移动窗口胜率
        3. 胜率趋势
        4. 稳定性指标
        """
        features = {}

        strategy_names = ['strategy_1', 'strategy_2', 'strategy_6']  # 使用实际的3个策略
        window_sizes = self.feature_config.get('strategy_streaks', {}).get('window_sizes', [3, 5, 10, 20])

        # 从历史中提取每个策略的表现
        strategy_history = self._build_strategy_history(history)

        for strategy_name in strategy_names:
            if strategy_name not in strategy_history:
                continue

            strategy_records = strategy_history[strategy_name]

            # 1. 连胜连败特征
            current_streak = self._calculate_strategy_streak(strategy_records)
            features[f'{strategy_name}_current_streak'] = current_streak
            features[f'{strategy_name}_max_win_streak'] = self._calculate_strategy_max_streak(strategy_records, target=1)
            features[f'{strategy_name}_max_loss_streak'] = self._calculate_strategy_max_streak(strategy_records, target=0)

            # 2. 移动窗口胜率
            for window in window_sizes:
                recent_records = strategy_records[-window:] if len(strategy_records) >= window else strategy_records
                if recent_records:
                    correct_count = sum(1 for r in recent_records if r['correct'])
                    win_rate = correct_count / len(recent_records)
                    features[f'{strategy_name}_winrate_{window}'] = win_rate

                    # 胜率稳定性
                    if len(recent_records) >= 3:
                        # 计算胜率的方差
                        results = [1 if r['correct'] else 0 for r in recent_records]
                        features[f'{strategy_name}_stability_{window}'] = 1.0 - np.std(results)

            # 3. 胜率趋势
            if len(strategy_records) >= 6:
                # 比较前半段和后半段的胜率
                mid_point = len(strategy_records) // 2
                early_records = strategy_records[:mid_point]
                late_records = strategy_records[mid_point:]

                early_winrate = sum(1 for r in early_records if r['correct']) / len(early_records)
                late_winrate = sum(1 for r in late_records if r['correct']) / len(late_records)

                features[f'{strategy_name}_winrate_trend'] = late_winrate - early_winrate

            # 4. 近期表现权重
            if strategy_records:
                # 指数衰减权重
                weights = [0.9 ** i for i in range(len(strategy_records))]
                weights.reverse()  # 最近的权重最大

                weighted_correct = sum(r['correct'] * w for r, w in zip(strategy_records, weights))
                weighted_winrate = weighted_correct / sum(weights)
                features[f'{strategy_name}_weighted_winrate'] = weighted_winrate

        # 5. 策略间比较特征 (8个策略)
        if len(strategy_names) >= 2:
            # 比较各策略的当前连胜连败
            streaks = [features.get(f'{name}_current_streak', 0) for name in strategy_names]
            positive_streaks = [abs(s) if s > 0 else 0 for s in streaks]
            negative_streaks = [abs(s) if s < 0 else 0 for s in streaks]

            if max(positive_streaks) > 0:
                features['best_streak_strategy'] = strategy_names[np.argmax(positive_streaks)]
            else:
                features['best_streak_strategy'] = strategy_names[0]

            if max(negative_streaks) > 0:
                features['worst_streak_strategy'] = strategy_names[np.argmax(negative_streaks)]
            else:
                features['worst_streak_strategy'] = strategy_names[0]

            # 比较各策略的短期胜率 (5手)
            winrates_5 = [features.get(f'{name}_winrate_5', 0.5) for name in strategy_names]
            best_strategy_idx = np.argmax(winrates_5)
            features['best_winrate_strategy'] = strategy_names[best_strategy_idx]
            features['best_winrate_value'] = winrates_5[best_strategy_idx]

            # 胜率差异
            features['winrate_range'] = max(winrates_5) - min(winrates_5)
            features['winrate_std'] = np.std(winrates_5)

            # 8策略的额外比较特征
            winrates_10 = [features.get(f'{name}_winrate_10', 0.5) for name in strategy_names]
            features['best_winrate_10_strategy'] = strategy_names[np.argmax(winrates_10)]
            features['winrate_10_range'] = max(winrates_10) - min(winrates_10)

            # 加权胜率比较
            weighted_winrates = [features.get(f'{name}_weighted_winrate', 0.5) for name in strategy_names]
            features['best_weighted_strategy'] = strategy_names[np.argmax(weighted_winrates)]
            features['weighted_winrate_range'] = max(weighted_winrates) - min(weighted_winrates)

        return features

    def _build_strategy_history(self, history: List[Dict]) -> Dict[str, List[Dict]]:
        """从历史记录中构建每个策略的表现历史"""
        strategy_history = {f'strategy_{i}': [] for i in range(1, 9)}  # 8个策略

        for record in history:
            actual_result = record.get('result')
            if actual_result is None:
                continue

            # 提取各策略的预测和正确性
            for strategy_name in strategy_history.keys():
                strategy_prediction = record.get(f'{strategy_name}_prediction')
                if strategy_prediction is not None:
                    is_correct = (strategy_prediction == actual_result)
                    strategy_history[strategy_name].append({
                        'prediction': strategy_prediction,
                        'actual': actual_result,
                        'correct': is_correct,
                        'timestamp': record.get('timestamp', 0)
                    })

        return strategy_history

    def _calculate_strategy_streak(self, strategy_records: List[Dict]) -> int:
        """计算策略的当前连胜连败"""
        if not strategy_records:
            return 0

        current_result = strategy_records[-1]['correct']
        streak = 1

        for i in range(len(strategy_records) - 2, -1, -1):
            if strategy_records[i]['correct'] == current_result:
                streak += 1
            else:
                break

        return streak if current_result else -streak

    def _calculate_strategy_max_streak(self, strategy_records: List[Dict], target: int) -> int:
        """计算策略的最大连胜/连败"""
        if not strategy_records:
            return 0

        max_streak = 0
        current_streak = 0

        for record in strategy_records:
            is_target = (record['correct'] == bool(target))
            if is_target:
                current_streak += 1
                max_streak = max(max_streak, current_streak)
            else:
                current_streak = 0

        return max_streak

    def get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性 (简化版本)"""
        # 这里可以实现更复杂的特征重要性计算
        # 目前返回基础的重要性评估
        
        importance = {
            'consensus_ratio': 0.9,
            'weighted_consensus': 0.85,
            'avg_confidence': 0.8,
            'total_divergence': 0.75,
            'win_rate_10': 0.7,
            'current_streak': 0.65,
            'strategy_1_weight': 0.6,
            'volatility_5': 0.55,
            'trend_10': 0.5
        }
        
        return importance

    def _extract_temporal_features(self, history: List[Dict]) -> Dict[str, float]:
        """
        提取时序特征

        Args:
            history: 历史数据

        Returns:
            时序特征字典
        """
        features = {}

        if not history:
            return features

        # 时间间隔特征
        if len(history) >= 2:
            timestamps = [h.get('timestamp', 0) for h in history if 'timestamp' in h]
            if len(timestamps) >= 2:
                # 平均时间间隔
                intervals = [timestamps[i] - timestamps[i-1] for i in range(1, len(timestamps))]
                features['avg_time_interval'] = np.mean(intervals) if intervals else 0
                features['time_interval_std'] = np.std(intervals) if len(intervals) > 1 else 0

        # 周期性特征
        if len(history) >= 10:
            results = [h.get('result', 0) for h in history[-20:] if 'result' in h]
            if len(results) >= 10:
                # 简单的周期性检测
                features['periodicity_2'] = self._detect_periodicity(results, period=2)
                features['periodicity_3'] = self._detect_periodicity(results, period=3)
                features['periodicity_5'] = self._detect_periodicity(results, period=5)

        # 时间衰减特征
        if history:
            recent_results = [h.get('result', 0) for h in history[-10:] if 'result' in h]
            if recent_results:
                # 指数衰减加权平均
                weights = [0.9 ** i for i in range(len(recent_results))]
                weights.reverse()  # 最近的权重最大
                weighted_avg = sum(r * w for r, w in zip(recent_results, weights)) / sum(weights)
                features['exponential_weighted_avg'] = weighted_avg

        return features

    def _extract_interaction_features(self, strategy_outputs: Dict) -> Dict[str, float]:
        """
        提取交互特征

        Args:
            strategy_outputs: 策略输出

        Returns:
            交互特征字典
        """
        features = {}

        strategy_names = ['strategy_1', 'strategy_2', 'strategy_6']
        predictions = {}
        confidences = {}

        for name in strategy_names:
            if name in strategy_outputs:
                predictions[name] = strategy_outputs[name].prediction
                confidences[name] = strategy_outputs[name].confidence

        if len(predictions) < 3:
            return features

        # 预测与置信度的交互
        for name in strategy_names:
            if name in predictions and name in confidences:
                features[f'{name}_prediction'] = predictions[name]
                features[f'{name}_confidence'] = confidences[name]
                features[f'{name}_pred_conf_product'] = predictions[name] * confidences[name]

        # 策略间的置信度差异
        conf_values = list(confidences.values())
        if len(conf_values) >= 2:
            features['confidence_range'] = max(conf_values) - min(conf_values)
            features['confidence_ratio'] = max(conf_values) / min(conf_values) if min(conf_values) > 0 else 1.0

        # 高置信度策略的一致性
        high_conf_threshold = 0.6
        high_conf_predictions = [p for name, p in predictions.items()
                               if confidences.get(name, 0) >= high_conf_threshold]
        if high_conf_predictions:
            features['high_conf_consensus'] = sum(high_conf_predictions) / len(high_conf_predictions)
            features['high_conf_count'] = len(high_conf_predictions)
        else:
            features['high_conf_consensus'] = 0.5
            features['high_conf_count'] = 0

        return features

    def _extract_context_features(self, strategy_outputs: Dict, history: List[Dict]) -> Dict[str, float]:
        """
        提取上下文特征

        Args:
            strategy_outputs: 策略输出
            history: 历史数据

        Returns:
            上下文特征字典
        """
        features = {}

        # 当前决策的上下文
        if history:
            # 最近的决策模式
            recent_decisions = [h.get('decision', 0) for h in history[-5:] if 'decision' in h]
            if recent_decisions:
                features['recent_decision_avg'] = np.mean(recent_decisions)
                features['recent_decision_volatility'] = np.std(recent_decisions) if len(recent_decisions) > 1 else 0

        # 策略一致性的历史上下文
        if len(self.feature_history) >= 3:
            recent_consensus = []
            # 获取最近5条记录
            recent_records = list(self.feature_history)[-5:] if len(self.feature_history) >= 5 else list(self.feature_history)
            for record in recent_records:
                consensus = record['features'].get('consensus_ratio', 0.5)
                recent_consensus.append(consensus)

            if recent_consensus:
                features['consensus_trend'] = recent_consensus[-1] - recent_consensus[0] if len(recent_consensus) >= 2 else 0
                features['consensus_stability'] = 1.0 - np.std(recent_consensus) if len(recent_consensus) > 1 else 1.0

        # 环境变化检测
        if len(self.feature_history) >= 10:
            # 检测特征分布的变化
            all_records = list(self.feature_history)
            old_features = [record['features'] for record in all_records[-10:-5]]
            new_features = [record['features'] for record in all_records[-5:]]

            if old_features and new_features:
                # 简单的分布变化检测
                old_avg_conf = np.mean([f.get('avg_confidence', 0.5) for f in old_features])
                new_avg_conf = np.mean([f.get('avg_confidence', 0.5) for f in new_features])
                features['confidence_shift'] = new_avg_conf - old_avg_conf

        return features

    def _extract_statistical_features(self, strategy_outputs: Dict, history: List[Dict]) -> Dict[str, float]:
        """
        提取统计特征

        Args:
            strategy_outputs: 策略输出
            history: 历史数据

        Returns:
            统计特征字典
        """
        features = {}

        # 策略预测的统计特征
        predictions = [output.prediction for output in strategy_outputs.values()]
        confidences = [output.confidence for output in strategy_outputs.values()]

        if predictions:
            # 预测的统计量
            features['prediction_mean'] = np.mean(predictions)
            features['prediction_std'] = np.std(predictions) if len(predictions) > 1 else 0
            features['prediction_skewness'] = self._calculate_skewness(predictions)

            # 置信度的统计量
            features['confidence_mean'] = np.mean(confidences)
            features['confidence_std'] = np.std(confidences) if len(confidences) > 1 else 0
            features['confidence_skewness'] = self._calculate_skewness(confidences)

        # 历史结果的统计特征
        if history:
            results = [h.get('result', 0) for h in history if 'result' in h]
            if results:
                # 不同窗口的统计特征
                for window in [5, 10, 20]:
                    window_results = results[-window:] if len(results) >= window else results
                    if len(window_results) >= 3:
                        features[f'result_skewness_{window}'] = self._calculate_skewness(window_results)
                        features[f'result_kurtosis_{window}'] = self._calculate_kurtosis(window_results)

        # 特征历史的统计特征
        if len(self.feature_history) >= 5:
            # 一致性比例的统计
            recent_records = list(self.feature_history)[-10:] if len(self.feature_history) >= 10 else list(self.feature_history)
            consensus_history = [record['features'].get('consensus_ratio', 0.5)
                               for record in recent_records]
            if consensus_history:
                features['consensus_mean'] = np.mean(consensus_history)
                features['consensus_std'] = np.std(consensus_history) if len(consensus_history) > 1 else 0
                features['consensus_trend_slope'] = self._calculate_trend_slope(consensus_history)

        return features

    def _detect_periodicity(self, data: List[float], period: int) -> float:
        """
        检测数据的周期性

        Args:
            data: 数据序列
            period: 周期长度

        Returns:
            周期性强度 (0-1)
        """
        if len(data) < period * 2:
            return 0.0

        # 计算自相关
        correlations = []
        for i in range(len(data) - period):
            if i + period < len(data):
                # 简单的相关性计算
                val1 = data[i]
                val2 = data[i + period]
                correlations.append(1.0 if val1 == val2 else 0.0)

        return np.mean(correlations) if correlations else 0.0

    def _calculate_skewness(self, data: List[float]) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0.0

        mean = np.mean(data)
        std = np.std(data)

        if std == 0:
            return 0.0

        skewness = np.mean([((x - mean) / std) ** 3 for x in data])
        return skewness

    def _calculate_kurtosis(self, data: List[float]) -> float:
        """计算峰度"""
        if len(data) < 4:
            return 0.0

        mean = np.mean(data)
        std = np.std(data)

        if std == 0:
            return 0.0

        kurtosis = np.mean([((x - mean) / std) ** 4 for x in data]) - 3
        return kurtosis

    def _calculate_trend_slope(self, data: List[float]) -> float:
        """计算趋势斜率"""
        if len(data) < 2:
            return 0.0

        # 简单的线性回归斜率
        n = len(data)
        x = list(range(n))

        x_mean = np.mean(x)
        y_mean = np.mean(data)

        numerator = sum((x[i] - x_mean) * (data[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))

        if denominator == 0:
            return 0.0

        slope = numerator / denominator
        return slope
