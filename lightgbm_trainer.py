#!/usr/bin/env python3
"""
LightGBM优化训练器
专注于使用LightGBM进行高效训练和特征工程
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class LightGBMTrainer:
    """LightGBM优化训练器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_all_strategies(self) -> pd.DataFrame:
        """加载所有8个策略数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载所有8个策略数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            
            # 检查各策略相关性
            correlations = {}
            for col in strategy_cols:
                corr = df[col].corr(df['actual_result'])
                correlations[col] = corr
            
            return df, correlations
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame(), {}
    
    def create_lightgbm_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """为LightGBM创建优化特征"""
        self.logger.info("\n🔧 创建LightGBM优化特征...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. 基础统计特征
        df['strategy_sum'] = df[strategy_cols].sum(axis=1)
        df['strategy_mean'] = df[strategy_cols].mean(axis=1)
        df['strategy_std'] = df[strategy_cols].std(axis=1).fillna(0)
        df['strategy_median'] = df[strategy_cols].median(axis=1)
        
        # 2. 投票特征
        df['majority_vote'] = (df['strategy_sum'] >= 4).astype(int)
        df['strong_majority'] = (df['strategy_sum'] >= 6).astype(int)
        df['consensus_strength'] = np.abs(df['strategy_sum'] - 4) / 4
        
        # 3. 基于相关性的分组特征
        # 高相关性组: strategy_1, strategy_8
        df['high_corr_sum'] = df['strategy_1'] + df['strategy_8']
        df['high_corr_mean'] = df['high_corr_sum'] / 2
        
        # 中等相关性组: strategy_3, strategy_5, strategy_7
        df['mid_corr_sum'] = df['strategy_3'] + df['strategy_5'] + df['strategy_7']
        df['mid_corr_mean'] = df['mid_corr_sum'] / 3
        
        # 低相关性组: strategy_2, strategy_4, strategy_6
        df['low_corr_sum'] = df['strategy_2'] + df['strategy_4'] + df['strategy_6']
        df['low_corr_mean'] = df['low_corr_sum'] / 3
        
        # 4. 对比特征
        df['high_vs_low'] = df['high_corr_mean'] - df['low_corr_mean']
        df['high_vs_mid'] = df['high_corr_mean'] - df['mid_corr_mean']
        
        # 5. 加权特征 (基于相关性)
        weights = {
            'strategy_1': 0.138, 'strategy_8': 0.038, 'strategy_5': 0.006,
            'strategy_3': 0.005, 'strategy_7': 0.004, 'strategy_4': -0.004,
            'strategy_6': -0.001, 'strategy_2': -0.0003
        }
        
        df['weighted_sum'] = sum(df[col] * weight for col, weight in weights.items())
        df['abs_weighted_sum'] = sum(df[col] * abs(weight) for col, weight in weights.items())
        
        # 6. 非线性特征
        df['strategy_1_boosted'] = df['strategy_1'] * (1 + df['strategy_8'] * 0.5)  # strategy_1与strategy_8的协同
        df['strategy_1_isolated'] = df['strategy_1'] * (1 - df['strategy_mean'])  # strategy_1独立时的权重
        
        # 7. 序列特征 (时间序列)
        df = df.sort_values(['boot_id', 'id']).reset_index(drop=True)
        
        # 滑动窗口
        for window in [3, 5]:
            df[f'strategy_sum_ma_{window}'] = df['strategy_sum'].rolling(window=window, min_periods=1).mean()
            df[f'majority_vote_ma_{window}'] = df['majority_vote'].rolling(window=window, min_periods=1).mean()
            df[f'strategy_1_ma_{window}'] = df['strategy_1'].rolling(window=window, min_periods=1).mean()
        
        # 趋势
        df['strategy_sum_trend'] = df['strategy_sum'].diff().fillna(0)
        df['strategy_1_trend'] = df['strategy_1'].diff().fillna(0)
        
        # 8. Boot内特征
        boot_stats = df.groupby('boot_id').agg({
            'strategy_sum': 'mean',
            'strategy_1': 'mean',
            'majority_vote': 'mean'
        }).add_prefix('boot_')
        
        df = df.merge(boot_stats, left_on='boot_id', right_index=True, how='left')
        
        # Boot内位置
        df['position_in_boot'] = df.groupby('boot_id').cumcount() + 1
        df['boot_progress'] = df.groupby('boot_id')['position_in_boot'].transform(lambda x: x / x.max())
        
        # 9. 平衡化特征
        df['balanced_vote'] = (df['strategy_1'] * 0.3 + df['strategy_sum'] * 0.7 / 8)  # 降低strategy_1权重
        df['other_strategies_enhanced'] = (df['strategy_sum'] - df['strategy_1']) / 7  # 增强其他策略
        
        # 填充NaN
        df = df.fillna(0)
        df = df.replace([np.inf, -np.inf], 0)
        
        feature_count = len(df.columns) - len(strategy_cols) - 3
        self.logger.info(f"   ✅ 创建了 {feature_count} 个LightGBM特征")
        
        return df
    
    def train_lightgbm_models(self, df: pd.DataFrame) -> Dict[str, Any]:
        """训练LightGBM模型"""
        try:
            import lightgbm as lgb
            from sklearn.model_selection import train_test_split, cross_val_score
            from sklearn.metrics import accuracy_score, classification_report
            from sklearn.ensemble import VotingClassifier
            from sklearn.linear_model import LogisticRegression
            
            self.logger.info("\n🤖 开始LightGBM模型训练...")
            
            # 准备特征
            exclude_cols = ['id', 'boot_id', 'actual_result'] + [f'strategy_{i}' for i in range(1, 9)]
            feature_cols = [col for col in df.columns if col not in exclude_cols]
            
            X = df[feature_cols].values
            y = df['actual_result'].values
            
            self.logger.info(f"   📊 特征维度: {X.shape}")
            self.logger.info(f"   📊 使用特征: {len(feature_cols)} 个")
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 创建多个LightGBM模型配置
            lgb_configs = {
                'lgb_default': {
                    'n_estimators': 200,
                    'max_depth': 6,
                    'learning_rate': 0.1,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'random_state': 42,
                    'verbose': -1
                },
                'lgb_conservative': {
                    'n_estimators': 150,
                    'max_depth': 4,
                    'learning_rate': 0.05,
                    'subsample': 0.9,
                    'colsample_bytree': 0.9,
                    'random_state': 42,
                    'verbose': -1
                },
                'lgb_aggressive': {
                    'n_estimators': 300,
                    'max_depth': 8,
                    'learning_rate': 0.15,
                    'subsample': 0.7,
                    'colsample_bytree': 0.7,
                    'random_state': 42,
                    'verbose': -1
                }
            }
            
            # 添加对比模型
            models = {}
            
            # LightGBM模型
            for name, config in lgb_configs.items():
                models[name] = lgb.LGBMClassifier(**config)
            
            # 对比模型
            models['logistic'] = LogisticRegression(random_state=42, max_iter=1000, C=0.1)
            
            # 训练和评估
            results = {}
            for name, model in models.items():
                self.logger.info(f"   🔄 训练 {name}...")
                
                start_time = time.time()
                model.fit(X_train, y_train)
                train_time = time.time() - start_time
                
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'train_time': train_time
                }
                
                self.logger.info(f"      ✅ {name}: 准确率={accuracy:.3f}, CV={cv_scores.mean():.3f}±{cv_scores.std():.3f}, 时间={train_time:.1f}s")
            
            # 创建集成模型
            self.logger.info("   🔗 创建LightGBM集成...")
            
            # 选择最佳的LightGBM模型
            lgb_models = {k: v for k, v in results.items() if k.startswith('lgb_')}
            best_lgb = max(lgb_models.items(), key=lambda x: x[1]['cv_mean'])
            
            # 集成最佳LightGBM和逻辑回归
            ensemble = VotingClassifier(
                estimators=[
                    ('best_lgb', best_lgb[1]['model']),
                    ('logistic', results['logistic']['model'])
                ],
                voting='soft'
            )
            
            ensemble.fit(X_train, y_train)
            ensemble_pred = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
            
            self.logger.info(f"   🏆 集成模型: 准确率={ensemble_accuracy:.3f}")
            
            # 特征重要性分析
            if hasattr(best_lgb[1]['model'], 'feature_importances_'):
                importances = best_lgb[1]['model'].feature_importances_
                feature_importance = dict(zip(feature_cols, importances))
                top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
                
                self.logger.info(f"\n   🎯 Top 10 重要特征:")
                for i, (feature, importance) in enumerate(top_features):
                    self.logger.info(f"      {i+1:2d}. {feature}: {importance:.3f}")
            
            return {
                'individual_models': results,
                'ensemble': ensemble,
                'ensemble_accuracy': ensemble_accuracy,
                'best_lgb_name': best_lgb[0],
                'best_lgb_accuracy': best_lgb[1]['accuracy'],
                'feature_importance': top_features if 'top_features' in locals() else [],
                'feature_names': feature_cols
            }
            
        except Exception as e:
            self.logger.error(f"❌ LightGBM训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def run_lightgbm_training(self):
        """运行LightGBM训练"""
        self.logger.info("🚀 开始LightGBM优化训练")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df, correlations = self.load_all_strategies()
        if df.empty:
            self.logger.error("❌ 无法加载数据，训练终止")
            return
        
        # 2. 创建特征
        df_features = self.create_lightgbm_features(df)
        
        # 3. 训练模型
        results = self.train_lightgbm_models(df_features)
        
        if not results:
            self.logger.error("❌ 模型训练失败")
            return
        
        # 4. 生成报告
        total_time = time.time() - start_time
        self.generate_report(results, correlations, total_time, len(df))
    
    def generate_report(self, results: Dict[str, Any], correlations: Dict[str, float],
                       total_time: float, total_samples: int):
        """生成训练报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 LightGBM优化训练报告")
        self.logger.info("="*80)
        
        self.logger.info(f"\n📊 训练统计:")
        self.logger.info(f"   - 总样本数: {total_samples:,}")
        self.logger.info(f"   - 训练时间: {total_time:.1f}秒")
        self.logger.info(f"   - 特征数量: {len(results.get('feature_names', []))}")
        
        if 'individual_models' in results:
            self.logger.info(f"\n🤖 模型结果:")
            for name, model_info in results['individual_models'].items():
                self.logger.info(f"   - {name:15}: 准确率={model_info['accuracy']:.3f}, CV={model_info['cv_mean']:.3f}±{model_info['cv_std']:.3f}")
        
        self.logger.info(f"\n🏆 最佳结果:")
        self.logger.info(f"   - 最佳LightGBM: {results.get('best_lgb_name', 'N/A')} ({results.get('best_lgb_accuracy', 0):.3f})")
        self.logger.info(f"   - 集成模型: {results.get('ensemble_accuracy', 0):.3f}")
        
        if results.get('feature_importance'):
            self.logger.info(f"\n🎯 重要特征:")
            for i, (feature, importance) in enumerate(results['feature_importance'][:5]):
                self.logger.info(f"   {i+1}. {feature}: {importance:.3f}")
        
        # 性能分析
        baseline_accuracy = 0.568
        best_accuracy = results.get('ensemble_accuracy', 0)
        improvement = best_accuracy - baseline_accuracy
        
        self.logger.info(f"\n📈 性能提升:")
        self.logger.info(f"   - 基准准确率: {baseline_accuracy:.3f}")
        self.logger.info(f"   - LightGBM准确率: {best_accuracy:.3f}")
        self.logger.info(f"   - 提升幅度: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        
        target_accuracy = 0.60
        if best_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 训练成功！达到目标准确率 {target_accuracy:.1%}")
        else:
            gap = target_accuracy - best_accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f} ({gap/target_accuracy*100:.1f}%)")
        
        self.logger.info("="*80)

if __name__ == "__main__":
    trainer = LightGBMTrainer()
    trainer.run_lightgbm_training()
