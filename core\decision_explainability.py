#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
决策解释性系统

提供决策推理过程、特征重要性分析、决策路径可视化等功能
"""

import numpy as np
import pandas as pd
import logging
import json
import time
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from enum import Enum
import warnings

try:
    from sklearn.inspection import permutation_importance
    from sklearn.tree import DecisionTreeClassifier
    from sklearn.ensemble import RandomForestClassifier
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False


class ExplanationType(Enum):
    """解释类型枚举"""
    FEATURE_IMPORTANCE = "feature_importance"
    DECISION_PATH = "decision_path"
    COUNTERFACTUAL = "counterfactual"
    RULE_BASED = "rule_based"
    LAYER_CONTRIBUTION = "layer_contribution"
    CONFIDENCE_BREAKDOWN = "confidence_breakdown"


class ImportanceMethod(Enum):
    """重要性计算方法枚举"""
    PERMUTATION = "permutation"
    SHAP = "shap"
    GRADIENT = "gradient"
    ATTENTION = "attention"
    CORRELATION = "correlation"
    VARIANCE = "variance"


@dataclass
class FeatureImportance:
    """特征重要性"""
    feature_name: str
    importance_score: float
    rank: int
    method: ImportanceMethod
    confidence_interval: Optional[Tuple[float, float]] = None
    description: str = ""


@dataclass
class DecisionStep:
    """决策步骤"""
    step_id: str
    layer_name: str
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    reasoning: str
    confidence: float
    contribution_score: float
    timestamp: float


@dataclass
class DecisionPath:
    """决策路径"""
    decision_id: str
    steps: List[DecisionStep]
    final_prediction: float
    final_confidence: float
    total_processing_time: float
    critical_features: List[str]
    decision_factors: Dict[str, float]


@dataclass
class CounterfactualExplanation:
    """反事实解释"""
    original_prediction: float
    counterfactual_prediction: float
    feature_changes: Dict[str, Tuple[float, float]]  # feature: (original, counterfactual)
    change_magnitude: float
    feasibility_score: float
    explanation: str


@dataclass
class RuleExplanation:
    """规则解释"""
    rule_id: str
    condition: str
    conclusion: str
    confidence: float
    support: int
    coverage: float
    examples: List[Dict[str, Any]]


@dataclass
class DecisionExplanation:
    """完整决策解释"""
    decision_id: str
    prediction: float
    confidence: float
    explanation_type: ExplanationType
    feature_importances: List[FeatureImportance]
    decision_path: Optional[DecisionPath]
    counterfactuals: List[CounterfactualExplanation]
    rules: List[RuleExplanation]
    confidence_breakdown: Dict[str, float]
    layer_contributions: Dict[str, float]
    summary: str
    detailed_reasoning: str
    timestamp: float


class FeatureImportanceCalculator:
    """特征重要性计算器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.history_data = []
        self.surrogate_model = None
        
    def add_decision_data(self, features: Dict[str, float], prediction: float, actual: Optional[int] = None):
        """添加决策数据用于重要性分析"""
        data_point = {
            'features': features.copy(),
            'prediction': prediction,
            'actual': actual,
            'timestamp': time.time()
        }
        self.history_data.append(data_point)
        
        # 保持历史数据在合理范围内
        if len(self.history_data) > 1000:
            self.history_data = self.history_data[-800:]
    
    def calculate_feature_importance(self, features: Dict[str, float], 
                                   method: ImportanceMethod = ImportanceMethod.CORRELATION) -> List[FeatureImportance]:
        """计算特征重要性"""
        if not self.history_data:
            return self._default_importance(features)
        
        try:
            if method == ImportanceMethod.CORRELATION:
                return self._correlation_importance(features)
            elif method == ImportanceMethod.VARIANCE:
                return self._variance_importance(features)
            elif method == ImportanceMethod.PERMUTATION and SKLEARN_AVAILABLE:
                return self._permutation_importance(features)
            elif method == ImportanceMethod.SHAP and SHAP_AVAILABLE:
                return self._shap_importance(features)
            else:
                return self._correlation_importance(features)
                
        except Exception as e:
            self.logger.warning(f"特征重要性计算失败: {str(e)}")
            return self._default_importance(features)
    
    def _correlation_importance(self, features: Dict[str, float]) -> List[FeatureImportance]:
        """基于相关性的重要性计算"""
        if len(self.history_data) < 10:
            return self._default_importance(features)
        
        # 准备数据
        feature_names = list(features.keys())
        feature_matrix = []
        predictions = []
        
        for data in self.history_data[-100:]:  # 使用最近100个数据点
            feature_vector = [data['features'].get(name, 0.0) for name in feature_names]
            feature_matrix.append(feature_vector)
            predictions.append(data['prediction'])
        
        feature_matrix = np.array(feature_matrix)
        predictions = np.array(predictions)
        
        # 计算相关性
        importances = []
        for i, feature_name in enumerate(feature_names):
            if feature_matrix.shape[0] > 1:
                correlation = np.corrcoef(feature_matrix[:, i], predictions)[0, 1]
                if np.isnan(correlation):
                    correlation = 0.0
                importance_score = abs(correlation)
            else:
                importance_score = 0.1
            
            importances.append(FeatureImportance(
                feature_name=feature_name,
                importance_score=importance_score,
                rank=0,  # 将在排序后设置
                method=ImportanceMethod.CORRELATION,
                description=f"与预测结果的相关性: {correlation:.3f}"
            ))
        
        # 排序并设置排名
        importances.sort(key=lambda x: x.importance_score, reverse=True)
        for i, importance in enumerate(importances):
            importance.rank = i + 1
        
        return importances
    
    def _variance_importance(self, features: Dict[str, float]) -> List[FeatureImportance]:
        """基于方差的重要性计算"""
        if len(self.history_data) < 10:
            return self._default_importance(features)
        
        feature_names = list(features.keys())
        feature_matrix = []
        
        for data in self.history_data[-100:]:
            feature_vector = [data['features'].get(name, 0.0) for name in feature_names]
            feature_matrix.append(feature_vector)
        
        feature_matrix = np.array(feature_matrix)
        
        importances = []
        for i, feature_name in enumerate(feature_names):
            variance = np.var(feature_matrix[:, i])
            # 归一化方差作为重要性分数
            importance_score = min(variance / (variance + 0.1), 1.0)
            
            importances.append(FeatureImportance(
                feature_name=feature_name,
                importance_score=importance_score,
                rank=0,
                method=ImportanceMethod.VARIANCE,
                description=f"特征方差: {variance:.3f}"
            ))
        
        # 排序并设置排名
        importances.sort(key=lambda x: x.importance_score, reverse=True)
        for i, importance in enumerate(importances):
            importance.rank = i + 1
        
        return importances
    
    def _permutation_importance(self, features: Dict[str, float]) -> List[FeatureImportance]:
        """基于排列的重要性计算"""
        if not SKLEARN_AVAILABLE or len(self.history_data) < 20:
            return self._correlation_importance(features)
        
        try:
            # 训练代理模型
            self._train_surrogate_model()
            
            if self.surrogate_model is None:
                return self._correlation_importance(features)
            
            # 准备数据
            feature_names = list(features.keys())
            X = []
            y = []
            
            for data in self.history_data[-100:]:
                feature_vector = [data['features'].get(name, 0.0) for name in feature_names]
                X.append(feature_vector)
                y.append(data['prediction'])
            
            X = np.array(X)
            y = np.array(y)
            
            # 计算排列重要性
            perm_importance = permutation_importance(
                self.surrogate_model, X, y, n_repeats=5, random_state=42
            )
            
            importances = []
            for i, feature_name in enumerate(feature_names):
                importance_score = perm_importance.importances_mean[i]
                std = perm_importance.importances_std[i]
                
                importances.append(FeatureImportance(
                    feature_name=feature_name,
                    importance_score=importance_score,
                    rank=0,
                    method=ImportanceMethod.PERMUTATION,
                    confidence_interval=(importance_score - std, importance_score + std),
                    description=f"排列重要性: {importance_score:.3f} ± {std:.3f}"
                ))
            
            # 排序并设置排名
            importances.sort(key=lambda x: x.importance_score, reverse=True)
            for i, importance in enumerate(importances):
                importance.rank = i + 1
            
            return importances
            
        except Exception as e:
            self.logger.warning(f"排列重要性计算失败: {str(e)}")
            return self._correlation_importance(features)
    
    def _shap_importance(self, features: Dict[str, float]) -> List[FeatureImportance]:
        """基于SHAP的重要性计算"""
        if not SHAP_AVAILABLE:
            return self._correlation_importance(features)
        
        # SHAP实现较复杂，这里提供简化版本
        return self._correlation_importance(features)
    
    def _train_surrogate_model(self):
        """训练代理模型"""
        if not SKLEARN_AVAILABLE or len(self.history_data) < 20:
            return
        
        try:
            # 准备训练数据
            feature_names = list(self.history_data[0]['features'].keys())
            X = []
            y = []
            
            for data in self.history_data[-200:]:
                feature_vector = [data['features'].get(name, 0.0) for name in feature_names]
                X.append(feature_vector)
                y.append(1 if data['prediction'] > 0.5 else 0)  # 二分类
            
            X = np.array(X)
            y = np.array(y)
            
            # 训练随机森林模型
            self.surrogate_model = RandomForestClassifier(
                n_estimators=50, max_depth=10, random_state=42
            )
            self.surrogate_model.fit(X, y)
            
        except Exception as e:
            self.logger.warning(f"代理模型训练失败: {str(e)}")
            self.surrogate_model = None
    
    def _default_importance(self, features: Dict[str, float]) -> List[FeatureImportance]:
        """默认重要性（当无法计算时）"""
        importances = []
        feature_names = list(features.keys())
        
        # 基于特征名称的启发式重要性
        priority_features = ['consensus_ratio', 'avg_confidence', 'weighted_consensus', 
                           'strategy_1_confidence', 'strategy_2_confidence', 'strategy_6_confidence']
        
        for i, feature_name in enumerate(feature_names):
            if feature_name in priority_features:
                importance_score = 0.8 - (priority_features.index(feature_name) * 0.1)
            else:
                importance_score = 0.3 / (i + 1)
            
            importances.append(FeatureImportance(
                feature_name=feature_name,
                importance_score=importance_score,
                rank=i + 1,
                method=ImportanceMethod.CORRELATION,
                description="默认重要性评分"
            ))
        
        return importances


class DecisionPathTracker:
    """决策路径跟踪器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.current_path = None
        self.step_counter = 0
    
    def start_decision_tracking(self, decision_id: str):
        """开始跟踪决策路径"""
        self.current_path = DecisionPath(
            decision_id=decision_id,
            steps=[],
            final_prediction=0.0,
            final_confidence=0.0,
            total_processing_time=0.0,
            critical_features=[],
            decision_factors={}
        )
        self.step_counter = 0
        self.start_time = time.time()
    
    def add_decision_step(self, layer_name: str, input_data: Dict[str, Any], 
                         output_data: Dict[str, Any], reasoning: str, 
                         confidence: float, contribution_score: float = 0.5):
        """添加决策步骤"""
        if self.current_path is None:
            return
        
        self.step_counter += 1
        step = DecisionStep(
            step_id=f"step_{self.step_counter}",
            layer_name=layer_name,
            input_data=input_data.copy(),
            output_data=output_data.copy(),
            reasoning=reasoning,
            confidence=confidence,
            contribution_score=contribution_score,
            timestamp=time.time()
        )
        
        self.current_path.steps.append(step)
    
    def finalize_decision_path(self, final_prediction: float, final_confidence: float) -> DecisionPath:
        """完成决策路径跟踪"""
        if self.current_path is None:
            return None
        
        self.current_path.final_prediction = final_prediction
        self.current_path.final_confidence = final_confidence
        self.current_path.total_processing_time = time.time() - self.start_time
        
        # 分析关键特征和决策因素
        self._analyze_critical_factors()
        
        path = self.current_path
        self.current_path = None
        return path
    
    def _analyze_critical_factors(self):
        """分析关键因素"""
        if not self.current_path.steps:
            return
        
        # 找出贡献度最高的步骤
        high_contribution_steps = [
            step for step in self.current_path.steps 
            if step.contribution_score > 0.7
        ]
        
        # 提取关键特征
        critical_features = set()
        decision_factors = {}
        
        for step in high_contribution_steps:
            # 从输入数据中提取重要特征
            for key, value in step.input_data.items():
                if isinstance(value, (int, float)) and abs(value) > 0.5:
                    critical_features.add(key)
            
            # 记录层级贡献
            decision_factors[step.layer_name] = step.contribution_score
        
        self.current_path.critical_features = list(critical_features)
        self.current_path.decision_factors = decision_factors


class CounterfactualGenerator:
    """反事实解释生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def generate_counterfactuals(self, features: Dict[str, float], 
                               prediction: float, target_prediction: float = None,
                               max_changes: int = 3) -> List[CounterfactualExplanation]:
        """生成反事实解释"""
        if target_prediction is None:
            target_prediction = 1.0 - prediction  # 相反的预测
        
        counterfactuals = []
        
        # 生成多个反事实场景
        for i in range(min(max_changes, 3)):
            counterfactual = self._generate_single_counterfactual(
                features, prediction, target_prediction, i + 1
            )
            if counterfactual:
                counterfactuals.append(counterfactual)
        
        return counterfactuals
    
    def _generate_single_counterfactual(self, features: Dict[str, float], 
                                      original_prediction: float, target_prediction: float,
                                      num_changes: int) -> CounterfactualExplanation:
        """生成单个反事实解释"""
        # 选择要修改的特征（基于重要性）
        important_features = ['consensus_ratio', 'avg_confidence', 'weighted_consensus']
        features_to_change = list(features.keys())[:num_changes]
        
        feature_changes = {}
        total_change = 0.0
        
        for feature_name in features_to_change:
            original_value = features[feature_name]
            
            # 计算需要的变化
            if feature_name in important_features:
                # 对重要特征进行更大的调整
                change_direction = 1 if target_prediction > original_prediction else -1
                new_value = original_value + (change_direction * 0.2)
            else:
                # 对其他特征进行小幅调整
                new_value = original_value + np.random.normal(0, 0.1)
            
            # 确保值在合理范围内
            new_value = max(0.0, min(1.0, new_value))
            
            feature_changes[feature_name] = (original_value, new_value)
            total_change += abs(new_value - original_value)
        
        # 估算可行性
        feasibility_score = max(0.1, 1.0 - (total_change / len(feature_changes)))
        
        # 生成解释
        explanation = self._generate_counterfactual_explanation(
            feature_changes, original_prediction, target_prediction
        )
        
        return CounterfactualExplanation(
            original_prediction=original_prediction,
            counterfactual_prediction=target_prediction,
            feature_changes=feature_changes,
            change_magnitude=total_change,
            feasibility_score=feasibility_score,
            explanation=explanation
        )
    
    def _generate_counterfactual_explanation(self, feature_changes: Dict[str, Tuple[float, float]],
                                           original_pred: float, target_pred: float) -> str:
        """生成反事实解释文本"""
        changes_desc = []
        for feature, (old_val, new_val) in feature_changes.items():
            direction = "增加" if new_val > old_val else "减少"
            change_pct = abs((new_val - old_val) / old_val * 100) if old_val != 0 else 0
            changes_desc.append(f"{feature} {direction} {change_pct:.1f}%")
        
        explanation = f"如果 {', '.join(changes_desc)}，预测结果将从 {original_pred:.3f} 变为 {target_pred:.3f}"
        return explanation


class RuleExtractor:
    """规则提取器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.decision_history = []
        self.extracted_rules = []

    def add_decision_record(self, features: Dict[str, float], prediction: float,
                          confidence: float, actual: Optional[int] = None):
        """添加决策记录用于规则提取"""
        record = {
            'features': features.copy(),
            'prediction': prediction,
            'confidence': confidence,
            'actual': actual,
            'timestamp': time.time()
        }
        self.decision_history.append(record)

        # 保持历史记录在合理范围内
        if len(self.decision_history) > 500:
            self.decision_history = self.decision_history[-400:]

    def extract_rules(self, min_support: int = 5, min_confidence: float = 0.7) -> List[RuleExplanation]:
        """提取决策规则"""
        if len(self.decision_history) < min_support * 2:
            return self._generate_default_rules()

        rules = []

        # 基于特征阈值的规则提取
        rules.extend(self._extract_threshold_rules(min_support, min_confidence))

        # 基于特征组合的规则提取
        rules.extend(self._extract_combination_rules(min_support, min_confidence))

        # 基于置信度的规则提取
        rules.extend(self._extract_confidence_rules(min_support, min_confidence))

        self.extracted_rules = rules
        return rules

    def _extract_threshold_rules(self, min_support: int, min_confidence: float) -> List[RuleExplanation]:
        """提取基于阈值的规则"""
        rules = []

        # 分析关键特征的阈值
        key_features = ['consensus_ratio', 'avg_confidence', 'weighted_consensus']

        for feature in key_features:
            if not any(feature in record['features'] for record in self.decision_history):
                continue

            # 收集该特征的值和对应的预测
            feature_values = []
            predictions = []

            for record in self.decision_history:
                if feature in record['features']:
                    feature_values.append(record['features'][feature])
                    predictions.append(1 if record['prediction'] > 0.5 else 0)

            if len(feature_values) < min_support:
                continue

            # 寻找有效的阈值
            thresholds = [0.3, 0.5, 0.7, 0.8]

            for threshold in thresholds:
                rule = self._evaluate_threshold_rule(
                    feature, threshold, feature_values, predictions, min_support, min_confidence
                )
                if rule:
                    rules.append(rule)

        return rules

    def _evaluate_threshold_rule(self, feature: str, threshold: float,
                                feature_values: List[float], predictions: List[int],
                                min_support: int, min_confidence: float) -> Optional[RuleExplanation]:
        """评估阈值规则"""
        # 计算满足条件的样本
        condition_met = [fv >= threshold for fv in feature_values]
        positive_predictions = [p == 1 for p in predictions]

        # 计算支持度和置信度
        support = sum(cm and pp for cm, pp in zip(condition_met, positive_predictions))
        total_condition_met = sum(condition_met)

        if total_condition_met < min_support:
            return None

        confidence = support / total_condition_met if total_condition_met > 0 else 0
        coverage = total_condition_met / len(feature_values)

        if confidence < min_confidence:
            return None

        # 生成规则
        condition = f"{feature} >= {threshold:.2f}"
        conclusion = "预测为正类"

        # 收集示例
        examples = []
        for i, (cm, fv, pred) in enumerate(zip(condition_met, feature_values, predictions)):
            if cm and pred == 1 and len(examples) < 3:
                examples.append({
                    'feature_value': fv,
                    'prediction': pred,
                    'index': i
                })

        return RuleExplanation(
            rule_id=f"threshold_{feature}_{threshold}",
            condition=condition,
            conclusion=conclusion,
            confidence=confidence,
            support=support,
            coverage=coverage,
            examples=examples
        )

    def _extract_combination_rules(self, min_support: int, min_confidence: float) -> List[RuleExplanation]:
        """提取基于特征组合的规则"""
        rules = []

        # 常见的特征组合
        combinations = [
            ['consensus_ratio', 'avg_confidence'],
            ['strategy_1_confidence', 'strategy_2_confidence'],
            ['weighted_consensus', 'consensus_ratio']
        ]

        for combo in combinations:
            if not all(any(feat in record['features'] for record in self.decision_history) for feat in combo):
                continue

            rule = self._evaluate_combination_rule(combo, min_support, min_confidence)
            if rule:
                rules.append(rule)

        return rules

    def _evaluate_combination_rule(self, features: List[str], min_support: int,
                                 min_confidence: float) -> Optional[RuleExplanation]:
        """评估组合规则"""
        # 收集数据
        valid_records = []
        for record in self.decision_history:
            if all(feat in record['features'] for feat in features):
                valid_records.append(record)

        if len(valid_records) < min_support:
            return None

        # 简化的组合规则：所有特征都高于平均值
        feature_means = {}
        for feat in features:
            values = [record['features'][feat] for record in valid_records]
            feature_means[feat] = np.mean(values)

        # 评估规则
        condition_met = []
        predictions = []

        for record in valid_records:
            meets_condition = all(
                record['features'][feat] >= feature_means[feat] for feat in features
            )
            condition_met.append(meets_condition)
            predictions.append(1 if record['prediction'] > 0.5 else 0)

        support = sum(cm and pred for cm, pred, pred in zip(condition_met, predictions, predictions))
        total_condition_met = sum(condition_met)

        if total_condition_met < min_support:
            return None

        confidence = support / total_condition_met if total_condition_met > 0 else 0
        coverage = total_condition_met / len(valid_records)

        if confidence < min_confidence:
            return None

        # 生成规则描述
        conditions = [f"{feat} >= {feature_means[feat]:.2f}" for feat in features]
        condition = " AND ".join(conditions)
        conclusion = "预测为正类"

        return RuleExplanation(
            rule_id=f"combination_{'_'.join(features)}",
            condition=condition,
            conclusion=conclusion,
            confidence=confidence,
            support=support,
            coverage=coverage,
            examples=[]
        )

    def _extract_confidence_rules(self, min_support: int, min_confidence: float) -> List[RuleExplanation]:
        """提取基于置信度的规则"""
        rules = []

        # 高置信度规则
        high_conf_records = [r for r in self.decision_history if r['confidence'] > 0.8]
        if len(high_conf_records) >= min_support:
            high_conf_correct = sum(1 for r in high_conf_records
                                  if r['actual'] is not None and
                                  (r['prediction'] > 0.5) == (r['actual'] == 1))

            if len(high_conf_records) > 0:
                accuracy = high_conf_correct / len([r for r in high_conf_records if r['actual'] is not None])

                if accuracy >= min_confidence:
                    rules.append(RuleExplanation(
                        rule_id="high_confidence_rule",
                        condition="置信度 > 0.8",
                        conclusion=f"预测准确率为 {accuracy:.2f}",
                        confidence=accuracy,
                        support=high_conf_correct,
                        coverage=len(high_conf_records) / len(self.decision_history),
                        examples=[]
                    ))

        return rules

    def _generate_default_rules(self) -> List[RuleExplanation]:
        """生成默认规则（当数据不足时）"""
        return [
            RuleExplanation(
                rule_id="default_consensus_rule",
                condition="consensus_ratio > 0.6",
                conclusion="倾向于预测多数策略的结果",
                confidence=0.7,
                support=10,
                coverage=0.5,
                examples=[]
            ),
            RuleExplanation(
                rule_id="default_confidence_rule",
                condition="avg_confidence > 0.7",
                conclusion="高置信度时预测更可靠",
                confidence=0.8,
                support=15,
                coverage=0.3,
                examples=[]
            )
        ]

    def get_applicable_rules(self, features: Dict[str, float]) -> List[RuleExplanation]:
        """获取适用于当前特征的规则"""
        applicable_rules = []

        for rule in self.extracted_rules:
            if self._rule_applies(rule, features):
                applicable_rules.append(rule)

        return applicable_rules

    def _rule_applies(self, rule: RuleExplanation, features: Dict[str, float]) -> bool:
        """检查规则是否适用于当前特征"""
        # 简化的规则匹配逻辑
        condition = rule.condition.lower()

        # 解析简单的条件
        if ">=" in condition:
            parts = condition.split(">=")
            if len(parts) == 2:
                feature_name = parts[0].strip()
                threshold = float(parts[1].strip())
                return features.get(feature_name, 0) >= threshold

        if ">" in condition:
            parts = condition.split(">")
            if len(parts) == 2:
                feature_name = parts[0].strip()
                threshold = float(parts[1].strip())
                return features.get(feature_name, 0) > threshold

        # 对于复杂条件，默认返回True
        return True


class DecisionExplainer:
    """决策解释器主类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化决策解释器

        Args:
            config: 配置字典
        """
        self.config = config.get('explainability', {})
        self.logger = logging.getLogger(__name__)

        # 初始化各个组件
        self.feature_calculator = FeatureImportanceCalculator(self.config)
        self.path_tracker = DecisionPathTracker(self.config)
        self.counterfactual_generator = CounterfactualGenerator(self.config)
        self.rule_extractor = RuleExtractor(self.config)

        # 解释历史
        self.explanation_history = []

        self.logger.info("决策解释器初始化完成")

    def start_decision_explanation(self, decision_id: str):
        """开始决策解释跟踪"""
        self.path_tracker.start_decision_tracking(decision_id)

    def add_layer_explanation(self, layer_name: str, input_data: Dict[str, Any],
                            output_data: Dict[str, Any], reasoning: str,
                            confidence: float, contribution_score: float = 0.5):
        """添加层级解释"""
        self.path_tracker.add_decision_step(
            layer_name, input_data, output_data, reasoning, confidence, contribution_score
        )

    def generate_explanation(self, decision_id: str, features: Dict[str, float],
                           prediction: float, confidence: float,
                           explanation_types: List[ExplanationType] = None) -> DecisionExplanation:
        """
        生成完整的决策解释

        Args:
            decision_id: 决策ID
            features: 输入特征
            prediction: 预测结果
            confidence: 置信度
            explanation_types: 需要的解释类型

        Returns:
            完整的决策解释
        """
        if explanation_types is None:
            explanation_types = [
                ExplanationType.FEATURE_IMPORTANCE,
                ExplanationType.DECISION_PATH,
                ExplanationType.RULE_BASED,
                ExplanationType.CONFIDENCE_BREAKDOWN
            ]

        # 生成各类解释
        feature_importances = []
        decision_path = None
        counterfactuals = []
        rules = []
        confidence_breakdown = {}
        layer_contributions = {}

        # 特征重要性
        if ExplanationType.FEATURE_IMPORTANCE in explanation_types:
            feature_importances = self.feature_calculator.calculate_feature_importance(features)

        # 决策路径
        if ExplanationType.DECISION_PATH in explanation_types:
            decision_path = self.path_tracker.finalize_decision_path(prediction, confidence)
            if decision_path:
                layer_contributions = decision_path.decision_factors

        # 反事实解释
        if ExplanationType.COUNTERFACTUAL in explanation_types:
            counterfactuals = self.counterfactual_generator.generate_counterfactuals(
                features, prediction
            )

        # 规则解释
        if ExplanationType.RULE_BASED in explanation_types:
            rules = self.rule_extractor.get_applicable_rules(features)

        # 置信度分解
        if ExplanationType.CONFIDENCE_BREAKDOWN in explanation_types:
            confidence_breakdown = self._analyze_confidence_breakdown(features, confidence)

        # 生成总结和详细推理
        summary = self._generate_summary(
            prediction, confidence, feature_importances, rules
        )
        detailed_reasoning = self._generate_detailed_reasoning(
            features, prediction, confidence, feature_importances,
            decision_path, rules, counterfactuals
        )

        # 创建完整解释
        explanation = DecisionExplanation(
            decision_id=decision_id,
            prediction=prediction,
            confidence=confidence,
            explanation_type=ExplanationType.FEATURE_IMPORTANCE,  # 主要类型
            feature_importances=feature_importances,
            decision_path=decision_path,
            counterfactuals=counterfactuals,
            rules=rules,
            confidence_breakdown=confidence_breakdown,
            layer_contributions=layer_contributions,
            summary=summary,
            detailed_reasoning=detailed_reasoning,
            timestamp=time.time()
        )

        # 记录解释历史
        self.explanation_history.append(explanation)
        if len(self.explanation_history) > 100:
            self.explanation_history = self.explanation_history[-80:]

        # 更新各组件的历史数据
        self.feature_calculator.add_decision_data(features, prediction)
        self.rule_extractor.add_decision_record(features, prediction, confidence)

        return explanation

    def _analyze_confidence_breakdown(self, features: Dict[str, float],
                                    confidence: float) -> Dict[str, float]:
        """分析置信度分解"""
        breakdown = {}

        # 基于特征的置信度贡献
        total_feature_contribution = 0
        for feature_name, value in features.items():
            if 'confidence' in feature_name.lower():
                contribution = value * 0.3  # 置信度相关特征贡献更多
            elif 'consensus' in feature_name.lower():
                contribution = value * 0.2  # 共识相关特征
            else:
                contribution = value * 0.1  # 其他特征

            breakdown[f"feature_{feature_name}"] = contribution
            total_feature_contribution += contribution

        # 归一化
        if total_feature_contribution > 0:
            for key in breakdown:
                breakdown[key] = (breakdown[key] / total_feature_contribution) * confidence

        # 添加系统置信度
        breakdown['system_base_confidence'] = max(0.1, confidence - sum(breakdown.values()))

        return breakdown

    def _generate_summary(self, prediction: float, confidence: float,
                         feature_importances: List[FeatureImportance],
                         rules: List[RuleExplanation]) -> str:
        """生成解释总结"""
        pred_label = "正类" if prediction > 0.5 else "负类"
        conf_level = "高" if confidence > 0.7 else "中" if confidence > 0.4 else "低"

        summary = f"预测结果: {pred_label} (置信度: {confidence:.3f}, {conf_level}置信度)"

        # 添加最重要的特征
        if feature_importances:
            top_feature = feature_importances[0]
            summary += f"\n关键因素: {top_feature.feature_name} (重要性: {top_feature.importance_score:.3f})"

        # 添加适用的规则
        if rules:
            summary += f"\n适用规则: {len(rules)}条规则支持此决策"

        return summary

    def _generate_detailed_reasoning(self, features: Dict[str, float], prediction: float,
                                   confidence: float, feature_importances: List[FeatureImportance],
                                   decision_path: Optional[DecisionPath], rules: List[RuleExplanation],
                                   counterfactuals: List[CounterfactualExplanation]) -> str:
        """生成详细推理过程"""
        reasoning_parts = []

        # 1. 输入特征分析
        reasoning_parts.append("=== 输入特征分析 ===")
        key_features = {k: v for k, v in features.items() if abs(v) > 0.1}
        for feature, value in list(key_features.items())[:5]:
            reasoning_parts.append(f"- {feature}: {value:.3f}")

        # 2. 特征重要性分析
        if feature_importances:
            reasoning_parts.append("\n=== 特征重要性分析 ===")
            for imp in feature_importances[:3]:
                reasoning_parts.append(
                    f"- {imp.feature_name}: {imp.importance_score:.3f} (排名: {imp.rank})"
                )

        # 3. 决策路径分析
        if decision_path and decision_path.steps:
            reasoning_parts.append("\n=== 决策路径分析 ===")
            for step in decision_path.steps:
                reasoning_parts.append(
                    f"- {step.layer_name}: {step.reasoning} (贡献度: {step.contribution_score:.3f})"
                )

        # 4. 规则匹配分析
        if rules:
            reasoning_parts.append("\n=== 规则匹配分析 ===")
            for rule in rules[:2]:
                reasoning_parts.append(
                    f"- 规则: {rule.condition} → {rule.conclusion} (置信度: {rule.confidence:.3f})"
                )

        # 5. 反事实分析
        if counterfactuals:
            reasoning_parts.append("\n=== 反事实分析 ===")
            for cf in counterfactuals[:1]:
                reasoning_parts.append(f"- {cf.explanation}")

        # 6. 最终结论
        reasoning_parts.append(f"\n=== 最终结论 ===")
        reasoning_parts.append(f"基于以上分析，系统预测结果为 {prediction:.3f}，置信度为 {confidence:.3f}")

        return "\n".join(reasoning_parts)

    def update_explanation_feedback(self, decision_id: str, actual_result: int):
        """更新解释反馈"""
        # 找到对应的解释
        explanation = None
        for exp in self.explanation_history:
            if exp.decision_id == decision_id:
                explanation = exp
                break

        if explanation:
            # 更新规则提取器的反馈
            features = {}
            if explanation.decision_path and explanation.decision_path.steps:
                for step in explanation.decision_path.steps:
                    features.update(step.input_data)

            self.rule_extractor.add_decision_record(
                features, explanation.prediction, explanation.confidence, actual_result
            )

            # 更新特征重要性计算器的反馈
            self.feature_calculator.add_decision_data(
                features, explanation.prediction, actual_result
            )

    def get_explanation_summary(self) -> Dict[str, Any]:
        """获取解释系统总结"""
        return {
            'total_explanations': len(self.explanation_history),
            'feature_importance_available': len(self.feature_calculator.history_data) > 0,
            'rules_extracted': len(self.rule_extractor.extracted_rules),
            'average_confidence': np.mean([exp.confidence for exp in self.explanation_history]) if self.explanation_history else 0.0,
            'explanation_types_supported': [t.value for t in ExplanationType],
            'importance_methods_available': [m.value for m in ImportanceMethod]
        }
