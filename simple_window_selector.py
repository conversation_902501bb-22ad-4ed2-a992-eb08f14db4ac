#!/usr/bin/env python3
"""
简单移动窗口策略选择器
基于移动窗口准确率选择最佳策略
"""

import sys
import os
import logging
import warnings
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SimpleWindowSelector:
    """简单移动窗口策略选择器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_window_accuracies(self, df: pd.DataFrame, window_size: int) -> pd.DataFrame:
        """计算移动窗口准确率"""
        self.logger.info(f"📈 计算窗口大小 {window_size} 的准确率...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 为每个策略计算移动窗口准确率
        for strategy in strategy_cols:
            accuracies = []
            
            for i in range(len(df)):
                # 计算当前位置的窗口准确率
                start_idx = max(0, i - window_size + 1)
                end_idx = i + 1
                
                window_data = df.iloc[start_idx:end_idx]
                if len(window_data) > 0:
                    correct = (window_data[strategy] == window_data['actual_result']).sum()
                    accuracy = correct / len(window_data)
                else:
                    accuracy = 0.5
                
                accuracies.append(accuracy)
            
            df[f'{strategy}_window_acc_{window_size}'] = accuracies
        
        return df
    
    def select_best_strategy_per_sample(self, df: pd.DataFrame, window_size: int) -> pd.DataFrame:
        """为每个样本选择最佳策略"""
        self.logger.info(f"🎯 基于窗口 {window_size} 选择最佳策略...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        selected_strategies = []
        selection_scores = []
        
        for i, row in df.iterrows():
            # 获取各策略的窗口准确率
            strategy_scores = {}
            
            for strategy in strategy_cols:
                acc_col = f'{strategy}_window_acc_{window_size}'
                if acc_col in df.columns:
                    strategy_scores[strategy] = row[acc_col]
                else:
                    strategy_scores[strategy] = 0.5
            
            # 选择准确率最高的策略
            best_strategy = max(strategy_scores, key=strategy_scores.get)
            best_score = strategy_scores[best_strategy]
            
            selected_strategies.append(best_strategy)
            selection_scores.append(best_score)
        
        df[f'selected_strategy_{window_size}'] = selected_strategies
        df[f'selection_score_{window_size}'] = selection_scores
        
        return df
    
    def evaluate_window_selector(self, df: pd.DataFrame, window_size: int) -> Dict[str, Any]:
        """评估窗口选择器"""
        self.logger.info(f"📊 评估窗口 {window_size} 选择器...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        selected_col = f'selected_strategy_{window_size}'
        
        # 计算选择器准确率
        correct_predictions = 0
        total_predictions = len(df)
        
        strategy_usage = {strategy: 0 for strategy in strategy_cols}
        
        for _, row in df.iterrows():
            selected_strategy = row[selected_col]
            prediction = row[selected_strategy]
            actual = row['actual_result']
            
            if prediction == actual:
                correct_predictions += 1
            
            strategy_usage[selected_strategy] += 1
        
        window_accuracy = correct_predictions / total_predictions
        
        # 对比基准
        baseline_accuracy = (df['strategy_1'] == df['actual_result']).mean()
        improvement = window_accuracy - baseline_accuracy
        
        self.logger.info(f"   📊 窗口 {window_size} 选择器准确率: {window_accuracy:.3f}")
        self.logger.info(f"   📊 相比基准提升: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        
        return {
            'window_size': window_size,
            'accuracy': window_accuracy,
            'improvement': improvement,
            'strategy_usage': strategy_usage
        }
    
    def test_multiple_windows(self, df: pd.DataFrame) -> Dict[str, Any]:
        """测试多个窗口大小"""
        self.logger.info("🔬 测试多个窗口大小...")
        
        window_sizes = [3, 5, 7, 10, 15, 20, 30, 50]
        results = {}
        best_window = None
        best_accuracy = 0
        
        for window_size in window_sizes:
            self.logger.info(f"\n🔄 测试窗口大小 {window_size}...")
            
            # 计算窗口准确率
            df = self.calculate_window_accuracies(df, window_size)
            
            # 选择策略
            df = self.select_best_strategy_per_sample(df, window_size)
            
            # 评估
            result = self.evaluate_window_selector(df, window_size)
            results[window_size] = result
            
            if result['accuracy'] > best_accuracy:
                best_accuracy = result['accuracy']
                best_window = window_size
        
        self.logger.info(f"\n🏆 最佳窗口大小: {best_window} (准确率: {best_accuracy:.3f})")
        
        return {
            'best_window': best_window,
            'best_accuracy': best_accuracy,
            'all_results': results
        }
    
    def analyze_strategy_switching(self, df: pd.DataFrame, best_window: int) -> Dict[str, Any]:
        """分析策略切换模式"""
        self.logger.info(f"🔄 分析策略切换模式 (窗口 {best_window})...")
        
        selected_col = f'selected_strategy_{best_window}'
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 统计策略切换
        switches = 0
        strategy_sequences = []
        
        prev_strategy = None
        for _, row in df.iterrows():
            current_strategy = row[selected_col]
            strategy_sequences.append(current_strategy)
            
            if prev_strategy is not None and current_strategy != prev_strategy:
                switches += 1
            
            prev_strategy = current_strategy
        
        switch_rate = switches / len(df)
        
        # 分析每个策略被选择的时机
        strategy_contexts = {}
        for strategy in strategy_cols:
            strategy_mask = df[selected_col] == strategy
            if strategy_mask.sum() > 0:
                strategy_data = df[strategy_mask]
                
                # 计算该策略被选择时的平均窗口准确率
                acc_col = f'{strategy}_window_acc_{best_window}'
                avg_window_acc = strategy_data[acc_col].mean()
                
                # 计算该策略的实际表现
                actual_performance = (strategy_data[strategy] == strategy_data['actual_result']).mean()
                
                strategy_contexts[strategy] = {
                    'selection_count': strategy_mask.sum(),
                    'avg_window_accuracy': avg_window_acc,
                    'actual_performance': actual_performance,
                    'selection_percentage': strategy_mask.sum() / len(df) * 100
                }
        
        self.logger.info(f"   📊 策略切换率: {switch_rate:.3f}")
        self.logger.info(f"   📊 各策略选择情况:")
        
        for strategy, context in strategy_contexts.items():
            self.logger.info(f"     {strategy}: 选择{context['selection_count']}次 "
                           f"({context['selection_percentage']:.1f}%), "
                           f"窗口准确率{context['avg_window_accuracy']:.3f}, "
                           f"实际表现{context['actual_performance']:.3f}")
        
        return {
            'switch_rate': switch_rate,
            'strategy_contexts': strategy_contexts
        }
    
    def run_optimization(self):
        """运行优化"""
        self.logger.info("🚀 开始简单移动窗口策略选择优化")
        self.logger.info("🎯 目标：利用98.5%理论上限，通过移动窗口实现智能策略选择")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，优化终止")
            return
        
        # 2. 测试多个窗口大小
        window_results = self.test_multiple_windows(df)
        
        # 3. 分析最佳窗口的策略切换
        best_window = window_results['best_window']
        switching_analysis = self.analyze_strategy_switching(df, best_window)
        
        # 4. 生成最终报告
        total_time = time.time() - start_time
        self.generate_final_report(window_results, switching_analysis, total_time)
    
    def generate_final_report(self, window_results: Dict[str, Any], 
                            switching_analysis: Dict[str, Any], total_time: float):
        """生成最终报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 简单移动窗口策略选择最终报告")
        self.logger.info("="*80)
        
        best_window = window_results['best_window']
        best_accuracy = window_results['best_accuracy']
        baseline_accuracy = 0.568
        improvement = best_accuracy - baseline_accuracy
        theoretical_limit = 0.985
        
        self.logger.info(f"\n📊 最终性能:")
        self.logger.info(f"   - 基准准确率 (strategy_1): {baseline_accuracy:.3f}")
        self.logger.info(f"   - 最佳窗口选择器准确率: {best_accuracy:.3f}")
        self.logger.info(f"   - 理论上限: {theoretical_limit:.3f}")
        self.logger.info(f"   - 实际提升: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        self.logger.info(f"   - 最佳窗口大小: {best_window}")
        
        if theoretical_limit > baseline_accuracy:
            realization_ratio = (best_accuracy - baseline_accuracy) / (theoretical_limit - baseline_accuracy)
            self.logger.info(f"   - 潜力实现: {realization_ratio*100:.1f}%")
        
        # 显示所有窗口的结果
        self.logger.info(f"\n📈 各窗口大小表现:")
        for window_size, result in window_results['all_results'].items():
            self.logger.info(f"   窗口 {window_size:2d}: 准确率 {result['accuracy']:.3f}, "
                           f"提升 {result['improvement']:+.3f}")
        
        target_accuracy = 0.60
        if best_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 成功达到目标准确率 {target_accuracy:.1%}!")
            self.logger.info("✅ 移动窗口策略选择系统已准备好投入生产！")
        else:
            gap = target_accuracy - best_accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f}")
            
            if improvement > 0.02:  # 提升超过2%
                self.logger.info("✅ 移动窗口选择显著有效！")
                self.logger.info("💡 证明了策略选择器的价值")
            elif improvement > 0.005:  # 提升超过0.5%
                self.logger.info("✅ 移动窗口选择有一定效果")
                self.logger.info("💡 方向正确，需要进一步优化")
            else:
                self.logger.info("⚠️ 移动窗口选择效果有限")
                self.logger.info("💡 可能需要更复杂的选择算法")
        
        # 关键结论
        self.logger.info(f"\n💡 关键结论:")
        if improvement > 0.01:
            self.logger.info("   ✅ 您是对的！选择器确实有问题，移动窗口方法有效！")
            self.logger.info("   ✅ 98.5%的理论上限可以部分实现")
            self.logger.info("   ✅ 其他策略确实有价值，关键是如何选择")
        elif improvement > 0.002:
            self.logger.info("   ✅ 移动窗口方法有微弱效果")
            self.logger.info("   💡 需要更智能的选择算法")
        else:
            self.logger.info("   ⚠️ 即使用移动窗口，选择效果仍然有限")
            self.logger.info("   💡 可能需要考虑其他因素")
        
        # 策略切换分析
        switch_rate = switching_analysis['switch_rate']
        self.logger.info(f"\n🔄 策略切换分析:")
        self.logger.info(f"   - 策略切换率: {switch_rate:.3f}")
        
        if switch_rate > 0.1:
            self.logger.info("   ✅ 系统能够动态切换策略")
        else:
            self.logger.info("   ⚠️ 策略切换较少，可能过于保守")
        
        self.logger.info(f"\n⏱️ 总优化时间: {total_time:.1f}秒")
        self.logger.info("="*80)

if __name__ == "__main__":
    optimizer = SimpleWindowSelector()
    optimizer.run_optimization()
