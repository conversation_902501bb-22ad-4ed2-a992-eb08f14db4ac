#!/usr/bin/env python3
"""
增强特征训练器
添加更多有意义的特征来提升模型性能
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class EnhancedTrainer:
    """增强特征训练器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            # 加载更多字段用于特征工程
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1,
                strategy_2,
                strategy_6,
                true_label as actual_result
            FROM strategy_results 
            WHERE strategy_1 IS NOT NULL 
                AND strategy_2 IS NOT NULL 
                AND strategy_6 IS NOT NULL
                AND true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载历史数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            for col in ['strategy_1', 'strategy_2', 'strategy_6']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in ['strategy_1', 'strategy_2', 'strategy_6', 'actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条历史数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def create_enhanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建增强特征"""
        self.logger.info("🔧 创建增强特征...")
        
        # 确保数据按boot_id和id排序
        df = df.sort_values(['boot_id', 'id']).reset_index(drop=True)
        
        # 1. 基础组合特征
        df['strategy_sum'] = df['strategy_1'] + df['strategy_2'] + df['strategy_6']
        df['strategy_majority'] = (df['strategy_sum'] >= 2).astype(int)
        df['strategy_unanimous'] = ((df['strategy_1'] == df['strategy_2']) & 
                                   (df['strategy_2'] == df['strategy_6'])).astype(int)
        
        # 2. 策略差异特征
        df['s1_vs_s2'] = (df['strategy_1'] != df['strategy_2']).astype(int)
        df['s1_vs_s6'] = (df['strategy_1'] != df['strategy_6']).astype(int)
        df['s2_vs_s6'] = (df['strategy_2'] != df['strategy_6']).astype(int)
        df['total_disagreement'] = df['s1_vs_s2'] + df['s1_vs_s6'] + df['s2_vs_s6']
        
        # 3. 历史窗口特征（滑动窗口）
        window_sizes = [3, 5, 10]
        
        for window in window_sizes:
            # 策略历史平均
            for strategy in ['strategy_1', 'strategy_2', 'strategy_6']:
                df[f'{strategy}_avg_{window}'] = df[strategy].rolling(window=window, min_periods=1).mean()
                df[f'{strategy}_std_{window}'] = df[strategy].rolling(window=window, min_periods=1).std().fillna(0)
            
            # 结果历史平均
            df[f'result_avg_{window}'] = df['actual_result'].shift(1).rolling(window=window, min_periods=1).mean()
            df[f'majority_avg_{window}'] = df['strategy_majority'].rolling(window=window, min_periods=1).mean()
        
        # 4. Boot内特征
        boot_features = df.groupby('boot_id').agg({
            'strategy_1': ['mean', 'std', 'sum'],
            'strategy_2': ['mean', 'std', 'sum'],
            'strategy_6': ['mean', 'std', 'sum'],
            'actual_result': ['mean', 'std', 'sum']
        }).round(4)
        
        # 扁平化列名
        boot_features.columns = ['_'.join(col).strip() for col in boot_features.columns]
        boot_features = boot_features.add_prefix('boot_')
        
        # 合并boot特征
        df = df.merge(boot_features, left_on='boot_id', right_index=True, how='left')
        
        # 5. 序列特征
        df['position_in_boot'] = df.groupby('boot_id').cumcount() + 1
        df['boot_progress'] = df.groupby('boot_id')['position_in_boot'].transform(lambda x: x / x.max())
        
        # 6. 趋势特征
        for strategy in ['strategy_1', 'strategy_2', 'strategy_6']:
            # 最近变化
            df[f'{strategy}_changed'] = (df[strategy] != df[strategy].shift(1)).astype(int)
            df[f'{strategy}_change_count_5'] = df[f'{strategy}_changed'].rolling(window=5, min_periods=1).sum()
            
            # 连续性
            df[f'{strategy}_consecutive'] = df.groupby((df[strategy] != df[strategy].shift()).cumsum()).cumcount() + 1
        
        # 7. 交互特征
        df['s1_s2_interaction'] = df['strategy_1'] * df['strategy_2']
        df['s1_s6_interaction'] = df['strategy_1'] * df['strategy_6']
        df['s2_s6_interaction'] = df['strategy_2'] * df['strategy_6']
        df['all_strategies_interaction'] = df['strategy_1'] * df['strategy_2'] * df['strategy_6']
        
        # 填充NaN值
        df = df.fillna(0)
        
        # 移除无限值
        df = df.replace([np.inf, -np.inf], 0)
        
        self.logger.info(f"   ✅ 创建了 {len(df.columns) - 6} 个增强特征")  # 减去原始6列
        
        return df
    
    def train_enhanced_models(self, df: pd.DataFrame) -> Dict[str, Any]:
        """训练增强模型"""
        try:
            from sklearn.model_selection import train_test_split, cross_val_score
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import accuracy_score, classification_report
            from sklearn.feature_selection import SelectKBest, f_classif
            import xgboost as xgb
            
            self.logger.info("🤖 开始增强模型训练...")
            
            # 准备特征和标签
            feature_cols = [col for col in df.columns if col not in ['id', 'boot_id', 'actual_result']]
            X = df[feature_cols].values
            y = df['actual_result'].values
            
            self.logger.info(f"   📊 特征维度: {X.shape}")
            self.logger.info(f"   📊 使用特征: {len(feature_cols)} 个")
            
            # 特征选择
            selector = SelectKBest(score_func=f_classif, k=min(20, len(feature_cols)))
            X_selected = selector.fit_transform(X, y)
            
            selected_features = [feature_cols[i] for i in selector.get_support(indices=True)]
            self.logger.info(f"   🎯 选择了 {len(selected_features)} 个最佳特征")
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X_selected, y, test_size=0.2, random_state=42, stratify=y
            )
            
            self.logger.info(f"   📊 数据分割: 训练集 {len(X_train)}, 测试集 {len(X_test)}")
            
            # 创建优化的模型
            models = {
                'logistic': LogisticRegression(random_state=42, max_iter=1000, C=1.0),
                'random_forest': RandomForestClassifier(
                    n_estimators=200, 
                    max_depth=10, 
                    min_samples_split=10,
                    min_samples_leaf=5,
                    random_state=42
                ),
                'gradient_boost': GradientBoostingClassifier(
                    n_estimators=150, 
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42
                ),
                'xgboost': xgb.XGBClassifier(
                    n_estimators=150,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42
                )
            }
            
            # 训练和评估
            results = {}
            for name, model in models.items():
                self.logger.info(f"   🔄 训练 {name}...")
                
                start_time = time.time()
                model.fit(X_train, y_train)
                train_time = time.time() - start_time
                
                # 预测和评估
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'train_time': train_time
                }
                
                self.logger.info(f"      ✅ {name}: 准确率={accuracy:.3f}, CV={cv_scores.mean():.3f}±{cv_scores.std():.3f}")
            
            # 创建集成模型
            self.logger.info("   🔗 创建集成模型...")
            ensemble = VotingClassifier(
                estimators=[(name, result['model']) for name, result in results.items()],
                voting='soft'
            )
            
            ensemble.fit(X_train, y_train)
            ensemble_pred = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
            
            # 集成模型交叉验证
            ensemble_cv = cross_val_score(ensemble, X_train, y_train, cv=5)
            
            self.logger.info(f"   🏆 集成模型: 准确率={ensemble_accuracy:.3f}, CV={ensemble_cv.mean():.3f}±{ensemble_cv.std():.3f}")
            
            return {
                'individual_models': results,
                'ensemble': ensemble,
                'ensemble_accuracy': ensemble_accuracy,
                'ensemble_cv_mean': ensemble_cv.mean(),
                'ensemble_cv_std': ensemble_cv.std(),
                'selected_features': selected_features,
                'feature_selector': selector
            }
            
        except Exception as e:
            self.logger.error(f"❌ 增强模型训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def run_enhanced_training(self):
        """运行增强训练"""
        self.logger.info("🚀 开始增强特征训练")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，训练终止")
            return
        
        # 2. 创建增强特征
        df_enhanced = self.create_enhanced_features(df)
        
        # 3. 训练模型
        results = self.train_enhanced_models(df_enhanced)
        
        if not results:
            self.logger.error("❌ 模型训练失败")
            return
        
        # 4. 生成报告
        total_time = time.time() - start_time
        self.generate_report(results, total_time, len(df))
    
    def generate_report(self, results: Dict[str, Any], total_time: float, total_samples: int):
        """生成训练报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 增强特征训练报告")
        self.logger.info("="*80)
        
        self.logger.info(f"\n📊 训练统计:")
        self.logger.info(f"   - 总样本数: {total_samples:,}")
        self.logger.info(f"   - 训练时间: {total_time:.1f}秒")
        self.logger.info(f"   - 选择特征数: {len(results.get('selected_features', []))}")
        
        if 'individual_models' in results:
            self.logger.info(f"\n🤖 个别模型结果:")
            for name, model_info in results['individual_models'].items():
                self.logger.info(f"   - {name:15}: 准确率={model_info['accuracy']:.3f}, CV={model_info['cv_mean']:.3f}±{model_info['cv_std']:.3f}")
        
        if 'ensemble_accuracy' in results:
            self.logger.info(f"\n🏆 集成模型结果:")
            self.logger.info(f"   - 测试准确率: {results['ensemble_accuracy']:.3f}")
            self.logger.info(f"   - 交叉验证: {results['ensemble_cv_mean']:.3f}±{results['ensemble_cv_std']:.3f}")
        
        # 显示重要特征
        if 'selected_features' in results:
            self.logger.info(f"\n🎯 重要特征 (前10个):")
            for i, feature in enumerate(results['selected_features'][:10]):
                self.logger.info(f"   {i+1:2d}. {feature}")
        
        # 评估结果
        target_accuracy = 0.60
        best_accuracy = results.get('ensemble_accuracy', 0)
        
        if best_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 训练成功！达到目标准确率 {target_accuracy:.1%}")
        else:
            improvement = best_accuracy - 0.568  # 与之前的基准比较
            self.logger.info(f"\n📈 准确率提升: {improvement:.3f} (从0.568到{best_accuracy:.3f})")
            if improvement > 0:
                self.logger.info("✅ 增强特征有效果！")
            else:
                self.logger.warning("⚠️ 增强特征效果有限")
        
        self.logger.info("="*80)

if __name__ == "__main__":
    trainer = EnhancedTrainer()
    trainer.run_enhanced_training()
