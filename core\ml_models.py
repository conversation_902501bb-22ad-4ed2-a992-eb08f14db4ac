#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模型层 (Layer 3)

实现多种机器学习模型：
1. 基础投票模型：简单多数投票、加权投票
2. 树模型：XGBoost、LightGBM
3. 神经网络：深度学习模型
4. 元学习器：学习何时使用哪个模型
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from collections import deque
from dataclasses import dataclass
from abc import ABC, abstractmethod

try:
    from .advanced_ensemble import AdvancedEnsembleLayer
    ADVANCED_ENSEMBLE_AVAILABLE = True
except ImportError:
    ADVANCED_ENSEMBLE_AVAILABLE = False


@dataclass
class ModelPrediction:
    """模型预测结果"""
    model_name: str
    prediction: float      # 0-1之间的概率
    confidence: float      # 置信度
    reasoning: str         # 预测理由
    

class BaseModel(ABC):
    """基础模型抽象类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"Model.{name}")
        
        # 模型状态
        self.is_trained = False
        self.training_data = deque(maxlen=10000)
        self.performance_history = deque(maxlen=1000)
        
    @abstractmethod
    def predict(self, features: Dict[str, float]) -> ModelPrediction:
        """预测"""
        pass
    
    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练模型"""
        pass
    
    @abstractmethod
    def update_online(self, features: Dict[str, float], target: int):
        """在线学习更新"""
        pass


class VotingModel(BaseModel):
    """投票模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("voting", config)
        self.voting_config = config.get('ml_models', {}).get('voting', {})
        
    def predict(self, features: Dict[str, float]) -> ModelPrediction:
        """基于特征进行投票预测"""
        
        # 简单多数投票
        consensus_ratio = features.get('consensus_ratio', 0.5)
        
        # 加权投票 (考虑置信度)
        weighted_consensus = features.get('weighted_consensus', 0.5)
        
        # 综合预测
        if self.voting_config.get('weighted_voting', {}).get('enabled', True):
            prediction = weighted_consensus
            reasoning = "基于加权投票"
        else:
            prediction = consensus_ratio
            reasoning = "基于简单投票"
        
        # 计算置信度
        confidence = self._calculate_confidence(features)
        
        return ModelPrediction(
            model_name=self.name,
            prediction=prediction,
            confidence=confidence,
            reasoning=reasoning
        )
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """投票模型不需要训练"""
        self.is_trained = True
        
    def update_online(self, features: Dict[str, float], target: int):
        """更新投票权重"""
        # 简化的在线权重更新
        pass
    
    def _calculate_confidence(self, features: Dict[str, float]) -> float:
        """计算置信度"""
        # 基于一致性和历史表现计算置信度
        consensus_strength = abs(features.get('consensus_ratio', 0.5) - 0.5) * 2
        avg_confidence = features.get('avg_confidence', 0.5)
        
        return (consensus_strength + avg_confidence) / 2


class XGBoostModel(BaseModel):
    """XGBoost模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("xgboost", config)
        self.xgb_config = config.get('ml_models', {}).get('xgboost', {})
        self.model = None
        
        # 尝试导入XGBoost
        try:
            import xgboost as xgb
            self.xgb = xgb
        except ImportError:
            self.logger.warning("XGBoost未安装，使用简化版本")
            self.xgb = None
    
    def predict(self, features: Dict[str, float]) -> ModelPrediction:
        """XGBoost预测"""
        if not self.is_trained or self.model is None:
            # 未训练时使用简单逻辑
            prediction = features.get('weighted_consensus', 0.5)
            confidence = 0.5
            reasoning = "模型未训练，使用默认逻辑"
        else:
            # 使用训练好的模型
            X = self._features_to_array(features)
            if self.xgb:
                prediction = float(self.model.predict_proba(X.reshape(1, -1))[0][1])
            else:
                prediction = features.get('weighted_consensus', 0.5)
            
            confidence = self._calculate_confidence(features, prediction)
            reasoning = "XGBoost模型预测"
        
        return ModelPrediction(
            model_name=self.name,
            prediction=prediction,
            confidence=confidence,
            reasoning=reasoning
        )
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练XGBoost模型"""
        if self.xgb is None:
            self.logger.warning("XGBoost不可用，跳过训练")
            return
        
        try:
            from sklearn.model_selection import train_test_split
            from sklearn.ensemble import RandomForestClassifier
            
            # 如果XGBoost不可用，使用RandomForest作为替代
            if len(X) < 100:
                self.logger.warning("训练数据不足，跳过训练")
                return
            
            # 分割数据
            X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # 训练模型
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=42
            )
            self.model.fit(X_train, y_train)
            
            # 验证性能
            val_score = self.model.score(X_val, y_val)
            self.logger.info(f"XGBoost模型训练完成，验证准确率: {val_score:.4f}")
            
            self.is_trained = True
            
        except Exception as e:
            self.logger.error(f"XGBoost训练失败: {str(e)}")
    
    def update_online(self, features: Dict[str, float], target: int):
        """在线学习更新"""
        # 添加到训练数据
        feature_array = self._features_to_array(features)
        self.training_data.append((feature_array, target))
        
        # 定期重训练
        if len(self.training_data) % 200 == 0:
            self._retrain_from_buffer()
    
    def _features_to_array(self, features: Dict[str, float]) -> np.ndarray:
        """将特征字典转换为数组 (包含策略级连胜连败特征)"""
        # 定义完整的特征顺序（与训练时保持一致，包含策略级特征）
        feature_names = [
            # 基础特征
            'consensus_ratio', 'weighted_consensus', 'avg_confidence',
            'min_confidence', 'max_confidence', 'confidence_std',
            'total_divergence', 'max_divergence',
            'strategy_1_prediction', 'strategy_2_prediction', 'strategy_6_prediction',
            'strategy_1_confidence', 'strategy_2_confidence', 'strategy_6_confidence',
            'win_rate_10', 'win_rate_5', 'current_streak',
            'strategy_1_weight', 'strategy_2_weight', 'strategy_6_weight',

        ]

        # 动态添加8个策略的特征
        for i in range(1, 9):
            strategy = f'strategy_{i}'
            feature_names.extend([
                f'{strategy}_current_streak', f'{strategy}_max_win_streak', f'{strategy}_max_loss_streak',
                f'{strategy}_winrate_3', f'{strategy}_winrate_5', f'{strategy}_winrate_10', f'{strategy}_winrate_20',
                f'{strategy}_winrate_trend', f'{strategy}_weighted_winrate',
                f'{strategy}_stability_3', f'{strategy}_stability_5', f'{strategy}_stability_10'
            ])

        # 策略比较特征
        feature_names.extend([
            'best_winrate_strategy', 'best_winrate_value', 'winrate_range', 'winrate_std',
            'best_winrate_10_strategy', 'winrate_10_range', 'best_weighted_strategy', 'weighted_winrate_range'
        ])

        feature_array = []
        for name in feature_names:
            value = features.get(name, 0.0)
            # 处理字符串类型的特征
            if isinstance(value, str):
                if 'strategy_1' in value:
                    value = 1.0
                elif 'strategy_2' in value:
                    value = 2.0
                elif 'strategy_6' in value:
                    value = 6.0
                else:
                    value = 0.0
            feature_array.append(float(value))

        return np.array(feature_array)
    
    def _calculate_confidence(self, features: Dict[str, float], prediction: float) -> float:
        """计算置信度"""
        # 基于预测概率的置信度
        confidence = abs(prediction - 0.5) * 2
        
        # 结合特征置信度
        feature_confidence = features.get('avg_confidence', 0.5)
        
        return (confidence + feature_confidence) / 2
    
    def _retrain_from_buffer(self):
        """从缓冲区重新训练"""
        if len(self.training_data) < 50:
            return
        
        # 准备训练数据
        X = np.array([item[0] for item in self.training_data])
        y = np.array([item[1] for item in self.training_data])
        
        # 重新训练
        self.train(X, y)


class NeuralNetworkModel(BaseModel):
    """神经网络模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("neural_network", config)
        self.nn_config = config.get('ml_models', {}).get('neural_network', {})
        self.model = None
        
    def predict(self, features: Dict[str, float]) -> ModelPrediction:
        """神经网络预测"""
        if not self.is_trained or self.model is None:
            # 如果模型未训练，使用基础逻辑
            prediction = features.get('weighted_consensus', 0.5)
            confidence = features.get('avg_confidence', 0.5)
            reasoning = "神经网络预测 (未训练状态)"
        else:
            # 使用训练好的模型进行预测
            try:
                # 准备特征向量
                feature_vector = self._prepare_feature_vector(features)

                # 模型预测
                prediction = self.model.predict_proba([feature_vector])[0][1]

                # 计算置信度
                confidence = min(0.9, abs(prediction - 0.5) * 2 + 0.4)
                reasoning = "神经网络预测 (已训练)"

            except Exception as e:
                self.logger.warning(f"神经网络预测失败: {str(e)}")
                prediction = features.get('weighted_consensus', 0.5)
                confidence = 0.3
                reasoning = "神经网络预测 (降级模式)"

        return ModelPrediction(
            model_name=self.name,
            prediction=prediction,
            confidence=confidence,
            reasoning=reasoning
        )
    
    def train(self, X: np.ndarray, y: np.ndarray):
        """训练神经网络"""
        try:
            from sklearn.neural_network import MLPClassifier

            # 配置神经网络参数
            hidden_layers = self.nn_config.get('hidden_layers', [64, 32])
            max_iter = self.nn_config.get('max_iter', 1000)
            learning_rate = self.nn_config.get('learning_rate_init', 0.001)

            # 创建和训练模型
            self.model = MLPClassifier(
                hidden_layer_sizes=tuple(hidden_layers),
                max_iter=max_iter,
                learning_rate_init=learning_rate,
                random_state=42,
                early_stopping=True,
                validation_fraction=0.1
            )

            self.model.fit(X, y)
            self.is_trained = True

            # 计算训练准确率
            train_score = self.model.score(X, y)
            self.logger.info(f"神经网络训练完成，训练准确率: {train_score:.3f}")

        except Exception as e:
            self.logger.error(f"神经网络训练失败: {str(e)}")
            self.is_trained = False

    def _prepare_feature_vector(self, features: Dict[str, float]) -> np.ndarray:
        """准备特征向量 (与训练时保持一致的完整特征集)"""
        # 使用与XGBoost相同的完整特征集，确保维度一致
        feature_names = [
            # 基础特征
            'consensus_ratio', 'weighted_consensus', 'avg_confidence',
            'min_confidence', 'max_confidence', 'confidence_std',
            'total_divergence', 'max_divergence',
            'strategy_1_prediction', 'strategy_2_prediction', 'strategy_6_prediction',
            'strategy_1_confidence', 'strategy_2_confidence', 'strategy_6_confidence',
            'win_rate_10', 'win_rate_5', 'current_streak',
            'strategy_1_weight', 'strategy_2_weight', 'strategy_6_weight',

        ]

        # 动态添加8个策略的特征
        for i in range(1, 9):
            strategy = f'strategy_{i}'
            feature_names.extend([
                f'{strategy}_current_streak', f'{strategy}_max_win_streak', f'{strategy}_max_loss_streak',
                f'{strategy}_winrate_3', f'{strategy}_winrate_5', f'{strategy}_winrate_10', f'{strategy}_winrate_20',
                f'{strategy}_winrate_trend', f'{strategy}_weighted_winrate',
                f'{strategy}_stability_3', f'{strategy}_stability_5', f'{strategy}_stability_10'
            ])

        # 策略比较特征
        feature_names.extend([
            'best_winrate_strategy', 'best_winrate_value', 'winrate_range', 'winrate_std',
            'best_winrate_10_strategy', 'winrate_10_range', 'best_weighted_strategy', 'weighted_winrate_range'
        ])

        feature_vector = []
        for feature_name in feature_names:
            value = features.get(feature_name, 0.0)
            if isinstance(value, (int, float, np.number)):
                feature_vector.append(float(value))
            elif isinstance(value, str):
                # 处理字符串特征
                if 'strategy_1' in value:
                    feature_vector.append(1.0)
                elif 'strategy_2' in value:
                    feature_vector.append(2.0)
                elif 'strategy_6' in value:
                    feature_vector.append(6.0)
                else:
                    feature_vector.append(0.0)
            else:
                feature_vector.append(0.0)

        return np.array(feature_vector)
    
    def update_online(self, features: Dict[str, float], target: int):
        """在线学习更新"""
        pass


class StrategySelectionModel(BaseModel):
    """策略选择模型 (核心新功能)"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("strategy_selector", config)
        self.selector_config = config.get('ml_models', {}).get('strategy_selector', {})
        self.model = None
        self.strategy_mapping = {f'strategy_{i}': i-1 for i in range(1, 9)}  # 8个策略

    def predict(self, features: Dict[str, float]) -> ModelPrediction:
        """策略选择预测"""
        if not self.is_trained or self.model is None:
            # 未训练时使用基于连胜连败的简单逻辑
            selected_strategy, confidence, reasoning = self._simple_strategy_selection(features)
        else:
            # 使用训练好的模型
            try:
                feature_vector = self._prepare_strategy_features(features)
                strategy_probs = self.model.predict_proba([feature_vector])[0]

                # 选择概率最高的策略
                best_strategy_idx = np.argmax(strategy_probs)
                strategy_names = [f'strategy_{i}' for i in range(1, 9)]  # 8个策略
                selected_strategy = strategy_names[best_strategy_idx]

                confidence = float(strategy_probs[best_strategy_idx])
                reasoning = f"ML模型选择策略 (概率: {confidence:.3f})"

            except Exception as e:
                self.logger.warning(f"策略选择模型预测失败: {e}")
                selected_strategy, confidence, reasoning = self._simple_strategy_selection(features)

        # 获取选中策略的预测值
        strategy_prediction = features.get(f'{selected_strategy}_prediction', 0.5)

        return ModelPrediction(
            model_name=self.name,
            prediction=strategy_prediction,
            confidence=confidence,
            reasoning=f"{reasoning} -> {selected_strategy} -> {strategy_prediction}"
        )

    def _simple_strategy_selection(self, features: Dict[str, float]) -> Tuple[str, float, str]:
        """基于连胜连败的简单策略选择 (8个策略)"""
        strategies = [f'strategy_{i}' for i in range(1, 9)]  # 8个策略
        scores = {}

        for strategy in strategies:
            score = 0.5  # 基础分数

            # 连胜连败评分
            current_streak = features.get(f'{strategy}_current_streak', 0)
            if current_streak > 0:
                score += min(current_streak * 0.1, 0.3)  # 连胜奖励
            elif current_streak < -2:
                score += 0.1  # 连败反弹机会

            # 近期胜率评分
            winrate_5 = features.get(f'{strategy}_winrate_5', 0.5)
            score += (winrate_5 - 0.5) * 0.4

            # 加权胜率评分
            weighted_winrate = features.get(f'{strategy}_weighted_winrate', 0.5)
            score += (weighted_winrate - 0.5) * 0.3

            # 最大连胜奖励
            max_win_streak = features.get(f'{strategy}_max_win_streak', 0)
            if max_win_streak > 3:
                score += min((max_win_streak - 3) * 0.02, 0.1)

            # 胜率趋势
            winrate_trend = features.get(f'{strategy}_winrate_trend', 0)
            score += winrate_trend * 0.2

            scores[strategy] = score

        # 选择得分最高的策略
        best_strategy = max(scores, key=scores.get)
        best_score = scores[best_strategy]

        confidence = min(0.9, max(0.3, best_score))
        reasoning = f"8策略连胜连败选择 (得分: {best_score:.3f})"

        return best_strategy, confidence, reasoning

    def _prepare_strategy_features(self, features: Dict[str, float]) -> np.ndarray:
        """准备策略选择特征 (8个策略，与训练时保持一致)"""
        strategy_features = []

        # 使用所有8个策略，每个策略9个特征
        for i in range(1, 9):
            strategy = f'strategy_{i}'
            # 与训练脚本中的特征顺序完全一致
            strategy_features.extend([
                features.get(f'{strategy}_current_streak', 0),      # 当前连胜连败
                features.get(f'{strategy}_max_win_streak', 0),      # 最大连胜
                features.get(f'{strategy}_max_loss_streak', 0),     # 最大连败
                features.get(f'{strategy}_winrate_3', 0.5),         # 3手窗口胜率
                features.get(f'{strategy}_winrate_5', 0.5),         # 5手窗口胜率
                features.get(f'{strategy}_winrate_10', 0.5),        # 10手窗口胜率
                features.get(f'{strategy}_winrate_20', 0.5),        # 20手窗口胜率
                features.get(f'{strategy}_weighted_winrate', 0.5),  # 加权胜率
                features.get(f'{strategy}_winrate_trend', 0)        # 胜率趋势
            ])

        return np.array(strategy_features)

    def train(self, X: np.ndarray, y: np.ndarray):
        """训练策略选择模型"""
        try:
            from sklearn.ensemble import RandomForestClassifier

            # 创建策略选择模型
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=8,
                random_state=42,
                class_weight='balanced'
            )

            self.model.fit(X, y)
            self.is_trained = True

            # 计算训练准确率
            train_score = self.model.score(X, y)
            self.logger.info(f"策略选择模型训练完成，训练准确率: {train_score:.3f}")

        except Exception as e:
            self.logger.error(f"策略选择模型训练失败: {e}")
            self.is_trained = False

    def update_online(self, features: Dict[str, float], target: int):
        """在线学习更新"""
        # 添加到训练数据
        feature_array = self._prepare_strategy_features(features)
        self.training_data.append((feature_array, target))

        # 定期重训练
        if len(self.training_data) % 100 == 0:
            self._retrain_from_buffer()

    def _retrain_from_buffer(self):
        """从缓冲区重新训练"""
        if len(self.training_data) < 30:
            return

        X = np.array([item[0] for item in self.training_data])
        y = np.array([item[1] for item in self.training_data])

        self.train(X, y)


class MetaLearner(BaseModel):
    """元学习器"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("meta_learner", config)
        self.meta_config = config.get('ml_models', {}).get('meta_learner', {})
        self.model_performances = {}

    def predict(self, features: Dict[str, float]) -> ModelPrediction:
        """元学习预测"""
        # 简化的元学习逻辑
        # 根据特征选择最佳模型

        if features.get('total_divergence', 0) > 1.5:
            # 高分歧时使用投票模型
            best_model = "voting"
            confidence = 0.7
        elif features.get('avg_confidence', 0) > 0.7:
            # 高置信度时使用XGBoost
            best_model = "xgboost"
            confidence = 0.8
        else:
            # 默认使用加权平均
            best_model = "ensemble"
            confidence = 0.6

        prediction = features.get('weighted_consensus', 0.5)
        reasoning = f"元学习器选择: {best_model}"

        return ModelPrediction(
            model_name=self.name,
            prediction=prediction,
            confidence=confidence,
            reasoning=reasoning
        )

    def train(self, X: np.ndarray, y: np.ndarray):
        """训练元学习器"""
        self.is_trained = True
        self.logger.info("元学习器训练完成")

    def update_online(self, features: Dict[str, float], target: int):
        """更新模型性能统计"""
        pass


class MLModelLayer:
    """
    机器学习模型层管理器
    
    管理多个ML模型的协调工作
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化模型
        self.models = {}
        
        ml_config = config.get('ml_models', {})
        
        if ml_config.get('voting', {}).get('enabled', True):
            self.models['voting'] = VotingModel(config)
            
        if ml_config.get('xgboost', {}).get('enabled', True):
            self.models['xgboost'] = XGBoostModel(config)
            
        if ml_config.get('neural_network', {}).get('enabled', True):
            self.models['neural_network'] = NeuralNetworkModel(config)
            
        if ml_config.get('meta_learner', {}).get('enabled', True):
            self.models['meta_learner'] = MetaLearner(config)

        # 策略选择模型 (核心新功能)
        if ml_config.get('strategy_selector', {}).get('enabled', True):
            self.models['strategy_selector'] = StrategySelectionModel(config)

        # 高级集成学习层
        if ADVANCED_ENSEMBLE_AVAILABLE and ml_config.get('advanced_ensemble', {}).get('enabled', True):
            self.advanced_ensemble = AdvancedEnsembleLayer(config)
        else:
            self.advanced_ensemble = None

        self.logger.info(f"机器学习模型层初始化完成，加载了 {len(self.models)} 个模型")
    
    def predict(self, features: Dict[str, float]) -> Dict[str, ModelPrediction]:
        """获取所有模型的预测"""
        predictions = {}
        
        for name, model in self.models.items():
            try:
                prediction = model.predict(features)
                predictions[name] = prediction
                self.logger.debug(f"{name} 预测: {prediction.prediction:.3f}")
            except Exception as e:
                self.logger.error(f"{name} 预测失败: {str(e)}")
        
        return predictions

    def get_ensemble_prediction(self, features: Dict[str, float], method: str = 'auto') -> ModelPrediction:
        """
        获取高级集成预测

        Args:
            features: 特征字典
            method: 集成方法 ('stacking', 'blending', 'auto')

        Returns:
            集成预测结果
        """
        # 获取基础模型预测
        base_predictions = self.predict(features)

        if self.advanced_ensemble:
            # 使用高级集成学习
            base_pred_dict = {name: pred.prediction for name, pred in base_predictions.items()}
            ensemble_result = self.advanced_ensemble.predict(base_pred_dict, method)

            return ModelPrediction(
                model_name=f"ensemble_{ensemble_result.method}",
                prediction=ensemble_result.prediction,
                confidence=ensemble_result.confidence,
                reasoning=ensemble_result.reasoning
            )
        else:
            # 简单平均集成
            if base_predictions:
                avg_prediction = sum(pred.prediction for pred in base_predictions.values()) / len(base_predictions)
                avg_confidence = sum(pred.confidence for pred in base_predictions.values()) / len(base_predictions)

                return ModelPrediction(
                    model_name="ensemble_average",
                    prediction=avg_prediction,
                    confidence=avg_confidence,
                    reasoning="简单平均集成"
                )
            else:
                return ModelPrediction(
                    model_name="ensemble_default",
                    prediction=0.5,
                    confidence=0.1,
                    reasoning="无可用模型"
                )
    
    def update_feedback(self, decision_id: str, actual_result: int,
                       base_predictions: Optional[Dict[str, float]] = None):
        """
        更新模型反馈

        Args:
            decision_id: 决策ID
            actual_result: 实际结果
            base_predictions: 基础模型预测（用于集成学习训练）
        """
        # 更新各个模型的在线学习
        for model in self.models.values():
            try:
                # 这里需要获取对应的特征，简化实现
                features = {}  # 实际应该从决策历史中获取
                model.update_online(features, actual_result)
            except Exception as e:
                self.logger.error(f"模型 {model.name} 反馈更新失败: {str(e)}")

        # 更新高级集成学习
        if self.advanced_ensemble and base_predictions:
            self.advanced_ensemble.collect_training_data(base_predictions, actual_result)
    
    def online_learning_update(self):
        """触发在线学习更新"""
        self.logger.info("执行在线学习更新")
        # 实现在线学习逻辑
        pass
    
    def get_model_status(self) -> Dict[str, Any]:
        """获取模型状态"""
        status = {}
        for name, model in self.models.items():
            status[name] = {
                'is_trained': model.is_trained,
                'training_data_size': len(model.training_data)
            }
        return status
    
    def load_models(self, model_persistence):
        """加载保存的模型"""
        # 实现模型加载逻辑
        pass
