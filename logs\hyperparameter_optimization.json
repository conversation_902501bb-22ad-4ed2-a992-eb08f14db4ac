{"xgboost": {"best_params": {"colsample_bytree": 1.0, "learning_rate": 0.01, "max_depth": 3, "n_estimators": 50, "subsample": 0.8}, "best_score": 0.5764970367669019}, "random_forest": {"best_params": {"n_estimators": 50, "min_samples_split": 2, "min_samples_leaf": 2, "max_features": "sqrt", "max_depth": 5}, "best_score": 0.5629977803890848}, "neural_network": {"best_params": {"max_iter": 500, "learning_rate": "constant", "hidden_layer_sizes": [50, 25], "alpha": 0.001, "activation": "tanh"}, "best_score": 0.5724900312606459}}