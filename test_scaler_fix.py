#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试StandardScaler修复

验证深度学习模型的StandardScaler问题是否已修复
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from main import SimpleFusionV8
    print("✅ 成功导入V8系统")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_scaler_fix():
    """测试StandardScaler修复"""
    print("\n🧪 测试StandardScaler修复...")
    
    # 创建V8系统
    system = SimpleFusionV8()
    
    # 初始化系统（这里会预热模型）
    print("📊 初始化V8系统...")
    system.initialize()
    
    print("✅ 系统初始化完成")
    
    # 进行几次决策测试
    print("\n🎯 进行决策测试...")
    
    for i in range(5):
        try:
            # 模拟外部数据
            external_data = {
                'decision_id': f"scaler_test_{i}",
                'current_boot': {'boot_id': 100 + i, 'position': i % 40}
            }
            
            # 处理决策
            decision = system.process_decision(external_data)
            
            print(f"   决策 {i+1}: 预测={decision.prediction}, 置信度={decision.confidence:.3f}")
            
            # 模拟反馈
            actual_result = np.random.choice([0, 1])
            system.update_feedback(decision.decision_id, actual_result)
            
        except Exception as e:
            print(f"   ❌ 决策 {i+1} 失败: {str(e)}")
            return False
    
    print("✅ 所有决策测试通过")
    
    # 检查深度学习模型状态
    print("\n📊 检查深度学习模型状态...")
    
    try:
        dl_info = system.deep_learning_models.get_model_info()
        print(f"   模型数量: {dl_info['total_models']}")
        print(f"   PyTorch可用: {dl_info['pytorch_available']}")
        print(f"   设备: {dl_info['device']}")
        
        for model_name, model_info in dl_info['models'].items():
            print(f"   {model_name}: {model_info['type']}")
        
        # 检查scaler状态
        for model_name, scaler in system.deep_learning_models.scalers.items():
            if scaler and hasattr(scaler, 'scale_'):
                if scaler.scale_ is not None:
                    print(f"   ✅ {model_name} scaler已拟合")
                else:
                    print(f"   ⚠️ {model_name} scaler未拟合")
            else:
                print(f"   ℹ️ {model_name} 无scaler或scaler不可用")
        
    except Exception as e:
        print(f"   ❌ 检查模型状态失败: {str(e)}")
        return False
    
    print("✅ 深度学习模型状态检查完成")
    
    return True


def test_direct_prediction():
    """直接测试深度学习预测"""
    print("\n🧪 直接测试深度学习预测...")
    
    try:
        from core.deep_learning_models import DeepLearningModelLayer
        
        # 创建深度学习模型层
        config = {
            'lstm': {'enabled': True},
            'gru': {'enabled': True},
            'transformer': {'enabled': True}
        }
        
        dl_layer = DeepLearningModelLayer(config)
        
        # 创建测试特征历史
        features_history = []
        for i in range(10):
            features = {
                'consensus_ratio': 0.5 + np.random.normal(0, 0.1),
                'weighted_consensus': 0.5 + np.random.normal(0, 0.1),
                'avg_confidence': 0.6 + np.random.normal(0, 0.1),
                'min_confidence': 0.4 + np.random.normal(0, 0.05),
                'max_confidence': 0.8 + np.random.normal(0, 0.05),
                'confidence_std': 0.1 + np.random.normal(0, 0.02),
                'total_divergence': 0.2 + np.random.normal(0, 0.05),
                'max_divergence': 0.3 + np.random.normal(0, 0.05),
                'strategy_1_prediction': np.random.choice([0, 1]),
                'strategy_2_prediction': np.random.choice([0, 1]),
                'strategy_6_prediction': np.random.choice([0, 1]),
                'strategy_1_confidence': 0.7 + np.random.normal(0, 0.1),
                'strategy_2_confidence': 0.6 + np.random.normal(0, 0.1),
                'strategy_6_confidence': 0.8 + np.random.normal(0, 0.1),
                'win_rate_10': 0.55 + np.random.normal(0, 0.1),
                'win_rate_5': 0.6 + np.random.normal(0, 0.1),
                'current_streak': np.random.randint(-5, 6),
                'strategy_1_weight': 0.33 + np.random.normal(0, 0.05),
                'strategy_2_weight': 0.33 + np.random.normal(0, 0.05),
                'strategy_6_weight': 0.34 + np.random.normal(0, 0.05)
            }
            features_history.append(features)
        
        # 预热模型
        sample_features = features_history[0]
        dl_layer.warm_up_models(sample_features)
        
        print("   模型预热完成")
        
        # 测试每个模型的预测
        for model_name in dl_layer.models.keys():
            try:
                prediction = dl_layer.predict(model_name, features_history)
                print(f"   ✅ {model_name}: 预测={prediction.prediction:.3f}, 置信度={prediction.confidence:.3f}")
            except Exception as e:
                print(f"   ❌ {model_name}: 预测失败 - {str(e)}")
                return False
        
        # 测试集成预测
        try:
            ensemble_pred = dl_layer.get_ensemble_prediction(features_history)
            print(f"   ✅ 集成预测: 预测={ensemble_pred.prediction:.3f}, 置信度={ensemble_pred.confidence:.3f}")
        except Exception as e:
            print(f"   ❌ 集成预测失败: {str(e)}")
            return False
        
        print("✅ 直接深度学习预测测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 直接预测测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 StandardScaler修复测试")
    print("=" * 50)
    
    # 测试1: 直接深度学习预测
    success1 = test_direct_prediction()
    
    # 测试2: 完整V8系统
    success2 = test_scaler_fix()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 所有测试通过！StandardScaler问题已修复")
        print("\n📋 修复总结:")
        print("   ✅ 添加了scaler拟合状态检查")
        print("   ✅ 实现了自动拟合机制")
        print("   ✅ 添加了模型预热功能")
        print("   ✅ 增强了错误处理")
    else:
        print("❌ 部分测试失败，需要进一步检查")
        if not success1:
            print("   - 直接深度学习预测测试失败")
        if not success2:
            print("   - V8系统集成测试失败")
    
    return success1 and success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
