#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多目标决策优化功能

验证多目标优化算法、帕累托前沿搜索、决策参数优化等功能
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from core.multi_objective_optimization import (
        MultiObjectiveDecisionManager, ParetoOptimizer, OptimizationConfig,
        AccuracyObjective, RiskObjective, ProfitabilityObjective,
        RiskLimitConstraint, ConfidenceConstraint
    )
    from main import SimpleFusionV8
    print("✅ 成功导入多目标优化模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def create_mock_context():
    """创建模拟上下文数据"""
    return {
        'strategy_outputs': {
            'strategy_1': {'prediction': 1, 'confidence': 0.7},
            'strategy_2': {'prediction': 0, 'confidence': 0.6},
            'strategy_6': {'prediction': 1, 'confidence': 0.8}
        },
        'features': {
            'consensus_ratio': 0.6,
            'weighted_consensus': 0.65,
            'avg_confidence': 0.7,
            'win_rate_10': 0.55,
            'current_streak': 2
        },
        'ml_predictions': {
            'voting': 0.6,
            'xgboost': 0.7,
            'neural_network': 0.65,
            'meta_learner': 0.68
        },
        'current_risk': 0.4,
        'market_volatility': 0.3
    }


def test_objective_functions():
    """测试目标函数"""
    print("\n🧪 测试目标函数...")
    
    # 测试准确率目标函数
    accuracy_obj = AccuracyObjective(weight=1.0)
    
    # 添加一些历史数据
    for i in range(20):
        prediction = np.random.choice([0, 1])
        actual = np.random.choice([0, 1])
        accuracy_obj.update_history(prediction, actual)
    
    decision_vars = {
        'confidence_threshold': 0.6,
        'risk_tolerance': 0.5,
        'position_size': 0.7
    }
    
    context = create_mock_context()
    
    accuracy_value = accuracy_obj.evaluate(decision_vars, context)
    print(f"   准确率目标值: {accuracy_value:.4f}")
    
    # 测试风险目标函数
    risk_obj = RiskObjective(weight=0.8)
    
    # 添加一些损失历史
    for i in range(15):
        loss = np.random.normal(0, 0.5)
        risk_obj.update_loss(loss)
    
    risk_value = risk_obj.evaluate(decision_vars, context)
    print(f"   风险目标值: {risk_value:.4f}")
    
    # 测试盈利能力目标函数
    profit_obj = ProfitabilityObjective(weight=1.2)
    
    # 添加一些收益历史
    for i in range(25):
        return_val = np.random.normal(0.1, 0.3)
        profit_obj.update_return(return_val)
    
    profit_value = profit_obj.evaluate(decision_vars, context)
    print(f"   盈利能力目标值: {profit_value:.4f}")
    
    print(f"✅ 目标函数测试完成")
    
    return accuracy_obj, risk_obj, profit_obj


def test_constraint_functions():
    """测试约束函数"""
    print("\n🧪 测试约束函数...")
    
    decision_vars = {
        'confidence_threshold': 0.6,
        'risk_tolerance': 0.3,
        'position_size': 0.8
    }
    
    context = create_mock_context()
    
    # 测试风险限制约束
    risk_constraint = RiskLimitConstraint(max_risk=0.7)
    risk_violation = risk_constraint.evaluate(decision_vars, context)
    print(f"   风险约束违反值: {risk_violation:.4f} ({'违反' if risk_violation > 0 else '满足'})")
    
    # 测试置信度约束
    confidence_constraint = ConfidenceConstraint(min_confidence=0.3)
    confidence_violation = confidence_constraint.evaluate(decision_vars, context)
    print(f"   置信度约束违反值: {confidence_violation:.4f} ({'违反' if confidence_violation > 0 else '满足'})")
    
    print(f"✅ 约束函数测试完成")
    
    return risk_constraint, confidence_constraint


def test_pareto_optimizer():
    """测试帕累托优化器"""
    print("\n🧪 测试帕累托优化器...")
    
    # 创建优化配置
    config = OptimizationConfig(
        objectives=[
            {'type': 'accuracy', 'weight': 1.0},
            {'type': 'risk', 'weight': 0.8},
            {'type': 'profitability', 'weight': 1.2}
        ],
        constraints=[
            {'type': 'risk_limit', 'params': {'max_risk': 0.8}},
            {'type': 'confidence_limit', 'params': {'min_confidence': 0.2}}
        ],
        decision_variables={
            'confidence_threshold': {'min': 0.2, 'max': 0.9},
            'risk_tolerance': {'min': 0.1, 'max': 1.0},
            'position_size': {'min': 0.1, 'max': 1.0},
            'strategy_weight_1': {'min': 0.0, 'max': 1.0},
            'strategy_weight_2': {'min': 0.0, 'max': 1.0},
            'strategy_weight_6': {'min': 0.0, 'max': 1.0}
        },
        algorithm='nsga2',
        max_iterations=20,
        population_size=20,
        convergence_threshold=1e-6
    )
    
    # 创建优化器
    optimizer = ParetoOptimizer(config)
    
    # 添加一些历史数据
    for obj in optimizer.objectives:
        if hasattr(obj, 'update_history'):
            for i in range(30):
                prediction = np.random.choice([0, 1])
                actual = np.random.choice([0, 1])
                obj.update_history(prediction, actual)
        elif hasattr(obj, 'update_return'):
            for i in range(30):
                return_val = np.random.normal(0.05, 0.2)
                obj.update_return(return_val)
        elif hasattr(obj, 'update_loss'):
            for i in range(30):
                loss = np.random.normal(0, 0.3)
                obj.update_loss(loss)
    
    context = create_mock_context()
    
    print(f"📊 开始帕累托优化...")
    print(f"   目标函数数: {len(optimizer.objectives)}")
    print(f"   约束函数数: {len(optimizer.constraints)}")
    print(f"   决策变量数: {len(config.decision_variables)}")
    
    # 执行优化
    pareto_front = optimizer.optimize(context, num_solutions=30)
    
    print(f"✅ 帕累托优化完成:")
    print(f"   帕累托前沿解数: {len(pareto_front)}")
    
    if pareto_front:
        # 显示前5个解
        print(f"   前5个帕累托最优解:")
        for i, solution in enumerate(pareto_front[:5]):
            print(f"     解 {i+1}: 总分={solution.total_score:.4f}, 排名={solution.pareto_rank}")
            print(f"       决策变量: {solution.decision_variables}")
            print(f"       目标值: {[(obj.name, obj.value) for obj in solution.objectives]}")
        
        # 获取最佳解
        best_solution = optimizer.get_best_solution()
        print(f"   最佳解总分: {best_solution.total_score:.4f}")
        
        # 获取优化总结
        summary = optimizer.get_optimization_summary()
        print(f"   优化总结: {summary}")
    
    return optimizer, pareto_front


def test_multi_objective_manager():
    """测试多目标决策管理器"""
    print("\n🧪 测试多目标决策管理器...")
    
    # 创建配置
    config = {
        'multi_objective': {
            'optimization_frequency': 20,
            'objectives': [
                {'type': 'accuracy', 'weight': 1.0},
                {'type': 'risk', 'weight': 0.8},
                {'type': 'profitability', 'weight': 1.2}
            ],
            'constraints': [
                {'type': 'risk_limit', 'params': {'max_risk': 0.75}},
                {'type': 'confidence_limit', 'params': {'min_confidence': 0.25}}
            ],
            'max_iterations': 15,
            'population_size': 20
        }
    }
    
    # 创建管理器
    manager = MultiObjectiveDecisionManager(config)
    
    print(f"📊 多目标决策管理器初始化完成")
    
    # 模拟决策和反馈过程
    for i in range(50):
        # 模拟决策
        context = create_mock_context()
        
        # 检查是否需要优化
        should_optimize = manager.should_optimize(i)
        
        if should_optimize:
            print(f"   步骤 {i}: 触发优化")
            optimized_solution = manager.optimize_decision_parameters(context)
            
            if optimized_solution:
                print(f"     优化解总分: {optimized_solution.total_score:.4f}")
                print(f"     决策参数: {optimized_solution.decision_variables}")
                
                # 应用解到决策上下文
                updated_context = manager.apply_solution(optimized_solution, context)
                print(f"     应用后的策略权重: {updated_context.get('strategy_weights', {})}")
        
        # 模拟反馈
        prediction = np.random.choice([0, 1])
        actual_result = np.random.choice([0, 1])
        return_value = 1.0 if prediction == actual_result else -0.5
        loss = 0.0 if prediction == actual_result else 1.0
        
        manager.update_feedback(f"decision_{i}", prediction, actual_result, return_value, loss)
        
        # 定期显示状态
        if i % 20 == 0 and i > 0:
            current_params = manager.get_current_parameters()
            print(f"   步骤 {i}: 当前参数 = {current_params}")
    
    # 获取最终状态
    final_status = manager.get_optimization_status()
    
    print(f"✅ 多目标决策管理器测试完成:")
    print(f"   有当前解: {final_status['has_current_solution']}")
    print(f"   决策历史长度: {final_status['decision_history_length']}")
    print(f"   性能历史长度: {final_status['performance_history_length']}")
    
    if final_status['has_current_solution']:
        current_solution = final_status['current_solution']
        print(f"   当前最优解:")
        print(f"     总分: {current_solution['total_score']:.4f}")
        print(f"     帕累托排名: {current_solution['pareto_rank']}")
        print(f"     可行性: {current_solution['is_feasible']}")
    
    return manager, final_status


def test_v8_integration():
    """测试V8系统集成"""
    print("\n🧪 测试V8系统多目标优化集成...")
    
    # 创建V8系统
    system = SimpleFusionV8()
    system.initialize()
    
    print("✅ V8系统初始化完成")
    
    # 进行多次决策以触发多目标优化
    results = []
    
    for i in range(150):  # 超过优化频率阈值
        try:
            # 模拟外部数据
            external_data = {
                'decision_id': f"multi_obj_test_{i}",
                'current_boot': {'boot_id': 500 + i, 'position': i % 40}
            }
            
            # 处理决策
            decision = system.process_decision(external_data)
            
            # 模拟实际结果
            actual_result = np.random.choice([0, 1])
            
            # 更新反馈
            system.update_feedback(decision.decision_id, actual_result)
            
            results.append({
                'decision_id': decision.decision_id,
                'prediction': decision.prediction,
                'actual': actual_result,
                'correct': decision.prediction == actual_result,
                'confidence': decision.confidence
            })
            
            # 定期显示状态
            if i % 50 == 0 and i > 0:
                status = system.get_system_status()
                if 'multi_objective_optimization' in status:
                    mo_status = status['multi_objective_optimization']
                    print(f"   决策 {i+1}: 多目标优化状态")
                    print(f"     有当前解: {mo_status['has_current_solution']}")
                    if mo_status['has_current_solution']:
                        current_sol = mo_status['current_solution']
                        print(f"     当前解总分: {current_sol['total_score']:.4f}")
            
        except Exception as e:
            print(f"   ❌ 决策 {i+1} 失败: {str(e)}")
    
    # 获取最终系统状态
    final_status = system.get_system_status()
    
    print(f"✅ V8多目标优化集成测试完成:")
    print(f"   总决策数: {len(results)}")
    
    if results:
        accuracy = sum(r['correct'] for r in results) / len(results)
        avg_confidence = sum(r['confidence'] for r in results) / len(results)
        print(f"   准确率: {accuracy:.4f}")
        print(f"   平均置信度: {avg_confidence:.4f}")
    
    if 'multi_objective_optimization' in final_status:
        mo_status = final_status['multi_objective_optimization']
        print(f"   多目标优化状态:")
        print(f"     有当前解: {mo_status['has_current_solution']}")
        print(f"     优化频率: {mo_status['optimization_frequency']}")
        
        if mo_status['has_current_solution']:
            current_solution = mo_status['current_solution']
            print(f"     最优解总分: {current_solution['total_score']:.4f}")
            print(f"     决策参数: {current_solution['decision_variables']}")
    
    return system, results


def main():
    """主测试函数"""
    print("🚀 多目标决策优化测试")
    print("=" * 60)
    
    # 1. 测试目标函数
    accuracy_obj, risk_obj, profit_obj = test_objective_functions()
    
    # 2. 测试约束函数
    risk_constraint, confidence_constraint = test_constraint_functions()
    
    # 3. 测试帕累托优化器
    optimizer, pareto_front = test_pareto_optimizer()
    
    # 4. 测试多目标决策管理器
    manager, manager_status = test_multi_objective_manager()
    
    # 5. 测试V8系统集成
    system, v8_results = test_v8_integration()
    
    print("\n" + "=" * 60)
    print("🎉 多目标决策优化测试完成！")
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"   ✅ 目标函数: 准确率、风险、盈利能力目标正常工作")
    print(f"   ✅ 约束函数: 风险限制、置信度约束正常工作")
    print(f"   ✅ 帕累托优化: 生成了 {len(pareto_front)} 个最优解")
    print(f"   ✅ 决策管理器: 有当前解 = {manager_status['has_current_solution']}")
    
    if v8_results:
        v8_accuracy = sum(r['correct'] for r in v8_results) / len(v8_results)
        print(f"   ✅ V8系统集成: 准确率 {v8_accuracy:.4f}")
    
    # 性能对比
    if pareto_front:
        best_solution = max(pareto_front, key=lambda x: x.total_score)
        print(f"   🏆 最佳解总分: {best_solution.total_score:.4f}")
        print(f"   🏆 最佳决策参数: {best_solution.decision_variables}")
    
    return {
        'objectives': [accuracy_obj, risk_obj, profit_obj],
        'constraints': [risk_constraint, confidence_constraint],
        'optimizer': optimizer,
        'manager': manager,
        'system': system,
        'results': {
            'pareto_front': pareto_front,
            'manager_status': manager_status,
            'v8_results': v8_results
        }
    }


if __name__ == "__main__":
    test_results = main()
