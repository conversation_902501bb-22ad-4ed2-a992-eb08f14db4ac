#!/usr/bin/env python3
"""
Boot级别特征训练器
专注于Boot级别的特征工程，基于LightGBM的重要特征发现
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class BootLevelTrainer:
    """Boot级别特征训练器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data_with_boot_analysis(self) -> pd.DataFrame:
        """加载数据并进行Boot级别分析"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据并分析Boot特征...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            
            # Boot级别统计
            boot_stats = df.groupby('boot_id').agg({
                'actual_result': ['count', 'mean', 'std'],
                'strategy_1': ['mean', 'std', 'sum'],
                'strategy_2': ['mean', 'std'],
                'strategy_3': ['mean', 'std'],
                'strategy_4': ['mean', 'std'],
                'strategy_5': ['mean', 'std'],
                'strategy_6': ['mean', 'std'],
                'strategy_7': ['mean', 'std'],
                'strategy_8': ['mean', 'std']
            })
            
            boot_stats.columns = ['_'.join(col).strip() for col in boot_stats.columns]
            
            self.logger.info(f"📈 Boot统计:")
            self.logger.info(f"   - 总Boot数: {len(boot_stats)}")
            self.logger.info(f"   - 平均每Boot样本数: {boot_stats['actual_result_count'].mean():.1f}")
            self.logger.info(f"   - Boot准确率范围: {boot_stats['actual_result_mean'].min():.3f} - {boot_stats['actual_result_mean'].max():.3f}")
            
            return df, boot_stats
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def create_boot_level_features(self, df: pd.DataFrame, boot_stats: pd.DataFrame) -> pd.DataFrame:
        """创建Boot级别的高级特征"""
        self.logger.info("\n🔧 创建Boot级别高级特征...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. 基础Boot特征
        df = df.merge(boot_stats, left_on='boot_id', right_index=True, how='left')
        
        # 2. Boot内位置特征
        df['position_in_boot'] = df.groupby('boot_id').cumcount() + 1
        df['boot_size'] = df.groupby('boot_id')['boot_id'].transform('count')
        df['boot_progress'] = df['position_in_boot'] / df['boot_size']
        
        # Boot阶段
        df['boot_stage'] = pd.cut(df['boot_progress'], 
                                 bins=[0, 0.25, 0.5, 0.75, 1.0], 
                                 labels=['early', 'mid_early', 'mid_late', 'late'],
                                 include_lowest=True)
        df['boot_stage_encoded'] = df['boot_stage'].cat.codes
        
        # 3. Boot内趋势特征
        self.logger.info("   📈 Boot内趋势特征...")
        
        # 策略趋势
        for strategy in ['strategy_1', 'strategy_sum']:
            if strategy == 'strategy_sum':
                df['strategy_sum'] = df[strategy_cols].sum(axis=1)
            
            # Boot内累积平均
            df[f'{strategy}_boot_cumavg'] = df.groupby('boot_id')[strategy].expanding().mean().reset_index(0, drop=True)
            
            # Boot内趋势
            df[f'{strategy}_boot_trend'] = df.groupby('boot_id')[strategy].diff().fillna(0)
            
            # Boot内动量
            df[f'{strategy}_boot_momentum'] = df.groupby('boot_id')[strategy].rolling(window=3, min_periods=1).apply(
                lambda x: x.iloc[-1] - x.iloc[0] if len(x) > 1 else 0
            ).reset_index(0, drop=True).fillna(0)
        
        # 4. Boot间比较特征
        self.logger.info("   🔄 Boot间比较特征...")
        
        # 当前Boot vs 历史Boot平均
        df['boot_vs_historical_s1'] = df['strategy_1'] - df['strategy_1_mean']
        df['boot_vs_historical_sum'] = df['strategy_sum'] - (df['strategy_1_sum'] + 
                                                            df['strategy_2_mean'] + df['strategy_3_mean'] + 
                                                            df['strategy_4_mean'] + df['strategy_5_mean'] + 
                                                            df['strategy_6_mean'] + df['strategy_7_mean'] + 
                                                            df['strategy_8_mean']) * df['boot_size']
        
        # 5. Boot质量特征
        self.logger.info("   🎯 Boot质量特征...")
        
        # Boot一致性
        df['boot_consistency'] = 1 / (1 + df['actual_result_std'].fillna(0))
        
        # Boot预测难度
        df['boot_difficulty'] = np.abs(df['actual_result_mean'] - 0.5) * 2  # 距离50%的程度
        
        # 策略在Boot内的稳定性
        df['strategy_1_stability'] = 1 / (1 + df['strategy_1_std'].fillna(0))
        
        # 6. 多Boot历史特征
        self.logger.info("   📚 多Boot历史特征...")
        
        # 按时间顺序的Boot特征
        boot_order = df.groupby('boot_id')['id'].min().sort_values()
        boot_order_map = {boot_id: idx for idx, boot_id in enumerate(boot_order.index)}
        df['boot_order'] = df['boot_id'].map(boot_order_map)
        
        # 历史Boot窗口特征
        for window in [3, 5, 10]:
            # 历史Boot的strategy_1平均表现
            boot_s1_history = df.groupby('boot_id')['strategy_1_mean'].first().rolling(window=window, min_periods=1).mean()
            df[f'historical_boot_s1_avg_{window}'] = df['boot_id'].map(boot_s1_history)
            
            # 历史Boot的准确率
            boot_acc_history = df.groupby('boot_id')['actual_result_mean'].first().rolling(window=window, min_periods=1).mean()
            df[f'historical_boot_acc_avg_{window}'] = df['boot_id'].map(boot_acc_history)
        
        # 7. 复合Boot特征
        self.logger.info("   🔗 复合Boot特征...")
        
        # Boot质量 × 策略强度
        df['boot_quality_x_strategy'] = df['boot_consistency'] * df['strategy_1_mean']
        
        # Boot进度 × 策略趋势
        df['progress_x_trend'] = df['boot_progress'] * df['strategy_1_boot_trend']
        
        # Boot难度调整的策略权重
        df['difficulty_adjusted_strategy'] = df['strategy_1'] * (1 + df['boot_difficulty'])
        
        # 8. 时间序列Boot特征
        self.logger.info("   ⏰ 时间序列Boot特征...")
        
        # Boot内时间衰减权重
        df['time_weight'] = np.exp(-0.1 * (df['boot_size'] - df['position_in_boot']))
        
        # 加权策略值
        df['weighted_strategy_1'] = df['strategy_1'] * df['time_weight']
        
        # Boot内累积信息
        df['cumulative_info'] = df.groupby('boot_id')['strategy_1'].expanding().sum().reset_index(0, drop=True)
        df['cumulative_info_normalized'] = df['cumulative_info'] / df['position_in_boot']
        
        # 处理分类变量
        if 'boot_stage' in df.columns:
            df['boot_stage'] = df['boot_stage'].cat.add_categories([0]).fillna(0)

        # 填充NaN
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(0)
        df[numeric_cols] = df[numeric_cols].replace([np.inf, -np.inf], 0)
        
        # 统计特征数量
        original_cols = len(strategy_cols) + 3  # 原始策略 + id, boot_id, actual_result
        new_features = len(df.columns) - original_cols
        self.logger.info(f"   ✅ 创建了 {new_features} 个Boot级别特征")
        
        return df
    
    def train_boot_optimized_models(self, df: pd.DataFrame) -> Dict[str, Any]:
        """训练Boot优化模型"""
        try:
            from sklearn.model_selection import train_test_split, cross_val_score
            from sklearn.linear_model import LogisticRegression, Ridge
            from sklearn.ensemble import RandomForestClassifier, VotingClassifier
            from sklearn.metrics import accuracy_score
            import lightgbm as lgb
            
            self.logger.info("\n🤖 开始Boot优化模型训练...")
            
            # 准备特征
            exclude_cols = ['id', 'boot_id', 'actual_result', 'boot_stage'] + [f'strategy_{i}' for i in range(1, 9)]
            feature_cols = [col for col in df.columns if col not in exclude_cols]
            
            X = df[feature_cols].values
            y = df['actual_result'].values
            
            self.logger.info(f"   📊 特征维度: {X.shape}")
            self.logger.info(f"   📊 Boot级别特征: {len(feature_cols)} 个")
            
            # 分割数据 - 按Boot分割以避免数据泄露
            unique_boots = df['boot_id'].unique()
            train_boots, test_boots = train_test_split(unique_boots, test_size=0.2, random_state=42)
            
            train_mask = df['boot_id'].isin(train_boots)
            test_mask = df['boot_id'].isin(test_boots)
            
            X_train, X_test = X[train_mask], X[test_mask]
            y_train, y_test = y[train_mask], y[test_mask]
            
            self.logger.info(f"   📊 训练Boot数: {len(train_boots)}, 测试Boot数: {len(test_boots)}")
            self.logger.info(f"   📊 训练样本: {len(X_train)}, 测试样本: {len(X_test)}")
            
            # 创建模型
            models = {
                'logistic': LogisticRegression(random_state=42, max_iter=1000, C=0.1),
                'logistic_ridge': LogisticRegression(random_state=42, max_iter=1000, C=0.01),
                'random_forest': RandomForestClassifier(
                    n_estimators=150, max_depth=6, min_samples_split=10,
                    min_samples_leaf=5, random_state=42
                ),
                'lightgbm': lgb.LGBMClassifier(
                    n_estimators=150, max_depth=5, learning_rate=0.05,
                    subsample=0.9, colsample_bytree=0.9, random_state=42, verbose=-1
                )
            }
            
            # 训练和评估
            results = {}
            for name, model in models.items():
                self.logger.info(f"   🔄 训练 {name}...")
                
                start_time = time.time()
                model.fit(X_train, y_train)
                train_time = time.time() - start_time
                
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                # 使用Boot级别的交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'train_time': train_time
                }
                
                self.logger.info(f"      ✅ {name}: 准确率={accuracy:.3f}, CV={cv_scores.mean():.3f}±{cv_scores.std():.3f}")
            
            # 创建集成模型
            self.logger.info("   🔗 创建Boot优化集成...")
            
            # 选择最佳模型进行集成
            top_models = sorted(results.items(), key=lambda x: x[1]['cv_mean'], reverse=True)[:2]
            
            ensemble = VotingClassifier(
                estimators=[(name, result['model']) for name, result in top_models],
                voting='soft'
            )
            
            ensemble.fit(X_train, y_train)
            ensemble_pred = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
            
            self.logger.info(f"   🏆 Boot优化集成: 准确率={ensemble_accuracy:.3f}")
            
            # 特征重要性分析
            if 'lightgbm' in results:
                lgb_model = results['lightgbm']['model']
                if hasattr(lgb_model, 'feature_importances_'):
                    importances = lgb_model.feature_importances_
                    feature_importance = dict(zip(feature_cols, importances))
                    top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
                    
                    self.logger.info(f"\n   🎯 Top 10 Boot特征:")
                    for i, (feature, importance) in enumerate(top_features):
                        self.logger.info(f"      {i+1:2d}. {feature}: {importance:.1f}")
            
            return {
                'individual_models': results,
                'ensemble': ensemble,
                'ensemble_accuracy': ensemble_accuracy,
                'feature_importance': top_features if 'top_features' in locals() else [],
                'feature_names': feature_cols,
                'test_boots': len(test_boots),
                'train_boots': len(train_boots)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Boot优化训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def run_boot_training(self):
        """运行Boot级别训练"""
        self.logger.info("🚀 开始Boot级别优化训练")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据和Boot分析
        df, boot_stats = self.load_data_with_boot_analysis()
        if df.empty:
            self.logger.error("❌ 无法加载数据，训练终止")
            return
        
        # 2. 创建Boot级别特征
        df_boot = self.create_boot_level_features(df, boot_stats)
        
        # 3. 训练Boot优化模型
        results = self.train_boot_optimized_models(df_boot)
        
        if not results:
            self.logger.error("❌ 模型训练失败")
            return
        
        # 4. 生成报告
        total_time = time.time() - start_time
        self.generate_report(results, total_time, len(df), len(boot_stats))
    
    def generate_report(self, results: Dict[str, Any], total_time: float, 
                       total_samples: int, total_boots: int):
        """生成训练报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 Boot级别优化训练报告")
        self.logger.info("="*80)
        
        self.logger.info(f"\n📊 训练统计:")
        self.logger.info(f"   - 总样本数: {total_samples:,}")
        self.logger.info(f"   - 总Boot数: {total_boots}")
        self.logger.info(f"   - 训练时间: {total_time:.1f}秒")
        self.logger.info(f"   - Boot特征数: {len(results.get('feature_names', []))}")
        self.logger.info(f"   - 训练Boot数: {results.get('train_boots', 0)}")
        self.logger.info(f"   - 测试Boot数: {results.get('test_boots', 0)}")
        
        if 'individual_models' in results:
            self.logger.info(f"\n🤖 模型结果:")
            for name, model_info in results['individual_models'].items():
                self.logger.info(f"   - {name:15}: 准确率={model_info['accuracy']:.3f}, CV={model_info['cv_mean']:.3f}±{model_info['cv_std']:.3f}")
        
        self.logger.info(f"\n🏆 Boot优化集成: {results.get('ensemble_accuracy', 0):.3f}")
        
        if results.get('feature_importance'):
            self.logger.info(f"\n🎯 重要Boot特征:")
            for i, (feature, importance) in enumerate(results['feature_importance'][:5]):
                self.logger.info(f"   {i+1}. {feature}: {importance:.1f}")
        
        # 性能分析
        baseline_accuracy = 0.568
        best_accuracy = results.get('ensemble_accuracy', 0)
        improvement = best_accuracy - baseline_accuracy
        
        self.logger.info(f"\n📈 性能提升:")
        self.logger.info(f"   - 基准准确率: {baseline_accuracy:.3f}")
        self.logger.info(f"   - Boot优化准确率: {best_accuracy:.3f}")
        self.logger.info(f"   - 提升幅度: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        
        target_accuracy = 0.60
        if best_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 训练成功！达到目标准确率 {target_accuracy:.1%}")
        else:
            gap = target_accuracy - best_accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f} ({gap/target_accuracy*100:.1f}%)")
        
        self.logger.info("="*80)

if __name__ == "__main__":
    trainer = BootLevelTrainer()
    trainer.run_boot_training()
