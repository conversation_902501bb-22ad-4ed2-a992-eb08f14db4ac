#!/usr/bin/env python3
"""
检查数据库中所有策略的数据
"""

import sys
import os
import logging
import warnings
import numpy as np
import pandas as pd
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def check_all_strategies():
    """检查所有策略数据"""
    logger = logging.getLogger(__name__)
    
    try:
        import pymysql
        
        connection = pymysql.connect(
            host='**************',
            user='root',
            password='216888',
            database='lushu',
            charset='utf8mb4',
            port=3306,
            connect_timeout=10
        )
        
        # 首先查看表结构
        cursor = connection.cursor()
        cursor.execute("DESCRIBE strategy_results")
        columns = cursor.fetchall()
        
        logger.info("📊 strategy_results 表结构:")
        strategy_columns = []
        for col in columns:
            col_name = col[0]
            col_type = col[1]
            logger.info(f"   {col_name}: {col_type}")
            if col_name.startswith('strategy_'):
                strategy_columns.append(col_name)
        
        logger.info(f"\n🎯 发现的策略列: {strategy_columns}")
        
        # 查询所有策略数据的统计信息
        if strategy_columns:
            strategy_cols_str = ', '.join(strategy_columns)
            sql = f"""
            SELECT 
                {strategy_cols_str},
                true_label as actual_result,
                COUNT(*) as count
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            GROUP BY {strategy_cols_str}, true_label
            ORDER BY count DESC
            LIMIT 20
            """
            
            logger.info("\n📈 策略组合统计 (前20个):")
            cursor.execute(sql)
            results = cursor.fetchall()
            
            for row in results:
                logger.info(f"   {row}")
        
        # 检查每个策略的数据质量
        logger.info(f"\n🔍 各策略数据质量检查:")
        
        for strategy in strategy_columns:
            cursor.execute(f"""
                SELECT 
                    {strategy},
                    COUNT(*) as count,
                    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM strategy_results WHERE true_label IS NOT NULL) as percentage
                FROM strategy_results 
                WHERE true_label IS NOT NULL
                GROUP BY {strategy}
                ORDER BY {strategy}
            """)
            
            strategy_stats = cursor.fetchall()
            logger.info(f"\n   {strategy} 分布:")
            for stat in strategy_stats:
                value, count, percentage = stat
                logger.info(f"     {value}: {count} ({percentage:.1f}%)")
        
        # 检查策略与结果的相关性
        logger.info(f"\n🎯 策略与结果相关性:")
        
        # 加载数据进行相关性分析
        sql = f"""
        SELECT 
            {strategy_cols_str},
            true_label as actual_result
        FROM strategy_results 
        WHERE true_label IS NOT NULL
        LIMIT 10000
        """
        
        df = pd.read_sql(sql, connection)
        
        # 数据清理
        for col in strategy_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        if df['actual_result'].dtype == 'object':
            df['actual_result'] = df['actual_result'].astype(str).str[0]
            df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
        
        df = df.dropna()
        
        # 计算相关性
        correlations = {}
        for strategy in strategy_columns:
            if strategy in df.columns:
                corr = df[strategy].corr(df['actual_result'])
                correlations[strategy] = corr
                logger.info(f"   {strategy}: {corr:.6f}")
        
        # 推荐最佳策略组合
        logger.info(f"\n💡 推荐策略组合:")
        sorted_strategies = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)
        
        logger.info("   按相关性排序:")
        for i, (strategy, corr) in enumerate(sorted_strategies):
            logger.info(f"   {i+1}. {strategy}: {corr:.6f}")
        
        # 推荐前3个最佳策略
        top_3 = [strategy for strategy, _ in sorted_strategies[:3]]
        logger.info(f"\n🏆 推荐使用的3个策略: {top_3}")
        
        # 推荐前5个策略
        top_5 = [strategy for strategy, _ in sorted_strategies[:5]]
        logger.info(f"🏆 推荐使用的5个策略: {top_5}")
        
        # 推荐所有有效策略
        effective_strategies = [strategy for strategy, corr in sorted_strategies if abs(corr) > 0.05]
        logger.info(f"🏆 所有有效策略 (|相关性| > 0.05): {effective_strategies}")
        
        cursor.close()
        connection.close()
        
        return {
            'all_strategies': strategy_columns,
            'correlations': correlations,
            'top_3': top_3,
            'top_5': top_5,
            'effective_strategies': effective_strategies
        }
        
    except Exception as e:
        logger.error(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = check_all_strategies()
    if results:
        print(f"\n🎯 总结:")
        print(f"   发现 {len(results['all_strategies'])} 个策略")
        print(f"   推荐前3个: {results['top_3']}")
        print(f"   推荐前5个: {results['top_5']}")
        print(f"   有效策略: {results['effective_strategies']}")
