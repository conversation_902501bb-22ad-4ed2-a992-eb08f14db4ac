#!/usr/bin/env python3
"""
最终结论分析器
深入分析为什么复杂模型没有用，得出最终结论
"""

import sys
import os
import logging
import warnings
import numpy as np
import pandas as pd
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class FinalConclusionAnalyzer:
    """最终结论分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据进行最终分析...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def analyze_strategy_quality(self, df: pd.DataFrame):
        """分析策略质量"""
        self.logger.info("\n🔍 深度分析策略质量...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. 基础准确率分析
        self.logger.info("   📊 基础准确率分析:")
        accuracies = {}
        for strategy in strategy_cols:
            accuracy = (df[strategy] == df['actual_result']).mean()
            accuracies[strategy] = accuracy
            
            # 计算置信区间
            n = len(df)
            std_error = np.sqrt(accuracy * (1 - accuracy) / n)
            ci_lower = accuracy - 1.96 * std_error
            ci_upper = accuracy + 1.96 * std_error
            
            # 判断是否显著优于随机
            significantly_better = ci_lower > 0.5
            
            self.logger.info(f"     {strategy}: {accuracy:.3f} [{ci_lower:.3f}, {ci_upper:.3f}] {'✅' if significantly_better else '❌'}")
        
        # 2. 信息量分析
        self.logger.info("\n   📈 信息量分析:")
        
        for strategy in strategy_cols:
            # 计算互信息
            from sklearn.metrics import mutual_info_score
            mi = mutual_info_score(df[strategy], df['actual_result'])
            
            # 计算熵
            strategy_entropy = -np.sum([p * np.log2(p) for p in [df[strategy].mean(), 1-df[strategy].mean()] if p > 0])
            result_entropy = -np.sum([p * np.log2(p) for p in [df['actual_result'].mean(), 1-df['actual_result'].mean()] if p > 0])
            
            # 信息增益比
            info_gain_ratio = mi / strategy_entropy if strategy_entropy > 0 else 0
            
            self.logger.info(f"     {strategy}: 互信息={mi:.6f}, 信息增益比={info_gain_ratio:.6f}")
        
        # 3. 随机性检验
        self.logger.info("\n   🎲 随机性检验:")
        
        for strategy in strategy_cols:
            # 卡方检验
            from scipy.stats import chi2_contingency
            
            # 构建列联表
            contingency_table = pd.crosstab(df[strategy], df['actual_result'])
            chi2, p_value, dof, expected = chi2_contingency(contingency_table)
            
            # 判断是否显著非随机
            is_significant = p_value < 0.05
            
            self.logger.info(f"     {strategy}: χ²={chi2:.3f}, p={p_value:.6f} {'✅非随机' if is_significant else '❌可能随机'}")
        
        return accuracies
    
    def analyze_combination_potential(self, df: pd.DataFrame):
        """分析组合潜力"""
        self.logger.info("\n🔗 分析组合潜力...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. 最佳可能组合
        self.logger.info("   🎯 最佳可能组合分析:")
        
        # 对每个样本，找出正确的策略数量
        correct_counts = []
        for _, row in df.iterrows():
            correct_count = sum(1 for strategy in strategy_cols if row[strategy] == row['actual_result'])
            correct_counts.append(correct_count)
        
        correct_counts = np.array(correct_counts)
        
        self.logger.info(f"     平均每个样本有 {correct_counts.mean():.2f} 个策略正确")
        self.logger.info(f"     至少1个策略正确的样本: {(correct_counts >= 1).mean():.3f}")
        self.logger.info(f"     至少2个策略正确的样本: {(correct_counts >= 2).mean():.3f}")
        self.logger.info(f"     至少3个策略正确的样本: {(correct_counts >= 3).mean():.3f}")
        self.logger.info(f"     所有策略都错误的样本: {(correct_counts == 0).mean():.3f}")
        
        # 2. 实际组合效果
        self.logger.info("\n   📊 实际组合效果:")
        
        # 简单多数投票
        df['majority_vote'] = (df[strategy_cols].sum(axis=1) >= 4).astype(int)
        majority_accuracy = (df['majority_vote'] == df['actual_result']).mean()
        
        # 加权投票
        weights = [0.147, -0.002, 0.007, -0.016, 0.014, -0.005, 0.007, 0.038]  # 基于相关性
        weighted_sum = sum(df[strategy] * weight for strategy, weight in zip(strategy_cols, weights))
        df['weighted_vote'] = (weighted_sum > 0).astype(int)
        weighted_accuracy = (df['weighted_vote'] == df['actual_result']).mean()
        
        # 只用最佳策略
        best_strategy_accuracy = (df['strategy_1'] == df['actual_result']).mean()
        
        self.logger.info(f"     多数投票准确率: {majority_accuracy:.3f}")
        self.logger.info(f"     加权投票准确率: {weighted_accuracy:.3f}")
        self.logger.info(f"     最佳单策略准确率: {best_strategy_accuracy:.3f}")
        
        # 3. 理论上限分析
        self.logger.info("\n   🚀 理论上限分析:")
        
        # 完美选择（每个样本选最佳策略）
        perfect_selections = []
        for _, row in df.iterrows():
            best_accuracy = 0
            for strategy in strategy_cols:
                if row[strategy] == row['actual_result']:
                    best_accuracy = 1
                    break
            perfect_selections.append(best_accuracy)
        
        perfect_accuracy = np.mean(perfect_selections)
        
        # 完美多数投票（每个样本选择正确策略的多数）
        perfect_majority_selections = []
        for _, row in df.iterrows():
            correct_strategies = [strategy for strategy in strategy_cols if row[strategy] == row['actual_result']]
            if len(correct_strategies) >= 4:  # 多数正确
                perfect_majority_selections.append(1)
            else:
                perfect_majority_selections.append(0)
        
        perfect_majority_accuracy = np.mean(perfect_majority_selections)
        
        self.logger.info(f"     完美选择上限: {perfect_accuracy:.3f}")
        self.logger.info(f"     完美多数投票上限: {perfect_majority_accuracy:.3f}")
        
        return {
            'majority_accuracy': majority_accuracy,
            'weighted_accuracy': weighted_accuracy,
            'best_strategy_accuracy': best_strategy_accuracy,
            'perfect_accuracy': perfect_accuracy,
            'perfect_majority_accuracy': perfect_majority_accuracy
        }
    
    def final_recommendation(self, accuracies: dict, combination_results: dict):
        """最终建议"""
        self.logger.info("\n💡 最终建议和结论:")
        
        best_strategy = max(accuracies, key=accuracies.get)
        best_accuracy = accuracies[best_strategy]
        
        # 分析是否值得使用复杂模型
        majority_improvement = combination_results['majority_accuracy'] - best_accuracy
        weighted_improvement = combination_results['weighted_accuracy'] - best_accuracy
        perfect_potential = combination_results['perfect_accuracy'] - best_accuracy
        
        self.logger.info(f"\n   📊 性能对比:")
        self.logger.info(f"     最佳单策略 ({best_strategy}): {best_accuracy:.3f}")
        self.logger.info(f"     多数投票提升: {majority_improvement:+.3f}")
        self.logger.info(f"     加权投票提升: {weighted_improvement:+.3f}")
        self.logger.info(f"     理论提升潜力: {perfect_potential:+.3f}")
        
        self.logger.info(f"\n   🎯 结论:")
        
        if abs(majority_improvement) < 0.005 and abs(weighted_improvement) < 0.005:
            self.logger.info("     ❌ 复杂模型没有显著价值")
            self.logger.info("     ✅ 建议直接使用strategy_1")
            self.logger.info("     💡 原因：其他策略质量太差，接近随机")
        elif perfect_potential > 0.05:
            self.logger.info("     ⚠️ 有提升潜力但当前方法无效")
            self.logger.info("     💡 建议改进基础策略算法")
            self.logger.info("     🔧 或探索更好的组合方法")
        else:
            self.logger.info("     ✅ 当前已接近理论上限")
            self.logger.info("     💡 建议专注于改进数据质量")
        
        # 具体建议
        self.logger.info(f"\n   🚀 具体建议:")
        
        if best_accuracy < 0.60:
            self.logger.info("     1. 重新设计基础策略算法")
            self.logger.info("     2. 增加更多有效的数据源")
            self.logger.info("     3. 改进特征工程方法")
            self.logger.info("     4. 考虑使用深度学习方法")
        
        self.logger.info("     5. 当前V8系统建议：")
        if weighted_improvement > 0.002:
            self.logger.info("        - 使用加权投票集成")
        else:
            self.logger.info("        - 直接使用strategy_1，简单有效")
        
        self.logger.info("     6. 长期改进方向：")
        self.logger.info("        - 分析strategy_1成功的原因")
        self.logger.info("        - 将成功经验应用到其他策略")
        self.logger.info("        - 探索新的预测维度")
    
    def run_final_analysis(self):
        """运行最终分析"""
        self.logger.info("🚀 开始最终结论分析")
        self.logger.info("🎯 目标：确定复杂模型是否有价值，给出最终建议")
        self.logger.info("="*80)
        
        # 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，分析终止")
            return
        
        # 分析策略质量
        accuracies = self.analyze_strategy_quality(df)
        
        # 分析组合潜力
        combination_results = self.analyze_combination_potential(df)
        
        # 最终建议
        self.final_recommendation(accuracies, combination_results)
        
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 最终结论")
        self.logger.info("="*80)
        
        # 回答用户的问题
        self.logger.info("\n❓ 回答您的问题:")
        self.logger.info("   Q: 这些模型有什么用？")
        
        best_accuracy = max(accuracies.values())
        if combination_results['weighted_accuracy'] - best_accuracy > 0.005:
            self.logger.info("   A: 有一定价值，但提升有限")
        else:
            self.logger.info("   A: 确实没什么用，直接用strategy_1更好")
        
        self.logger.info("\n   Q: 其他7个基础策略完全没有数据价值吗？")
        significant_strategies = sum(1 for acc in accuracies.values() if acc > 0.52)
        if significant_strategies <= 2:
            self.logger.info("   A: 基本没有价值，大部分接近随机")
        else:
            self.logger.info("   A: 有一定价值，但需要更好的利用方法")
        
        self.logger.info("\n   Q: 还是说我们的特征利用方式不对？")
        if combination_results['perfect_accuracy'] - best_accuracy > 0.1:
            self.logger.info("   A: 特征利用方式确实有问题，有很大改进空间")
        else:
            self.logger.info("   A: 主要是基础策略质量问题，不是特征利用问题")
        
        self.logger.info("="*80)

if __name__ == "__main__":
    analyzer = FinalConclusionAnalyzer()
    analyzer.run_final_analysis()
