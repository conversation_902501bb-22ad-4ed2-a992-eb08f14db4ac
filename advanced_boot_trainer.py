#!/usr/bin/env python3
"""
高级Boot特征训练器
基于Boot级别特征的成功，进一步深化和优化
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class AdvancedBootTrainer:
    """高级Boot特征训练器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data_with_sequence(self) -> pd.DataFrame:
        """加载数据并保持时间序列"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载时间序列数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            # 添加全局序列号
            df['global_sequence'] = range(len(df))
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条时间序列数据")
            self.logger.info(f"   - Boot范围: {df['boot_id'].min()} - {df['boot_id'].max()}")
            self.logger.info(f"   - 总Boot数: {df['boot_id'].nunique()}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def create_advanced_boot_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建高级Boot特征"""
        self.logger.info("\n🔧 创建高级Boot特征...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        df['strategy_sum'] = df[strategy_cols].sum(axis=1)
        
        # 1. 增强的Boot内累积特征
        self.logger.info("   1️⃣ 增强累积特征...")
        
        # 指数加权移动平均 (更重视近期数据)
        for col in ['strategy_1', 'strategy_sum', 'actual_result']:
            df[f'{col}_ewm_boot'] = df.groupby('boot_id')[col].ewm(alpha=0.3, adjust=False).mean().reset_index(0, drop=True)
            df[f'{col}_ewm_global'] = df[col].ewm(alpha=0.1, adjust=False).mean()
        
        # 加权累积平均 (位置权重)
        def weighted_cumavg(group):
            weights = np.arange(1, len(group) + 1)  # 线性增加权重
            return (group.expanding().apply(lambda x: np.average(x, weights=weights[:len(x)])))
        
        for col in ['strategy_1', 'strategy_sum']:
            df[f'{col}_weighted_cumavg'] = df.groupby('boot_id')[col].apply(weighted_cumavg).reset_index(0, drop=True)
        
        # 2. 多层次历史特征
        self.logger.info("   2️⃣ 多层次历史特征...")
        
        # Boot级别历史统计
        boot_history = df.groupby('boot_id').agg({
            'strategy_1': ['mean', 'std', 'min', 'max', 'sum'],
            'strategy_sum': ['mean', 'std', 'min', 'max'],
            'actual_result': ['mean', 'std', 'count']
        }).round(4)
        
        boot_history.columns = ['_'.join(col).strip() for col in boot_history.columns]
        boot_history = boot_history.add_prefix('boot_')
        
        # 历史Boot窗口特征 (扩展窗口)
        for window in [3, 5, 10, 20]:
            # 历史Boot性能
            boot_performance = boot_history['boot_actual_result_mean'].rolling(window=window, min_periods=1).agg(['mean', 'std', 'min', 'max'])
            boot_performance.columns = [f'hist_boot_perf_{window}_{col}' for col in boot_performance.columns]
            boot_history = pd.concat([boot_history, boot_performance], axis=1)
            
            # 历史Boot策略强度
            boot_strategy_strength = boot_history['boot_strategy_1_mean'].rolling(window=window, min_periods=1).agg(['mean', 'std'])
            boot_strategy_strength.columns = [f'hist_boot_s1_{window}_{col}' for col in boot_strategy_strength.columns]
            boot_history = pd.concat([boot_history, boot_strategy_strength], axis=1)
        
        # 合并Boot历史特征
        df = df.merge(boot_history, left_on='boot_id', right_index=True, how='left')
        
        # 3. 高级Boot内位置特征
        self.logger.info("   3️⃣ 高级位置特征...")
        
        df['position_in_boot'] = df.groupby('boot_id').cumcount() + 1
        df['boot_size'] = df.groupby('boot_id')['boot_id'].transform('count')
        df['boot_progress'] = df['position_in_boot'] / df['boot_size']
        
        # 非线性位置特征
        df['boot_progress_squared'] = df['boot_progress'] ** 2
        df['boot_progress_sqrt'] = np.sqrt(df['boot_progress'])
        df['boot_progress_log'] = np.log1p(df['boot_progress'])
        
        # 位置分段特征
        df['boot_phase'] = pd.cut(df['boot_progress'], 
                                 bins=[0, 0.2, 0.4, 0.6, 0.8, 1.0], 
                                 labels=[1, 2, 3, 4, 5],
                                 include_lowest=True).astype(int)
        
        # 4. 动态趋势特征
        self.logger.info("   4️⃣ 动态趋势特征...")
        
        # 多窗口趋势
        for window in [3, 5, 7]:
            # 策略趋势
            df[f'strategy_1_trend_{window}'] = df.groupby('boot_id')['strategy_1'].rolling(window=window, min_periods=1).apply(
                lambda x: (x.iloc[-1] - x.iloc[0]) / len(x) if len(x) > 1 else 0
            ).reset_index(0, drop=True).fillna(0)
            
            # 策略加速度
            df[f'strategy_1_accel_{window}'] = df.groupby('boot_id')[f'strategy_1_trend_{window}'].diff().fillna(0)
        
        # 趋势强度
        df['trend_strength'] = np.abs(df['strategy_1_trend_5'])
        df['trend_consistency'] = df.groupby('boot_id')['strategy_1_trend_3'].rolling(window=5, min_periods=1).std().reset_index(0, drop=True).fillna(0)
        
        # 5. 复合交互特征
        self.logger.info("   5️⃣ 复合交互特征...")
        
        # 位置 × 策略交互
        df['progress_x_strategy'] = df['boot_progress'] * df['strategy_1']
        df['progress_x_ewm_strategy'] = df['boot_progress'] * df['strategy_1_ewm_boot']
        df['phase_x_strategy'] = df['boot_phase'] * df['strategy_1']
        
        # 趋势 × 位置交互
        df['trend_x_progress'] = df['strategy_1_trend_5'] * df['boot_progress']
        df['trend_x_phase'] = df['strategy_1_trend_5'] * df['boot_phase']
        
        # 历史 × 当前交互
        df['current_vs_hist_boot'] = df['strategy_1'] - df['hist_boot_s1_10_mean']
        df['current_vs_boot_avg'] = df['strategy_1'] - df['boot_strategy_1_mean']
        
        # 6. 全局上下文特征
        self.logger.info("   6️⃣ 全局上下文特征...")
        
        # 全局位置
        df['global_progress'] = df['global_sequence'] / len(df)
        
        # 全局历史窗口
        for window in [50, 100, 200]:
            df[f'global_s1_avg_{window}'] = df['strategy_1'].rolling(window=window, min_periods=1).mean()
            df[f'global_result_avg_{window}'] = df['actual_result'].shift(1).rolling(window=window, min_periods=1).mean()
        
        # 全局vs局部对比
        df['boot_vs_global_s1'] = df['strategy_1_ewm_boot'] - df['strategy_1_ewm_global']
        df['boot_vs_global_trend'] = df['strategy_1_trend_5'] - df['strategy_1'].rolling(window=20, min_periods=1).apply(
            lambda x: (x.iloc[-1] - x.iloc[0]) / len(x) if len(x) > 1 else 0
        ).fillna(0)
        
        # 7. 自适应特征
        self.logger.info("   7️⃣ 自适应特征...")
        
        # 基于Boot质量的自适应权重
        df['boot_quality'] = 1 / (1 + df['boot_actual_result_std'].fillna(1))
        df['adaptive_strategy'] = df['strategy_1'] * df['boot_quality']
        df['adaptive_ewm'] = df['strategy_1_ewm_boot'] * df['boot_quality']
        
        # 基于趋势强度的自适应特征
        df['trend_adaptive_strategy'] = df['strategy_1'] * (1 + df['trend_strength'])
        
        # 8. 时间衰减特征
        self.logger.info("   8️⃣ 时间衰减特征...")
        
        # Boot内时间衰减
        df['time_decay_boot'] = np.exp(-0.1 * (df['boot_size'] - df['position_in_boot']))
        df['time_weighted_strategy'] = df['strategy_1'] * df['time_decay_boot']
        
        # 全局时间衰减
        df['time_decay_global'] = np.exp(-0.01 * (len(df) - df['global_sequence']))
        df['global_weighted_strategy'] = df['strategy_1'] * df['time_decay_global']
        
        # 填充和清理
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(0)
        df[numeric_cols] = df[numeric_cols].replace([np.inf, -np.inf], 0)
        
        # 统计特征数量
        original_cols = len(strategy_cols) + 4  # 原始策略 + id, boot_id, actual_result, global_sequence
        new_features = len(df.columns) - original_cols
        self.logger.info(f"   ✅ 创建了 {new_features} 个高级Boot特征")
        
        return df
    
    def train_advanced_models(self, df: pd.DataFrame) -> Dict[str, Any]:
        """训练高级模型"""
        try:
            from sklearn.model_selection import train_test_split, cross_val_score
            from sklearn.linear_model import LogisticRegression
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
            from sklearn.metrics import accuracy_score
            from sklearn.preprocessing import StandardScaler
            import lightgbm as lgb
            
            self.logger.info("\n🤖 开始高级模型训练...")
            
            # 准备特征
            exclude_cols = ['id', 'boot_id', 'actual_result', 'global_sequence'] + [f'strategy_{i}' for i in range(1, 9)]
            feature_cols = [col for col in df.columns if col not in exclude_cols]
            
            X = df[feature_cols].values
            y = df['actual_result'].values
            
            self.logger.info(f"   📊 特征维度: {X.shape}")
            self.logger.info(f"   📊 高级特征数: {len(feature_cols)} 个")
            
            # 特征标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 按Boot分割数据
            unique_boots = df['boot_id'].unique()
            train_boots, test_boots = train_test_split(unique_boots, test_size=0.2, random_state=42)
            
            train_mask = df['boot_id'].isin(train_boots)
            test_mask = df['boot_id'].isin(test_boots)
            
            X_train, X_test = X_scaled[train_mask], X_scaled[test_mask]
            y_train, y_test = y[train_mask], y[test_mask]
            
            self.logger.info(f"   📊 训练Boot数: {len(train_boots)}, 测试Boot数: {len(test_boots)}")
            
            # 创建优化模型
            models = {
                'logistic_optimized': LogisticRegression(
                    random_state=42, max_iter=2000, C=0.01, solver='liblinear'
                ),
                'random_forest_tuned': RandomForestClassifier(
                    n_estimators=300, max_depth=8, min_samples_split=5,
                    min_samples_leaf=2, max_features='sqrt', random_state=42
                ),
                'lightgbm_tuned': lgb.LGBMClassifier(
                    n_estimators=300, max_depth=6, learning_rate=0.05,
                    subsample=0.8, colsample_bytree=0.8, 
                    min_child_samples=20, random_state=42, verbose=-1
                ),
                'gradient_boost_tuned': GradientBoostingClassifier(
                    n_estimators=200, learning_rate=0.05, max_depth=6,
                    min_samples_split=10, min_samples_leaf=5, random_state=42
                )
            }
            
            # 训练和评估
            results = {}
            for name, model in models.items():
                self.logger.info(f"   🔄 训练 {name}...")
                
                start_time = time.time()
                model.fit(X_train, y_train)
                train_time = time.time() - start_time
                
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'train_time': train_time
                }
                
                self.logger.info(f"      ✅ {name}: 准确率={accuracy:.3f}, CV={cv_scores.mean():.3f}±{cv_scores.std():.3f}")
            
            # 创建多层集成
            self.logger.info("   🔗 创建高级集成...")
            
            # 选择最佳模型
            top_models = sorted(results.items(), key=lambda x: x[1]['cv_mean'], reverse=True)[:3]
            
            # 加权集成
            ensemble = VotingClassifier(
                estimators=[(name, result['model']) for name, result in top_models],
                voting='soft'
            )
            
            ensemble.fit(X_train, y_train)
            ensemble_pred = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
            
            self.logger.info(f"   🏆 高级集成: 准确率={ensemble_accuracy:.3f}")
            
            # 特征重要性分析
            if 'lightgbm_tuned' in results:
                lgb_model = results['lightgbm_tuned']['model']
                if hasattr(lgb_model, 'feature_importances_'):
                    importances = lgb_model.feature_importances_
                    feature_importance = dict(zip(feature_cols, importances))
                    top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:15]
                    
                    self.logger.info(f"\n   🎯 Top 15 高级特征:")
                    for i, (feature, importance) in enumerate(top_features):
                        self.logger.info(f"      {i+1:2d}. {feature}: {importance:.1f}")
            
            return {
                'individual_models': results,
                'ensemble': ensemble,
                'ensemble_accuracy': ensemble_accuracy,
                'feature_importance': top_features if 'top_features' in locals() else [],
                'feature_names': feature_cols,
                'scaler': scaler,
                'top_models': [name for name, _ in top_models]
            }
            
        except Exception as e:
            self.logger.error(f"❌ 高级模型训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def run_advanced_training(self):
        """运行高级训练"""
        self.logger.info("🚀 开始高级Boot特征训练")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载时间序列数据
        df = self.load_data_with_sequence()
        if df.empty:
            self.logger.error("❌ 无法加载数据，训练终止")
            return
        
        # 2. 创建高级Boot特征
        df_advanced = self.create_advanced_boot_features(df)
        
        # 3. 训练高级模型
        results = self.train_advanced_models(df_advanced)
        
        if not results:
            self.logger.error("❌ 模型训练失败")
            return
        
        # 4. 生成报告
        total_time = time.time() - start_time
        self.generate_report(results, total_time, len(df))
    
    def generate_report(self, results: Dict[str, Any], total_time: float, total_samples: int):
        """生成训练报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 高级Boot特征训练报告")
        self.logger.info("="*80)
        
        self.logger.info(f"\n📊 训练统计:")
        self.logger.info(f"   - 总样本数: {total_samples:,}")
        self.logger.info(f"   - 训练时间: {total_time:.1f}秒")
        self.logger.info(f"   - 高级特征数: {len(results.get('feature_names', []))}")
        
        if 'individual_models' in results:
            self.logger.info(f"\n🤖 模型结果:")
            for name, model_info in results['individual_models'].items():
                self.logger.info(f"   - {name:20}: 准确率={model_info['accuracy']:.3f}, CV={model_info['cv_mean']:.3f}±{model_info['cv_std']:.3f}")
        
        self.logger.info(f"\n🏆 高级集成结果: {results.get('ensemble_accuracy', 0):.3f}")
        self.logger.info(f"🏆 最佳模型组合: {results.get('top_models', [])}")
        
        if results.get('feature_importance'):
            self.logger.info(f"\n🎯 Top 10 高级特征:")
            for i, (feature, importance) in enumerate(results['feature_importance'][:10]):
                self.logger.info(f"   {i+1:2d}. {feature}: {importance:.1f}")
        
        # 性能分析
        baseline_accuracy = 0.568
        boot_accuracy = 0.572  # 之前Boot级别的最佳结果
        current_accuracy = results.get('ensemble_accuracy', 0)
        
        improvement_from_baseline = current_accuracy - baseline_accuracy
        improvement_from_boot = current_accuracy - boot_accuracy
        
        self.logger.info(f"\n📈 性能提升分析:")
        self.logger.info(f"   - 原始基准: {baseline_accuracy:.3f}")
        self.logger.info(f"   - Boot基准: {boot_accuracy:.3f}")
        self.logger.info(f"   - 当前结果: {current_accuracy:.3f}")
        self.logger.info(f"   - 总提升: {improvement_from_baseline:.3f} ({improvement_from_baseline/baseline_accuracy*100:+.1f}%)")
        self.logger.info(f"   - Boot提升: {improvement_from_boot:.3f} ({improvement_from_boot/boot_accuracy*100:+.1f}%)")
        
        target_accuracy = 0.60
        if current_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 训练成功！达到目标准确率 {target_accuracy:.1%}")
        else:
            gap = target_accuracy - current_accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f} ({gap/target_accuracy*100:.1f}%)")
            
            if improvement_from_boot > 0:
                self.logger.info("✅ 高级特征工程有效果！继续优化...")
            else:
                self.logger.info("⚠️ 需要探索新的特征工程方向")
        
        self.logger.info("="*80)

if __name__ == "__main__":
    trainer = AdvancedBootTrainer()
    trainer.run_advanced_training()
