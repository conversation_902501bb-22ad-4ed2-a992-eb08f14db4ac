#!/usr/bin/env python3
"""
V8系统诊断脚本
用于识别和修复系统集成问题
"""

import sys
import os
import logging
import traceback
import warnings
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class SystemDiagnostic:
    """系统诊断器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.issues = []
        self.fixes_applied = []
    
    def run_full_diagnostic(self):
        """运行完整诊断"""
        print("🔍 开始V8系统诊断...")
        print("="*60)
        
        # 1. 基础导入测试
        self.test_imports()
        
        # 2. 数据库连接测试
        self.test_database_connection()
        
        # 3. 基础策略测试
        self.test_base_strategies()
        
        # 4. 特征工程测试
        self.test_feature_engineering()
        
        # 5. ML模型测试
        self.test_ml_models()
        
        # 6. 简单集成测试
        self.test_simple_integration()
        
        # 7. 生成诊断报告
        self.generate_report()
    
    def test_imports(self):
        """测试基础导入"""
        print("\n📦 测试基础导入...")
        
        try:
            from core.base_strategies import BaseStrategyLayer
            print("  ✅ BaseStrategyLayer 导入成功")
        except Exception as e:
            self.issues.append(f"BaseStrategyLayer 导入失败: {e}")
            print(f"  ❌ BaseStrategyLayer 导入失败: {e}")

        try:
            from core.feature_engineering import FeatureEngineeringLayer
            print("  ✅ FeatureEngineeringLayer 导入成功")
        except Exception as e:
            self.issues.append(f"FeatureEngineeringLayer 导入失败: {e}")
            print(f"  ❌ FeatureEngineeringLayer 导入失败: {e}")

        try:
            from core.ml_models import MLModelLayer
            print("  ✅ MLModelLayer 导入成功")
        except Exception as e:
            self.issues.append(f"MLModelLayer 导入失败: {e}")
            print(f"  ❌ MLModelLayer 导入失败: {e}")
        
        try:
            from main import SimpleFusionV8
            print("  ✅ SimpleFusionV8 导入成功")
        except Exception as e:
            self.issues.append(f"SimpleFusionV8 导入失败: {e}")
            print(f"  ❌ SimpleFusionV8 导入失败: {e}")
    
    def test_database_connection(self):
        """测试数据库连接"""
        print("\n🗄️ 测试数据库连接...")
        
        try:
            from core.base_strategies import BaseStrategyLayer
            strategies = BaseStrategyLayer()

            # 测试数据库连接
            test_data = {
                'boot_id': 1,
                'hand_id': 1,
                'player_cards': [1, 2],
                'banker_cards': [3, 4],
                'result': 'P'
            }

            outputs = strategies.get_strategy_outputs(test_data)
            print(f"  ✅ 数据库连接成功，获得 {len(outputs)} 个策略输出")
            
        except Exception as e:
            self.issues.append(f"数据库连接失败: {e}")
            print(f"  ❌ 数据库连接失败: {e}")
    
    def test_base_strategies(self):
        """测试基础策略"""
        print("\n🎯 测试基础策略...")
        
        try:
            from core.base_strategies import BaseStrategyLayer
            strategies = BaseStrategyLayer()

            # 创建测试数据
            test_data = {
                'boot_id': 1,
                'hand_id': 1,
                'player_cards': [1, 2],
                'banker_cards': [3, 4],
                'result': 'P'
            }

            outputs = strategies.get_strategy_outputs(test_data)
            
            if outputs and len(outputs) > 0:
                print(f"  ✅ 基础策略测试成功，获得 {len(outputs)} 个输出")
                for strategy, value in outputs.items():
                    print(f"    - {strategy}: {value}")
            else:
                self.issues.append("基础策略返回空结果")
                print("  ❌ 基础策略返回空结果")
                
        except Exception as e:
            self.issues.append(f"基础策略测试失败: {e}")
            print(f"  ❌ 基础策略测试失败: {e}")
            traceback.print_exc()
    
    def test_feature_engineering(self):
        """测试特征工程"""
        print("\n🔧 测试特征工程...")
        
        try:
            from core.feature_engineering import FeatureEngineeringLayer
            fe = FeatureEngineeringLayer()

            # 创建测试策略输出
            strategy_outputs = {
                'strategy_1': 1,
                'strategy_2': 0,
                'strategy_6': 1
            }

            features = fe.generate_features(strategy_outputs)
            
            if features and len(features) > 0:
                print(f"  ✅ 特征工程测试成功，生成 {len(features)} 个特征")
                # 显示前几个特征
                for i, (key, value) in enumerate(features.items()):
                    if i < 5:  # 只显示前5个
                        print(f"    - {key}: {value}")
                if len(features) > 5:
                    print(f"    ... 还有 {len(features) - 5} 个特征")
            else:
                self.issues.append("特征工程返回空结果")
                print("  ❌ 特征工程返回空结果")
                
        except Exception as e:
            self.issues.append(f"特征工程测试失败: {e}")
            print(f"  ❌ 特征工程测试失败: {e}")
            traceback.print_exc()
    
    def test_ml_models(self):
        """测试ML模型"""
        print("\n🤖 测试ML模型...")
        
        try:
            from core.ml_models import MLModelLayer
            ml_models = MLModelLayer()

            # 创建测试特征
            test_features = {
                'consensus_ratio': 0.6,
                'weighted_consensus': 0.7,
                'avg_confidence': 0.8,
                'strategy_1_weight': 0.5,
                'strategy_2_weight': 0.3,
                'strategy_6_weight': 0.2
            }

            predictions = ml_models.predict(test_features)
            
            if predictions and len(predictions) > 0:
                print(f"  ✅ ML模型测试成功，获得 {len(predictions)} 个预测")
                for model_name, prediction in predictions.items():
                    if hasattr(prediction, 'prediction'):
                        print(f"    - {model_name}: {prediction.prediction:.3f} (置信度: {prediction.confidence:.3f})")
                    else:
                        print(f"    - {model_name}: {prediction}")
            else:
                self.issues.append("ML模型返回空结果")
                print("  ❌ ML模型返回空结果")
                
        except Exception as e:
            self.issues.append(f"ML模型测试失败: {e}")
            print(f"  ❌ ML模型测试失败: {e}")
            traceback.print_exc()
    
    def test_simple_integration(self):
        """测试简单集成"""
        print("\n🔗 测试简单集成...")
        
        try:
            from main import SimpleFusionV8
            system = SimpleFusionV8()
            system.initialize()
            
            # 创建测试外部数据
            external_data = {
                'decision_id': 'test_decision_001',
                'boot_id': 1,
                'hand_id': 1,
                'player_cards': [1, 2],
                'banker_cards': [3, 4],
                'result': 'P'
            }
            
            decision = system.process_decision(external_data)
            
            if decision:
                print(f"  ✅ 简单集成测试成功")
                print(f"    - 预测: {decision.prediction:.3f}")
                print(f"    - 置信度: {decision.confidence:.3f}")
                print(f"    - 风险级别: {decision.risk_level}")
                print(f"    - 是否执行: {decision.should_act}")
            else:
                self.issues.append("集成测试返回空决策")
                print("  ❌ 集成测试返回空决策")
                
        except Exception as e:
            self.issues.append(f"集成测试失败: {e}")
            print(f"  ❌ 集成测试失败: {e}")
            traceback.print_exc()
    
    def generate_report(self):
        """生成诊断报告"""
        print("\n📋 诊断报告")
        print("="*60)
        
        if not self.issues:
            print("🎉 恭喜！系统诊断未发现问题")
            print("✅ 所有组件都能正常工作")
        else:
            print(f"⚠️ 发现 {len(self.issues)} 个问题:")
            for i, issue in enumerate(self.issues, 1):
                print(f"  {i}. {issue}")
        
        if self.fixes_applied:
            print(f"\n🔧 已应用 {len(self.fixes_applied)} 个修复:")
            for fix in self.fixes_applied:
                print(f"  - {fix}")
        
        print("\n📊 诊断完成")

if __name__ == "__main__":
    diagnostic = SystemDiagnostic()
    diagnostic.run_full_diagnostic()
