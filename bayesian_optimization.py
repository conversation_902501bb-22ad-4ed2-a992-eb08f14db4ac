#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V8系统贝叶斯超参数优化

使用贝叶斯优化自动调优机器学习模型，目标准确率60%以上
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import pandas as pd
import logging
import time
import pickle
from typing import Dict, List, Any, Tuple, Optional
from pathlib import Path

try:
    import optuna
    from optuna.samplers import TPESampler
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False

try:
    from skopt import gp_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False

try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

from sklearn.model_selection import cross_val_score, TimeSeriesSplit
from sklearn.metrics import accuracy_score, classification_report
import xgboost as xgb
from sklearn.ensemble import VotingClassifier, RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.svm import SVC

from main import SimpleFusionV8

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class BayesianOptimizer:
    """贝叶斯超参数优化器"""
    
    def __init__(self, target_accuracy: float = 0.60):
        self.logger = logging.getLogger(__name__)
        self.target_accuracy = target_accuracy
        self.db_config = self.load_database_config()
        
        # 优化历史
        self.optimization_history = []
        self.best_params = {}
        self.best_scores = {}
        
        # 数据缓存
        self.X_train = None
        self.y_train = None
        self.X_val = None
        self.y_val = None
        
        self.logger.info(f"贝叶斯优化器初始化完成，目标准确率: {target_accuracy:.1%}")
    
    def load_database_config(self) -> Dict[str, Any]:
        """加载数据库配置"""
        return {
            'host': '**************',
            'user': 'root',
            'password': '216888',
            'database': 'lushu',
            'charset': 'utf8mb4',
            'table': 'strategy_results'
        }
    
    def load_training_data(self, max_records: int = 10000) -> Tuple[np.ndarray, np.ndarray]:
        """加载训练数据"""
        self.logger.info(f"加载训练数据，最大记录数: {max_records:,}")
        
        try:
            conn = pymysql.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset=self.db_config.get('charset', 'utf8mb4')
            )
            
            table = self.db_config['table']
            query = f"""
            SELECT 
                strategy_1,
                strategy_2, 
                strategy_6,
                true_label as actual_result,
                boot_id
            FROM {table}
            WHERE strategy_1 IS NOT NULL 
                AND strategy_2 IS NOT NULL 
                AND strategy_6 IS NOT NULL
                AND true_label IS NOT NULL
            ORDER BY boot_id, id
            LIMIT {max_records}
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条训练数据")
            
            # 生成特征
            X, y = self.generate_features(df)
            
            return X, y
            
        except Exception as e:
            self.logger.error(f"❌ 数据加载失败: {str(e)}")
            return np.array([]), np.array([])
    
    def generate_features(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """生成特征"""
        self.logger.info("生成机器学习特征...")
        
        # 使用V8系统生成特征
        system = SimpleFusionV8()
        system.initialize()
        
        features_list = []
        labels = []
        
        for idx, row in df.iterrows():
            try:
                # 准备外部数据
                external_data = {
                    'decision_id': f"feature_gen_{row['id'] if 'id' in row else idx}",
                    'boot_id': row.get('boot_id', 1),
                    'market_data': {
                        'historical_strategies': {
                            'strategy_1': int(row['strategy_1']),
                            'strategy_2': int(row['strategy_2']),
                            'strategy_6': int(row['strategy_6'])
                        }
                    }
                }
                
                # 生成特征
                decision = system.process_decision(external_data)
                
                if hasattr(decision, 'features') and decision.features:
                    # 转换特征为数值数组
                    feature_vector = []
                    for feature_name in sorted(decision.features.keys()):
                        value = decision.features[feature_name]
                        if isinstance(value, (int, float, np.number)):
                            feature_vector.append(float(value))
                        else:
                            feature_vector.append(0.0)
                    
                    features_list.append(feature_vector)
                    labels.append(int(row['actual_result']))
                
            except Exception as e:
                self.logger.warning(f"特征生成失败 (行 {idx}): {str(e)}")
                continue
        
        X = np.array(features_list)
        y = np.array(labels)
        
        self.logger.info(f"✅ 生成特征完成: {X.shape[0]} 样本, {X.shape[1]} 特征")
        
        return X, y
    
    def prepare_data_splits(self, X: np.ndarray, y: np.ndarray, test_size: float = 0.2):
        """准备数据分割"""
        from sklearn.model_selection import train_test_split
        
        # 分割训练和验证集
        self.X_train, self.X_val, self.y_train, self.y_val = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=y
        )
        
        self.logger.info(f"数据分割完成:")
        self.logger.info(f"  - 训练集: {self.X_train.shape[0]} 样本")
        self.logger.info(f"  - 验证集: {self.X_val.shape[0]} 样本")
        self.logger.info(f"  - 特征数: {self.X_train.shape[1]}")
    
    def optimize_xgboost(self, n_trials: int = 100) -> Dict[str, Any]:
        """XGBoost贝叶斯优化"""
        self.logger.info(f"🚀 开始XGBoost贝叶斯优化 ({n_trials} 次试验)")
        
        if not OPTUNA_AVAILABLE:
            self.logger.error("❌ Optuna库未安装，无法进行贝叶斯优化")
            return {}
        
        def objective(trial):
            # 定义超参数搜索空间
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 1000),
                'max_depth': trial.suggest_int('max_depth', 3, 15),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
                'gamma': trial.suggest_float('gamma', 0, 5),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),
                'random_state': 42
            }
            
            # 训练模型
            model = xgb.XGBClassifier(**params)
            model.fit(self.X_train, self.y_train)
            
            # 验证性能
            y_pred = model.predict(self.X_val)
            accuracy = accuracy_score(self.y_val, y_pred)
            
            return accuracy
        
        # 创建优化研究
        study = optuna.create_study(
            direction='maximize',
            sampler=TPESampler(seed=42)
        )
        
        # 执行优化
        study.optimize(objective, n_trials=n_trials)
        
        # 记录最佳结果
        best_params = study.best_params
        best_score = study.best_value
        
        self.best_params['xgboost'] = best_params
        self.best_scores['xgboost'] = best_score
        
        self.logger.info(f"✅ XGBoost优化完成:")
        self.logger.info(f"   - 最佳准确率: {best_score:.3f}")
        self.logger.info(f"   - 最佳参数: {best_params}")
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'study': study
        }
    
    def optimize_neural_network(self, n_trials: int = 50) -> Dict[str, Any]:
        """神经网络贝叶斯优化"""
        self.logger.info(f"🧠 开始神经网络贝叶斯优化 ({n_trials} 次试验)")
        
        if not OPTUNA_AVAILABLE:
            self.logger.error("❌ Optuna库未安装，无法进行贝叶斯优化")
            return {}
        
        def objective(trial):
            # 定义超参数搜索空间
            hidden_layer_sizes = []
            n_layers = trial.suggest_int('n_layers', 1, 3)
            
            for i in range(n_layers):
                layer_size = trial.suggest_int(f'layer_{i}_size', 50, 500)
                hidden_layer_sizes.append(layer_size)
            
            params = {
                'hidden_layer_sizes': tuple(hidden_layer_sizes),
                'activation': trial.suggest_categorical('activation', ['relu', 'tanh', 'logistic']),
                'solver': trial.suggest_categorical('solver', ['adam', 'lbfgs']),
                'alpha': trial.suggest_float('alpha', 1e-6, 1e-2, log=True),
                'learning_rate': trial.suggest_categorical('learning_rate', ['constant', 'adaptive']),
                'learning_rate_init': trial.suggest_float('learning_rate_init', 1e-4, 1e-1, log=True),
                'max_iter': 1000,
                'random_state': 42
            }
            
            # 训练模型
            model = MLPClassifier(**params)
            model.fit(self.X_train, self.y_train)
            
            # 验证性能
            y_pred = model.predict(self.X_val)
            accuracy = accuracy_score(self.y_val, y_pred)
            
            return accuracy
        
        # 创建优化研究
        study = optuna.create_study(
            direction='maximize',
            sampler=TPESampler(seed=42)
        )
        
        # 执行优化
        study.optimize(objective, n_trials=n_trials)
        
        # 记录最佳结果
        best_params = study.best_params
        best_score = study.best_value
        
        self.best_params['neural_network'] = best_params
        self.best_scores['neural_network'] = best_score
        
        self.logger.info(f"✅ 神经网络优化完成:")
        self.logger.info(f"   - 最佳准确率: {best_score:.3f}")
        self.logger.info(f"   - 最佳参数: {best_params}")
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'study': study
        }

    def build_optimized_ensemble(self) -> Dict[str, Any]:
        """构建优化后的集成模型"""
        self.logger.info("🔧 构建优化后的集成模型...")

        models = {}

        # 1. 构建优化后的XGBoost
        if 'xgboost' in self.best_params:
            xgb_model = xgb.XGBClassifier(**self.best_params['xgboost'])
            xgb_model.fit(self.X_train, self.y_train)
            models['xgboost'] = xgb_model
            self.logger.info(f"   ✅ XGBoost模型已训练")

        # 2. 构建优化后的神经网络
        if 'neural_network' in self.best_params:
            nn_model = MLPClassifier(**self.best_params['neural_network'])
            nn_model.fit(self.X_train, self.y_train)
            models['neural_network'] = nn_model
            self.logger.info(f"   ✅ 神经网络模型已训练")

        # 3. 添加随机森林作为基础模型
        rf_model = RandomForestClassifier(
            n_estimators=200,
            max_depth=10,
            random_state=42
        )
        rf_model.fit(self.X_train, self.y_train)
        models['random_forest'] = rf_model
        self.logger.info(f"   ✅ 随机森林模型已训练")

        # 4. 构建投票集成模型
        voting_models = [(name, model) for name, model in models.items()]
        voting_model = VotingClassifier(
            estimators=voting_models,
            voting='soft'
        )
        voting_model.fit(self.X_train, self.y_train)
        models['voting'] = voting_model
        self.logger.info(f"   ✅ 投票集成模型已训练")

        return models
