#!/usr/bin/env python3
"""
V8系统完整集成测试
测试所有组件的协同工作能力
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class V8IntegrationTest:
    """V8系统集成测试器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.system = None
        self.test_results = {}
        
    def load_test_data(self, limit: int = 100) -> pd.DataFrame:
        """加载测试数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1,
                strategy_2,
                strategy_6,
                true_label as actual_result
            FROM strategy_results 
            WHERE strategy_1 IS NOT NULL 
                AND strategy_2 IS NOT NULL 
                AND strategy_6 IS NOT NULL
                AND true_label IS NOT NULL
            ORDER BY boot_id, id
            LIMIT %s
            """
            
            df = pd.read_sql(sql, connection, params=(limit,))
            connection.close()
            
            # 数据类型转换
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0].astype(int)
            
            self.logger.info(f"✅ 加载了 {len(df)} 条测试数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载测试数据失败: {e}")
            return pd.DataFrame()
    
    def test_system_initialization(self) -> bool:
        """测试系统初始化"""
        self.logger.info("🔧 测试系统初始化...")
        
        try:
            from main import SimpleFusionV8
            self.system = SimpleFusionV8()
            self.system.initialize()
            
            # 检查系统状态
            status = self.system.get_system_status()
            
            if status.get('is_initialized', False):
                self.logger.info("   ✅ 系统初始化成功")
                self.test_results['initialization'] = True
                return True
            else:
                self.logger.error("   ❌ 系统初始化失败")
                self.test_results['initialization'] = False
                return False
                
        except Exception as e:
            self.logger.error(f"   ❌ 系统初始化异常: {e}")
            self.test_results['initialization'] = False
            return False
    
    def test_decision_processing(self, test_df: pd.DataFrame) -> bool:
        """测试决策处理流程"""
        self.logger.info("🎯 测试决策处理流程...")
        
        if self.system is None:
            self.logger.error("   ❌ 系统未初始化")
            return False
        
        try:
            successful_decisions = 0
            total_decisions = min(10, len(test_df))  # 测试前10条
            
            for idx, row in test_df.head(total_decisions).iterrows():
                try:
                    # 创建外部数据
                    external_data = {
                        'decision_id': f"test_decision_{idx}",
                        'boot_id': int(row['boot_id']),
                        'market_data': {
                            'historical_strategies': {
                                'strategy_1': int(row['strategy_1']),
                                'strategy_2': int(row['strategy_2']),
                                'strategy_6': int(row['strategy_6'])
                            }
                        }
                    }
                    
                    # 处理决策
                    decision = self.system.process_decision(external_data)
                    
                    if decision and hasattr(decision, 'prediction'):
                        successful_decisions += 1
                        
                        # 提供反馈
                        actual_result = int(row['actual_result'])
                        self.system.update_feedback(external_data['decision_id'], actual_result)
                        
                    else:
                        self.logger.warning(f"   ⚠️ 决策 {idx} 处理失败")
                        
                except Exception as e:
                    self.logger.warning(f"   ⚠️ 决策 {idx} 处理异常: {e}")
                    continue
            
            success_rate = successful_decisions / total_decisions
            self.logger.info(f"   📊 决策处理成功率: {success_rate:.2%} ({successful_decisions}/{total_decisions})")
            
            if success_rate >= 0.8:  # 80%成功率
                self.logger.info("   ✅ 决策处理测试通过")
                self.test_results['decision_processing'] = True
                return True
            else:
                self.logger.error("   ❌ 决策处理成功率过低")
                self.test_results['decision_processing'] = False
                return False
                
        except Exception as e:
            self.logger.error(f"   ❌ 决策处理测试异常: {e}")
            self.test_results['decision_processing'] = False
            return False
    
    def test_training_integration(self, test_df: pd.DataFrame) -> bool:
        """测试训练集成"""
        self.logger.info("🏋️ 测试训练集成...")
        
        if self.system is None:
            self.logger.error("   ❌ 系统未初始化")
            return False
        
        try:
            # 记录训练前的系统状态
            initial_status = self.system.get_system_status()
            initial_decision_count = initial_status.get('decision_count', 0)
            
            # 进行一轮训练
            training_samples = min(50, len(test_df))
            correct_predictions = 0
            
            for idx, row in test_df.head(training_samples).iterrows():
                try:
                    external_data = {
                        'decision_id': f"train_decision_{idx}",
                        'boot_id': int(row['boot_id']),
                        'market_data': {
                            'historical_strategies': {
                                'strategy_1': int(row['strategy_1']),
                                'strategy_2': int(row['strategy_2']),
                                'strategy_6': int(row['strategy_6'])
                            }
                        }
                    }
                    
                    decision = self.system.process_decision(external_data)
                    actual_result = int(row['actual_result'])
                    
                    if decision:
                        # 检查预测准确性
                        predicted = 1 if decision.prediction > 0.5 else 0
                        if predicted == actual_result:
                            correct_predictions += 1
                        
                        # 提供反馈进行训练
                        self.system.update_feedback(external_data['decision_id'], actual_result)
                    
                except Exception as e:
                    self.logger.warning(f"   ⚠️ 训练样本 {idx} 处理失败: {e}")
                    continue
            
            # 检查训练后的系统状态
            final_status = self.system.get_system_status()
            final_decision_count = final_status.get('decision_count', 0)
            
            accuracy = correct_predictions / training_samples if training_samples > 0 else 0
            decision_count_increased = final_decision_count > initial_decision_count
            
            self.logger.info(f"   📊 训练准确率: {accuracy:.3f} ({correct_predictions}/{training_samples})")
            self.logger.info(f"   📈 决策计数变化: {initial_decision_count} → {final_decision_count}")
            
            if accuracy > 0.4 and decision_count_increased:  # 基本准确率和系统状态更新
                self.logger.info("   ✅ 训练集成测试通过")
                self.test_results['training_integration'] = True
                return True
            else:
                self.logger.error("   ❌ 训练集成测试失败")
                self.test_results['training_integration'] = False
                return False
                
        except Exception as e:
            self.logger.error(f"   ❌ 训练集成测试异常: {e}")
            self.test_results['training_integration'] = False
            return False
    
    def test_performance_consistency(self, test_df: pd.DataFrame) -> bool:
        """测试性能一致性"""
        self.logger.info("📈 测试性能一致性...")
        
        if self.system is None:
            self.logger.error("   ❌ 系统未初始化")
            return False
        
        try:
            # 多轮测试
            rounds = 3
            round_accuracies = []
            
            for round_num in range(rounds):
                correct = 0
                total = min(20, len(test_df))
                
                start_idx = round_num * total
                end_idx = start_idx + total
                
                if end_idx > len(test_df):
                    break
                
                for idx, row in test_df.iloc[start_idx:end_idx].iterrows():
                    try:
                        external_data = {
                            'decision_id': f"consistency_test_{round_num}_{idx}",
                            'boot_id': int(row['boot_id']),
                            'market_data': {
                                'historical_strategies': {
                                    'strategy_1': int(row['strategy_1']),
                                    'strategy_2': int(row['strategy_2']),
                                    'strategy_6': int(row['strategy_6'])
                                }
                            }
                        }
                        
                        decision = self.system.process_decision(external_data)
                        
                        if decision:
                            predicted = 1 if decision.prediction > 0.5 else 0
                            actual = int(row['actual_result'])
                            
                            if predicted == actual:
                                correct += 1
                    
                    except Exception as e:
                        continue
                
                accuracy = correct / total if total > 0 else 0
                round_accuracies.append(accuracy)
                self.logger.info(f"   🔄 轮次 {round_num + 1}: 准确率 {accuracy:.3f}")
            
            if len(round_accuracies) > 0:
                avg_accuracy = np.mean(round_accuracies)
                std_accuracy = np.std(round_accuracies)
                
                self.logger.info(f"   📊 平均准确率: {avg_accuracy:.3f} ± {std_accuracy:.3f}")
                
                # 检查一致性：平均准确率 > 0.5 且标准差 < 0.1
                if avg_accuracy > 0.5 and std_accuracy < 0.1:
                    self.logger.info("   ✅ 性能一致性测试通过")
                    self.test_results['performance_consistency'] = True
                    return True
                else:
                    self.logger.error("   ❌ 性能一致性测试失败")
                    self.test_results['performance_consistency'] = False
                    return False
            else:
                self.logger.error("   ❌ 无法进行一致性测试")
                self.test_results['performance_consistency'] = False
                return False
                
        except Exception as e:
            self.logger.error(f"   ❌ 性能一致性测试异常: {e}")
            self.test_results['performance_consistency'] = False
            return False
    
    def run_full_integration_test(self):
        """运行完整集成测试"""
        self.logger.info("🚀 开始V8系统完整集成测试")
        self.logger.info("="*60)
        
        # 加载测试数据
        test_df = self.load_test_data(limit=200)
        if test_df.empty:
            self.logger.error("❌ 无法加载测试数据，测试终止")
            return
        
        # 运行各项测试
        tests = [
            ('系统初始化', self.test_system_initialization),
            ('决策处理', lambda: self.test_decision_processing(test_df)),
            ('训练集成', lambda: self.test_training_integration(test_df)),
            ('性能一致性', lambda: self.test_performance_consistency(test_df))
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            self.logger.info(f"\n🧪 执行测试: {test_name}")
            try:
                if test_func():
                    passed_tests += 1
                    self.logger.info(f"   ✅ {test_name} 测试通过")
                else:
                    self.logger.error(f"   ❌ {test_name} 测试失败")
            except Exception as e:
                self.logger.error(f"   ❌ {test_name} 测试异常: {e}")
        
        # 生成测试报告
        self.generate_test_report(passed_tests, total_tests)
    
    def generate_test_report(self, passed_tests: int, total_tests: int):
        """生成测试报告"""
        self.logger.info("\n" + "="*60)
        self.logger.info("🎯 V8系统集成测试报告")
        self.logger.info("="*60)
        
        success_rate = passed_tests / total_tests
        
        self.logger.info(f"📊 测试结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1%})")
        
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            self.logger.info(f"   - {test_name}: {status}")
        
        if success_rate >= 0.75:  # 75%通过率
            self.logger.info("\n🎉 集成测试总体通过！系统可以进入下一阶段")
        else:
            self.logger.error("\n⚠️ 集成测试未完全通过，需要进一步修复")
        
        self.logger.info("="*60)

if __name__ == "__main__":
    tester = V8IntegrationTest()
    tester.run_full_integration_test()
