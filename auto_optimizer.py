#!/usr/bin/env python3
"""
V8系统自动化优化器
自动化特征工程和模型优化，目标达到60%以上准确率，严格避免数据泄露
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
import json
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class DataLeakageDetector:
    """数据泄露检测器"""
    
    @staticmethod
    def detect_leakage(df: pd.DataFrame, feature_cols: List[str]) -> List[str]:
        """检测数据泄露特征"""
        leakage_features = []
        
        # 检查特征名中是否包含actual_result
        for col in feature_cols:
            if 'actual_result' in col.lower():
                leakage_features.append(col)
        
        # 检查是否有与actual_result过高相关性的特征
        if 'actual_result' in df.columns:
            for col in feature_cols:
                if col in df.columns:
                    corr = abs(df[col].corr(df['actual_result']))
                    if corr > 0.95:  # 过高相关性可能表示泄露
                        leakage_features.append(f"{col} (high_corr: {corr:.3f})")
        
        return leakage_features

class AutoOptimizer:
    """自动化优化器"""
    
    def __init__(self, target_accuracy: float = 0.60):
        self.logger = logging.getLogger(__name__)
        self.target_accuracy = target_accuracy
        self.current_best_accuracy = 0.0
        self.optimization_history = []
        self.detector = DataLeakageDetector()
        
        # 创建结果目录
        self.results_dir = Path("optimization_results")
        self.results_dir.mkdir(exist_ok=True)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            # 添加全局序列号
            df['global_sequence'] = range(len(df))
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def create_base_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建基础特征"""
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        df['strategy_sum'] = df[strategy_cols].sum(axis=1)
        
        # 基础统计特征
        df['strategy_mean'] = df[strategy_cols].mean(axis=1)
        df['strategy_std'] = df[strategy_cols].std(axis=1).fillna(0)
        df['strategy_min'] = df[strategy_cols].min(axis=1)
        df['strategy_max'] = df[strategy_cols].max(axis=1)
        df['strategy_range'] = df['strategy_max'] - df['strategy_min']
        df['strategy_median'] = df[strategy_cols].median(axis=1)
        
        # 投票特征
        df['majority_vote'] = (df['strategy_sum'] >= 4).astype(int)
        df['strong_majority'] = (df['strategy_sum'] >= 6).astype(int)
        df['unanimous_vote'] = (df['strategy_sum'] == 8).astype(int)
        df['consensus_strength'] = np.abs(df['strategy_sum'] - 4) / 4
        
        # Boot内位置特征
        df['position_in_boot'] = df.groupby('boot_id').cumcount() + 1
        df['boot_size'] = df.groupby('boot_id')['boot_id'].transform('count')
        df['boot_progress'] = df['position_in_boot'] / df['boot_size']
        
        return df
    
    def create_time_series_features(self, df: pd.DataFrame, config: Dict[str, Any]) -> pd.DataFrame:
        """创建时间序列特征"""
        self.logger.info(f"   🕒 创建时间序列特征 (配置: {config})")
        
        # 指数加权移动平均
        alpha_boot = config.get('alpha_boot', 0.3)
        alpha_global = config.get('alpha_global', 0.1)
        
        for col in ['strategy_1', 'strategy_sum']:
            df[f'{col}_ewm_boot'] = df.groupby('boot_id')[col].ewm(alpha=alpha_boot, adjust=False).mean().reset_index(0, drop=True)
            df[f'{col}_ewm_global'] = df[col].ewm(alpha=alpha_global, adjust=False).mean()
        
        # 滑动窗口特征
        windows = config.get('windows', [3, 5, 10])
        for window in windows:
            for col in ['strategy_1', 'strategy_sum']:
                df[f'{col}_ma_{window}'] = df[col].rolling(window=window, min_periods=1).mean()
                df[f'{col}_std_{window}'] = df[col].rolling(window=window, min_periods=1).std().fillna(0)
        
        # 趋势特征
        trend_windows = config.get('trend_windows', [3, 5])
        for window in trend_windows:
            for col in ['strategy_1', 'strategy_sum']:
                df[f'{col}_trend_{window}'] = df.groupby('boot_id')[col].rolling(window=window, min_periods=1).apply(
                    lambda x: (x.iloc[-1] - x.iloc[0]) / len(x) if len(x) > 1 else 0
                ).reset_index(0, drop=True).fillna(0)
        
        return df
    
    def create_interaction_features(self, df: pd.DataFrame, config: Dict[str, Any]) -> pd.DataFrame:
        """创建交互特征"""
        self.logger.info(f"   🔗 创建交互特征 (配置: {config})")
        
        # 位置交互
        if config.get('position_interactions', True):
            df['progress_x_strategy_1'] = df['boot_progress'] * df['strategy_1']
            df['progress_x_strategy_sum'] = df['boot_progress'] * df['strategy_sum']
            
            if 'strategy_1_ewm_boot' in df.columns:
                df['progress_x_ewm_strategy'] = df['boot_progress'] * df['strategy_1_ewm_boot']
        
        # 策略组合
        if config.get('strategy_combinations', True):
            # 基于相关性的分组
            high_corr = ['strategy_1', 'strategy_8']
            mid_corr = ['strategy_3', 'strategy_5', 'strategy_7']
            low_corr = ['strategy_2', 'strategy_4', 'strategy_6']
            
            df['high_corr_sum'] = df[high_corr].sum(axis=1)
            df['mid_corr_sum'] = df[mid_corr].sum(axis=1)
            df['low_corr_sum'] = df[low_corr].sum(axis=1)
            
            df['high_vs_low'] = df['high_corr_sum'] - df['low_corr_sum']
            df['high_vs_mid'] = df['high_corr_sum'] - df['mid_corr_sum']
        
        # 非线性变换
        if config.get('nonlinear_transforms', True):
            df['strategy_1_squared'] = df['strategy_1'] ** 2
            df['strategy_sum_squared'] = df['strategy_sum'] ** 2
            df['strategy_1_sqrt'] = np.sqrt(df['strategy_1'])
            df['boot_progress_squared'] = df['boot_progress'] ** 2
        
        return df
    
    def create_statistical_features(self, df: pd.DataFrame, config: Dict[str, Any]) -> pd.DataFrame:
        """创建统计特征"""
        self.logger.info(f"   📊 创建统计特征 (配置: {config})")
        
        # Boot级别统计
        if config.get('boot_statistics', True):
            boot_stats = df.groupby('boot_id').agg({
                'strategy_1': ['mean', 'std'],
                'strategy_sum': ['mean', 'std'],
                'strategy_mean': ['mean', 'std']
            }).round(4)
            
            boot_stats.columns = ['_'.join(col).strip() for col in boot_stats.columns]
            boot_stats = boot_stats.add_prefix('boot_')
            
            df = df.merge(boot_stats, left_on='boot_id', right_index=True, how='left')
        
        # 全局统计窗口
        global_windows = config.get('global_windows', [50, 100, 200])
        for window in global_windows:
            df[f'global_s1_avg_{window}'] = df['strategy_1'].rolling(window=window, min_periods=1).mean()
            df[f'global_sum_avg_{window}'] = df['strategy_sum'].rolling(window=window, min_periods=1).mean()
        
        return df

    def create_advanced_features(self, df: pd.DataFrame, config: Dict[str, Any]) -> pd.DataFrame:
        """创建高级特征"""
        self.logger.info(f"   🚀 创建高级特征 (配置: {config})")

        # 频域特征
        if config.get('fourier_features', False):
            from scipy.fft import fft

            # 对策略序列进行傅里叶变换
            for col in ['strategy_1', 'strategy_sum']:
                # 按Boot分组进行FFT
                def apply_fft(group):
                    if len(group) > 4:  # 至少需要4个点
                        fft_result = fft(group.values)
                        # 取前几个频率分量的幅度
                        amplitudes = np.abs(fft_result[:min(3, len(fft_result)//2)])
                        return pd.Series(amplitudes, index=[f'{col}_fft_{i}' for i in range(len(amplitudes))])
                    else:
                        return pd.Series([0, 0, 0], index=[f'{col}_fft_{i}' for i in range(3)])

                fft_features = df.groupby('boot_id')[col].apply(apply_fft).reset_index()
                for i in range(3):
                    df = df.merge(fft_features[['boot_id', f'{col}_fft_{i}']], on='boot_id', how='left')

        # 滞后特征
        if 'lag_features' in config:
            lags = config['lag_features']
            for lag in lags:
                for col in ['strategy_1', 'strategy_sum']:
                    df[f'{col}_lag_{lag}'] = df.groupby('boot_id')[col].shift(lag).fillna(0)

        # 滚动相关性
        if config.get('rolling_correlations', False):
            window = 10
            df['s1_sum_rolling_corr'] = df.groupby('boot_id').apply(
                lambda x: x['strategy_1'].rolling(window=window, min_periods=3).corr(x['strategy_sum'])
            ).reset_index(0, drop=True).fillna(0)

        # 季节性分解 (模拟)
        if config.get('seasonal_decompose', False):
            # 简化的季节性特征
            df['boot_cycle'] = df['position_in_boot'] % 10  # 10个位置为一个周期
            df['boot_cycle_sin'] = np.sin(2 * np.pi * df['boot_cycle'] / 10)
            df['boot_cycle_cos'] = np.cos(2 * np.pi * df['boot_cycle'] / 10)

        # 变点检测
        if config.get('change_points', False):
            # 简化的变点特征
            for col in ['strategy_1', 'strategy_sum']:
                df[f'{col}_change_point'] = (df.groupby('boot_id')[col].diff().abs() >
                                           df.groupby('boot_id')[col].diff().abs().rolling(5, min_periods=1).mean()).astype(int)

        # 波动率特征
        if config.get('volatility_features', False):
            for window in [5, 10]:
                for col in ['strategy_1', 'strategy_sum']:
                    df[f'{col}_volatility_{window}'] = df.groupby('boot_id')[col].rolling(window=window, min_periods=1).std().reset_index(0, drop=True).fillna(0)

        # 策略PCA
        if config.get('strategy_pca', False):
            from sklearn.decomposition import PCA

            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            pca = PCA(n_components=3)
            pca_features = pca.fit_transform(df[strategy_cols])

            for i in range(3):
                df[f'strategy_pca_{i}'] = pca_features[:, i]

        # 策略聚类
        if config.get('strategy_clustering', False):
            from sklearn.cluster import KMeans

            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            kmeans = KMeans(n_clusters=4, random_state=42)
            df['strategy_cluster'] = kmeans.fit_predict(df[strategy_cols])

        # 集成特征
        if config.get('ensemble_features', False):
            # 多种投票方式
            df['weighted_vote'] = (df['strategy_1'] * 0.3 + df['strategy_8'] * 0.1 +
                                 df[['strategy_2', 'strategy_3', 'strategy_4', 'strategy_5',
                                     'strategy_6', 'strategy_7']].sum(axis=1) * 0.6 / 6)

            # 置信度特征
            df['vote_confidence'] = 1 - np.abs(df['strategy_sum'] - 4) / 4

        return df

    def bayesian_optimize_hyperparameters(self, X_train, y_train, X_test, y_test) -> Dict[str, Any]:
        """贝叶斯优化超参数"""
        try:
            from skopt import gp_minimize
            from skopt.space import Real, Integer
            from sklearn.model_selection import cross_val_score
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.metrics import accuracy_score
            import lightgbm as lgb

            self.logger.info("   🎯 开始贝叶斯超参数优化...")

            # 定义搜索空间
            space = [
                Real(0.01, 1.0, name='lgb_learning_rate'),
                Integer(50, 500, name='lgb_n_estimators'),
                Integer(3, 10, name='lgb_max_depth'),
                Real(0.5, 1.0, name='lgb_subsample'),
                Real(0.5, 1.0, name='lgb_colsample_bytree'),
                Integer(10, 100, name='lgb_min_child_samples')
            ]

            # 目标函数
            def objective(params):
                learning_rate, n_estimators, max_depth, subsample, colsample_bytree, min_child_samples = params

                model = lgb.LGBMClassifier(
                    learning_rate=learning_rate,
                    n_estimators=n_estimators,
                    max_depth=max_depth,
                    subsample=subsample,
                    colsample_bytree=colsample_bytree,
                    min_child_samples=min_child_samples,
                    random_state=42,
                    verbose=-1
                )

                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=3, scoring='accuracy')
                return -cv_scores.mean()  # 最小化负准确率

            # 贝叶斯优化
            result = gp_minimize(objective, space, n_calls=20, random_state=42)

            # 最佳参数
            best_params = {
                'learning_rate': result.x[0],
                'n_estimators': result.x[1],
                'max_depth': result.x[2],
                'subsample': result.x[3],
                'colsample_bytree': result.x[4],
                'min_child_samples': result.x[5]
            }

            # 用最佳参数训练模型
            best_model = lgb.LGBMClassifier(**best_params, random_state=42, verbose=-1)
            best_model.fit(X_train, y_train)

            y_pred = best_model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)

            self.logger.info(f"   🏆 贝叶斯优化最佳准确率: {accuracy:.3f}")

            return {
                'model': best_model,
                'accuracy': accuracy,
                'best_params': best_params,
                'optimization_result': result
            }

        except ImportError:
            self.logger.warning("   ⚠️ scikit-optimize未安装，跳过贝叶斯优化")
            return {}
        except Exception as e:
            self.logger.error(f"   ❌ 贝叶斯优化失败: {e}")
            return {}

    def train_and_evaluate(self, df: pd.DataFrame, feature_config: Dict[str, Any]) -> Dict[str, Any]:
        """训练和评估模型"""
        try:
            from sklearn.model_selection import train_test_split, cross_val_score
            from sklearn.linear_model import LogisticRegression
            from sklearn.ensemble import RandomForestClassifier, VotingClassifier
            from sklearn.metrics import accuracy_score
            from sklearn.preprocessing import StandardScaler
            import lightgbm as lgb
            
            # 准备特征
            exclude_cols = ['id', 'boot_id', 'actual_result', 'global_sequence'] + [f'strategy_{i}' for i in range(1, 9)]
            feature_cols = [col for col in df.columns if col not in exclude_cols]
            
            # 数据泄露检测
            leakage_features = self.detector.detect_leakage(df, feature_cols)
            if leakage_features:
                self.logger.warning(f"⚠️ 检测到可能的数据泄露特征: {leakage_features}")
                # 移除泄露特征
                feature_cols = [col for col in feature_cols if col not in leakage_features]
            
            X = df[feature_cols].values
            y = df['actual_result'].values
            
            # 特征标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 按Boot分割数据
            unique_boots = df['boot_id'].unique()
            train_boots, test_boots = train_test_split(unique_boots, test_size=0.2, random_state=42)
            
            train_mask = df['boot_id'].isin(train_boots)
            test_mask = df['boot_id'].isin(test_boots)
            
            X_train, X_test = X_scaled[train_mask], X_scaled[test_mask]
            y_train, y_test = y[train_mask], y[test_mask]
            
            # 模型配置
            model_config = feature_config.get('models', {})
            
            models = {
                'logistic': LogisticRegression(
                    random_state=42, 
                    max_iter=model_config.get('logistic_max_iter', 2000),
                    C=model_config.get('logistic_C', 0.1),
                    solver='liblinear'
                ),
                'random_forest': RandomForestClassifier(
                    n_estimators=model_config.get('rf_n_estimators', 200),
                    max_depth=model_config.get('rf_max_depth', 8),
                    min_samples_split=model_config.get('rf_min_samples_split', 10),
                    min_samples_leaf=model_config.get('rf_min_samples_leaf', 5),
                    random_state=42
                ),
                'lightgbm': lgb.LGBMClassifier(
                    n_estimators=model_config.get('lgb_n_estimators', 200),
                    max_depth=model_config.get('lgb_max_depth', 6),
                    learning_rate=model_config.get('lgb_learning_rate', 0.1),
                    subsample=model_config.get('lgb_subsample', 0.8),
                    colsample_bytree=model_config.get('lgb_colsample_bytree', 0.8),
                    random_state=42, verbose=-1
                )
            }
            
            # 训练和评估
            results = {}
            for name, model in models.items():
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std()
                }
            
            # 集成模型
            top_models = sorted(results.items(), key=lambda x: x[1]['cv_mean'], reverse=True)[:2]
            ensemble = VotingClassifier(
                estimators=[(name, result['model']) for name, result in top_models],
                voting='soft'
            )
            
            ensemble.fit(X_train, y_train)
            ensemble_pred = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
            
            return {
                'individual_models': results,
                'ensemble_accuracy': ensemble_accuracy,
                'feature_count': len(feature_cols),
                'leakage_detected': len(leakage_features) > 0,
                'leakage_features': leakage_features
            }
            
        except Exception as e:
            self.logger.error(f"❌ 训练评估失败: {e}")
            import traceback
            traceback.print_exc()
            return {'ensemble_accuracy': 0.0, 'error': str(e)}
    
    def save_optimization_result(self, iteration: int, config: Dict[str, Any], result: Dict[str, Any]):
        """保存优化结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"optimization_iter_{iteration:03d}_{timestamp}.json"
        filepath = self.results_dir / filename
        
        optimization_record = {
            'iteration': iteration,
            'timestamp': timestamp,
            'config': config,
            'result': {k: v for k, v in result.items() if k != 'individual_models'},  # 排除模型对象
            'accuracy': result.get('ensemble_accuracy', 0.0)
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(optimization_record, f, indent=2, ensure_ascii=False)
        
        self.optimization_history.append(optimization_record)
    
    def generate_feature_configs(self) -> List[Dict[str, Any]]:
        """生成特征配置"""
        configs = []
        
        # 配置1: 基础时间序列
        configs.append({
            'name': 'basic_timeseries',
            'time_series': {
                'alpha_boot': 0.3,
                'alpha_global': 0.1,
                'windows': [3, 5, 10],
                'trend_windows': [3, 5]
            },
            'interactions': {
                'position_interactions': True,
                'strategy_combinations': True,
                'nonlinear_transforms': False
            },
            'statistics': {
                'boot_statistics': True,
                'global_windows': [50, 100]
            }
        })
        
        # 配置2: 增强时间序列
        configs.append({
            'name': 'enhanced_timeseries',
            'time_series': {
                'alpha_boot': 0.2,
                'alpha_global': 0.05,
                'windows': [3, 5, 10, 20],
                'trend_windows': [3, 5, 7]
            },
            'interactions': {
                'position_interactions': True,
                'strategy_combinations': True,
                'nonlinear_transforms': True
            },
            'statistics': {
                'boot_statistics': True,
                'global_windows': [50, 100, 200]
            }
        })
        
        # 配置3: 非线性重点
        configs.append({
            'name': 'nonlinear_focus',
            'time_series': {
                'alpha_boot': 0.4,
                'alpha_global': 0.15,
                'windows': [5, 10],
                'trend_windows': [5]
            },
            'interactions': {
                'position_interactions': True,
                'strategy_combinations': True,
                'nonlinear_transforms': True
            },
            'statistics': {
                'boot_statistics': True,
                'global_windows': [100, 200]
            }
        })
        
        # 配置4: 长期历史
        configs.append({
            'name': 'long_history',
            'time_series': {
                'alpha_boot': 0.1,
                'alpha_global': 0.02,
                'windows': [10, 20, 50],
                'trend_windows': [10, 20]
            },
            'interactions': {
                'position_interactions': True,
                'strategy_combinations': True,
                'nonlinear_transforms': False
            },
            'statistics': {
                'boot_statistics': True,
                'global_windows': [100, 200, 500]
            }
        })
        
        # 配置5: 模型调优
        configs.append({
            'name': 'model_tuned',
            'time_series': {
                'alpha_boot': 0.3,
                'alpha_global': 0.1,
                'windows': [3, 5, 10],
                'trend_windows': [3, 5]
            },
            'interactions': {
                'position_interactions': True,
                'strategy_combinations': True,
                'nonlinear_transforms': True
            },
            'statistics': {
                'boot_statistics': True,
                'global_windows': [50, 100, 200]
            },
            'models': {
                'logistic_C': 0.01,
                'rf_n_estimators': 300,
                'rf_max_depth': 10,
                'lgb_n_estimators': 300,
                'lgb_learning_rate': 0.05
            }
        })

        # 配置6-10: 高级特征工程
        # 频域特征
        configs.append({
            'name': 'frequency_features',
            'time_series': {
                'alpha_boot': 0.25,
                'alpha_global': 0.08,
                'windows': [3, 5, 10, 15],
                'trend_windows': [3, 5, 7]
            },
            'interactions': {
                'position_interactions': True,
                'strategy_combinations': True,
                'nonlinear_transforms': True
            },
            'statistics': {
                'boot_statistics': True,
                'global_windows': [30, 60, 120]
            },
            'advanced': {
                'fourier_features': True,
                'lag_features': [1, 2, 3, 5],
                'rolling_correlations': True
            }
        })

        # 深度时间序列
        configs.append({
            'name': 'deep_timeseries',
            'time_series': {
                'alpha_boot': 0.15,
                'alpha_global': 0.03,
                'windows': [5, 10, 20, 30, 50],
                'trend_windows': [5, 10, 15]
            },
            'interactions': {
                'position_interactions': True,
                'strategy_combinations': True,
                'nonlinear_transforms': True
            },
            'statistics': {
                'boot_statistics': True,
                'global_windows': [50, 100, 200, 300]
            },
            'advanced': {
                'seasonal_decompose': True,
                'change_points': True,
                'volatility_features': True
            }
        })

        # 策略组合优化
        configs.append({
            'name': 'strategy_optimization',
            'time_series': {
                'alpha_boot': 0.3,
                'alpha_global': 0.1,
                'windows': [3, 5, 10],
                'trend_windows': [3, 5]
            },
            'interactions': {
                'position_interactions': True,
                'strategy_combinations': True,
                'nonlinear_transforms': True
            },
            'statistics': {
                'boot_statistics': True,
                'global_windows': [50, 100, 200]
            },
            'advanced': {
                'strategy_pca': True,
                'strategy_clustering': True,
                'ensemble_features': True
            }
        })

        # 贝叶斯优化配置
        configs.append({
            'name': 'bayesian_optimized',
            'time_series': {
                'alpha_boot': 0.3,
                'alpha_global': 0.1,
                'windows': [3, 5, 10],
                'trend_windows': [3, 5]
            },
            'interactions': {
                'position_interactions': True,
                'strategy_combinations': True,
                'nonlinear_transforms': True
            },
            'statistics': {
                'boot_statistics': True,
                'global_windows': [50, 100, 200]
            },
            'bayesian_optimization': True
        })

        return configs
    
    def run_optimization_cycle(self):
        """运行优化循环"""
        self.logger.info("🚀 开始自动化优化循环")
        self.logger.info(f"🎯 目标准确率: {self.target_accuracy:.1%}")
        self.logger.info("="*80)
        
        # 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，优化终止")
            return
        
        # 创建基础特征
        df = self.create_base_features(df)
        
        # 生成特征配置
        feature_configs = self.generate_feature_configs()
        
        for iteration, config in enumerate(feature_configs, 1):
            self.logger.info(f"\n🔄 优化迭代 {iteration}/{len(feature_configs)}: {config['name']}")
            self.logger.info("-" * 60)
            
            try:
                # 创建特征的副本
                df_iter = df.copy()
                
                # 应用特征工程
                if 'time_series' in config:
                    df_iter = self.create_time_series_features(df_iter, config['time_series'])

                if 'interactions' in config:
                    df_iter = self.create_interaction_features(df_iter, config['interactions'])

                if 'statistics' in config:
                    df_iter = self.create_statistical_features(df_iter, config['statistics'])

                if 'advanced' in config:
                    df_iter = self.create_advanced_features(df_iter, config['advanced'])
                
                # 填充和清理
                numeric_cols = df_iter.select_dtypes(include=[np.number]).columns
                df_iter[numeric_cols] = df_iter[numeric_cols].fillna(0)
                df_iter[numeric_cols] = df_iter[numeric_cols].replace([np.inf, -np.inf], 0)
                
                # 训练和评估
                result = self.train_and_evaluate(df_iter, config)

                # 如果启用贝叶斯优化，进行额外优化
                if config.get('bayesian_optimization', False) and result.get('ensemble_accuracy', 0) > 0:
                    self.logger.info("   🎯 启动贝叶斯优化...")

                    # 准备数据
                    exclude_cols = ['id', 'boot_id', 'actual_result', 'global_sequence'] + [f'strategy_{i}' for i in range(1, 9)]
                    feature_cols = [col for col in df_iter.columns if col not in exclude_cols]

                    # 数据泄露检测
                    leakage_features = self.detector.detect_leakage(df_iter, feature_cols)
                    if leakage_features:
                        feature_cols = [col for col in feature_cols if col not in leakage_features]

                    X = df_iter[feature_cols].values
                    y = df_iter['actual_result'].values

                    from sklearn.model_selection import train_test_split
                    from sklearn.preprocessing import StandardScaler
                    from sklearn.metrics import accuracy_score

                    # 特征标准化
                    scaler = StandardScaler()
                    X_scaled = scaler.fit_transform(X)

                    # 按Boot分割数据
                    unique_boots = df_iter['boot_id'].unique()
                    train_boots, test_boots = train_test_split(unique_boots, test_size=0.2, random_state=42)

                    train_mask = df_iter['boot_id'].isin(train_boots)
                    test_mask = df_iter['boot_id'].isin(test_boots)

                    X_train, X_test = X_scaled[train_mask], X_scaled[test_mask]
                    y_train, y_test = y[train_mask], y[test_mask]

                    # 贝叶斯优化
                    bayesian_result = self.bayesian_optimize_hyperparameters(X_train, y_train, X_test, y_test)

                    if bayesian_result and bayesian_result.get('accuracy', 0) > result.get('ensemble_accuracy', 0):
                        self.logger.info(f"   🎉 贝叶斯优化提升: {bayesian_result['accuracy']:.3f} > {result.get('ensemble_accuracy', 0):.3f}")
                        result['ensemble_accuracy'] = bayesian_result['accuracy']
                        result['bayesian_optimized'] = True
                        result['best_params'] = bayesian_result.get('best_params', {})
                
                # 记录结果
                accuracy = result.get('ensemble_accuracy', 0.0)
                self.logger.info(f"   📊 集成准确率: {accuracy:.3f}")
                self.logger.info(f"   📊 特征数量: {result.get('feature_count', 0)}")
                
                if result.get('leakage_detected', False):
                    self.logger.warning(f"   ⚠️ 检测到数据泄露: {result.get('leakage_features', [])}")
                
                # 保存结果
                self.save_optimization_result(iteration, config, result)
                
                # 更新最佳结果
                if accuracy > self.current_best_accuracy:
                    self.current_best_accuracy = accuracy
                    self.logger.info(f"   🎉 新的最佳准确率: {accuracy:.3f}")
                
                # 检查是否达到目标
                if accuracy >= self.target_accuracy:
                    self.logger.info(f"   🎯 达到目标准确率 {self.target_accuracy:.1%}!")
                    self.logger.info(f"   🏆 最终准确率: {accuracy:.3f}")
                    break
                
            except Exception as e:
                self.logger.error(f"   ❌ 迭代 {iteration} 失败: {e}")
                continue
        
        # 生成最终报告
        self.generate_final_report()
    
    def generate_final_report(self):
        """生成最终报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 自动化优化最终报告")
        self.logger.info("="*80)
        
        if not self.optimization_history:
            self.logger.info("❌ 没有优化历史记录")
            return
        
        # 排序结果
        sorted_results = sorted(self.optimization_history, key=lambda x: x['accuracy'], reverse=True)
        
        self.logger.info(f"\n📊 优化统计:")
        self.logger.info(f"   - 总迭代次数: {len(self.optimization_history)}")
        self.logger.info(f"   - 最佳准确率: {self.current_best_accuracy:.3f}")
        self.logger.info(f"   - 目标准确率: {self.target_accuracy:.3f}")
        
        if self.current_best_accuracy >= self.target_accuracy:
            self.logger.info(f"   🎉 成功达到目标！")
        else:
            gap = self.target_accuracy - self.current_best_accuracy
            self.logger.info(f"   📊 距离目标还差: {gap:.3f}")
        
        self.logger.info(f"\n🏆 Top 3 配置:")
        for i, result in enumerate(sorted_results[:3], 1):
            self.logger.info(f"   {i}. {result['config']['name']}: {result['accuracy']:.3f}")
        
        # 保存最终报告
        report_path = self.results_dir / f"final_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total_iterations': len(self.optimization_history),
                    'best_accuracy': self.current_best_accuracy,
                    'target_accuracy': self.target_accuracy,
                    'target_achieved': self.current_best_accuracy >= self.target_accuracy
                },
                'top_results': sorted_results[:5]
            }, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"\n📄 详细报告已保存: {report_path}")
        self.logger.info("="*80)

if __name__ == "__main__":
    optimizer = AutoOptimizer(target_accuracy=0.60)
    optimizer.run_optimization_cycle()
