#!/usr/bin/env python3
"""
V8系统真实数据训练器
只使用真实数据库数据，不使用任何模拟数据
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class RealDataTrainer:
    """真实数据训练器 - 只使用数据库中的真实历史数据"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def test_database_connection(self):
        """测试数据库连接"""
        try:
            import pymysql
            
            self.logger.info("测试数据库连接...")
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            # 测试查询
            cursor = connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM strategy_results")
            total_count = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT COUNT(*) FROM strategy_results
                WHERE strategy_1 IS NOT NULL
                    AND strategy_2 IS NOT NULL
                    AND strategy_6 IS NOT NULL
                    AND true_label IS NOT NULL
            """)
            valid_count = cursor.fetchone()[0]
            
            cursor.close()
            connection.close()
            
            self.logger.info(f"✅ 数据库连接成功")
            self.logger.info(f"   总记录数: {total_count}")
            self.logger.info(f"   有效记录数: {valid_count}")
            
            return True, valid_count
            
        except Exception as e:
            self.logger.error(f"❌ 数据库连接失败: {e}")
            return False, 0
    
    def load_historical_data(self, limit: int = 1000, offset: int = 0) -> pd.DataFrame:
        """从数据库加载真实历史数据"""
        try:
            import pymysql
            
            self.logger.info(f"从数据库加载 {limit} 条历史数据 (偏移: {offset})...")
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            # 查询SQL - 只获取完整的记录
            sql = """
            SELECT
                id,
                boot_id,
                strategy_1,
                strategy_2,
                strategy_6,
                true_label as actual_result
            FROM strategy_results
            WHERE strategy_1 IS NOT NULL
                AND strategy_2 IS NOT NULL
                AND strategy_6 IS NOT NULL
                AND true_label IS NOT NULL
            ORDER BY boot_id, id
            LIMIT %s OFFSET %s
            """
            
            df = pd.read_sql(sql, connection, params=(limit, offset))
            connection.close()
            
            if len(df) == 0:
                self.logger.warning("⚠️ 没有找到符合条件的数据")
                return pd.DataFrame()
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条真实历史数据")
            
            # 数据质量检查
            self.logger.info("📊 数据质量检查:")
            self.logger.info(f"   - Boot范围: {df['boot_id'].min()} - {df['boot_id'].max()}")
            self.logger.info(f"   - 策略1分布: {df['strategy_1'].value_counts().to_dict()}")
            self.logger.info(f"   - 策略2分布: {df['strategy_2'].value_counts().to_dict()}")
            self.logger.info(f"   - 策略6分布: {df['strategy_6'].value_counts().to_dict()}")

            # 检查actual_result的数据类型和内容
            self.logger.info(f"   - actual_result数据类型: {df['actual_result'].dtype}")
            self.logger.info(f"   - actual_result样本值: {df['actual_result'].iloc[:5].tolist()}")

            # 尝试转换actual_result为数值
            try:
                # 如果是字符串，尝试取第一个字符并转换为数值
                if df['actual_result'].dtype == 'object':
                    df['actual_result'] = df['actual_result'].astype(str).str[0].astype(int)
                else:
                    df['actual_result'] = df['actual_result'].astype(int)

                self.logger.info(f"   - 实际结果分布: {df['actual_result'].value_counts().to_dict()}")
                self.logger.info(f"   - 正例比例: {df['actual_result'].mean():.3f}")
            except Exception as e:
                self.logger.error(f"   - actual_result转换失败: {e}")
                return pd.DataFrame()
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载历史数据失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return pd.DataFrame()
    
    def prepare_training_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        if df.empty:
            self.logger.error("❌ 数据为空，无法准备训练数据")
            return np.array([]), np.array([])
        
        # 特征：3个策略的输出
        features = df[['strategy_1', 'strategy_2', 'strategy_6']].values
        
        # 标签：实际结果
        labels = df['actual_result'].values
        
        self.logger.info(f"✅ 准备了 {len(features)} 个训练样本，{features.shape[1]} 个特征")
        
        # 检查数据平衡性
        unique, counts = np.unique(labels, return_counts=True)
        self.logger.info(f"   标签分布: {dict(zip(unique, counts))}")
        
        return features, labels
    
    def train_models(self, X: np.ndarray, y: np.ndarray):
        """训练模型"""
        if len(X) == 0:
            self.logger.error("❌ 没有训练数据")
            return None
        
        try:
            from sklearn.model_selection import train_test_split
            from sklearn.ensemble import VotingClassifier, RandomForestClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            self.logger.info(f"📊 数据分割:")
            self.logger.info(f"   训练集: {len(X_train)} 样本")
            self.logger.info(f"   测试集: {len(X_test)} 样本")
            
            # 创建模型
            models = {
                'logistic': LogisticRegression(random_state=42, max_iter=1000),
                'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
            }
            
            # 训练和评估每个模型
            results = {}
            for name, model in models.items():
                self.logger.info(f"🤖 训练 {name} 模型...")
                
                start_time = time.time()
                model.fit(X_train, y_train)
                train_time = time.time() - start_time
                
                # 预测
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'train_time': train_time,
                    'predictions': y_pred
                }
                
                self.logger.info(f"   ✅ {name} - 准确率: {accuracy:.4f}, 训练时间: {train_time:.2f}秒")
            
            # 创建集成模型
            self.logger.info("🔗 创建集成模型...")
            ensemble = VotingClassifier(
                estimators=[(name, model['model']) for name, model in results.items()],
                voting='soft'
            )
            
            start_time = time.time()
            ensemble.fit(X_train, y_train)
            train_time = time.time() - start_time
            
            y_pred_ensemble = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, y_pred_ensemble)
            
            self.logger.info(f"   ✅ 集成模型 - 准确率: {ensemble_accuracy:.4f}, 训练时间: {train_time:.2f}秒")
            
            # 详细报告
            self.logger.info("\n" + "="*60)
            self.logger.info("🎯 训练结果总结:")
            self.logger.info("="*60)
            
            for name, result in results.items():
                self.logger.info(f"{name:15} - 准确率: {result['accuracy']:.4f}")
            
            self.logger.info(f"{'集成模型':15} - 准确率: {ensemble_accuracy:.4f}")
            
            # 混淆矩阵
            self.logger.info(f"\n📊 集成模型混淆矩阵:")
            cm = confusion_matrix(y_test, y_pred_ensemble)
            self.logger.info(f"   {cm}")
            
            self.logger.info("="*60)
            
            return {
                'individual_models': results,
                'ensemble': ensemble,
                'ensemble_accuracy': ensemble_accuracy,
                'test_data': (X_test, y_test, y_pred_ensemble)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 模型训练失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def run_training(self, total_samples: int = 10000):
        """运行完整训练流程"""
        self.logger.info("🚀 开始真实数据训练流程")
        self.logger.info("="*60)
        
        # 1. 测试数据库连接
        success, available_count = self.test_database_connection()
        if not success:
            self.logger.error("❌ 无法连接数据库，训练终止")
            return
        
        if available_count == 0:
            self.logger.error("❌ 数据库中没有有效数据，训练终止")
            return
        
        # 2. 确定实际训练样本数
        actual_samples = min(total_samples, available_count)
        self.logger.info(f"📊 计划训练样本数: {actual_samples} (可用: {available_count})")
        
        # 3. 加载数据
        df = self.load_historical_data(limit=actual_samples)
        if df.empty:
            self.logger.error("❌ 无法加载训练数据，训练终止")
            return
        
        # 4. 准备训练数据
        X, y = self.prepare_training_data(df)
        if len(X) == 0:
            self.logger.error("❌ 无法准备训练数据，训练终止")
            return
        
        # 5. 训练模型
        results = self.train_models(X, y)
        if results is None:
            self.logger.error("❌ 模型训练失败")
            return
        
        self.logger.info("\n🎉 训练完成！")
        self.logger.info(f"🏆 最佳准确率: {results['ensemble_accuracy']:.4f}")

if __name__ == "__main__":
    trainer = RealDataTrainer()
    trainer.run_training(total_samples=5000)
