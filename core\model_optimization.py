#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型性能优化模块

实现超参数调优、特征选择、模型压缩等性能优化技术
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import time
from collections import deque
import itertools

try:
    from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
    from sklearn.feature_selection import SelectKBest, f_classif, RFE
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


@dataclass
class OptimizationResult:
    """优化结果"""
    method: str
    best_params: Dict[str, Any]
    best_score: float
    improvement: float
    optimization_time: float
    details: Dict[str, Any]


@dataclass
class FeatureImportance:
    """特征重要性"""
    feature_name: str
    importance_score: float
    rank: int
    selection_method: str


class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化超参数优化器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('hyperparameter_optimization', {})
        self.logger = logging.getLogger(__name__)
        
        # 优化方法
        self.optimization_method = self.config.get('method', 'grid_search')
        self.cv_folds = self.config.get('cv_folds', 3)
        self.max_iterations = self.config.get('max_iterations', 50)
        
        # 优化历史
        self.optimization_history = deque(maxlen=100)
        
    def optimize_model(self, model, X: np.ndarray, y: np.ndarray, 
                      param_grid: Dict[str, List]) -> OptimizationResult:
        """
        优化模型超参数
        
        Args:
            model: 要优化的模型
            X: 特征数据
            y: 标签数据
            param_grid: 参数网格
            
        Returns:
            优化结果
        """
        start_time = time.time()
        
        if not SKLEARN_AVAILABLE:
            return self._simple_optimization(model, X, y, param_grid, start_time)
        
        try:
            # 记录基线性能
            baseline_score = self._evaluate_model(model, X, y)

            # 检查模型是否是sklearn兼容的
            if not hasattr(model, 'fit') or not hasattr(model, 'predict'):
                self.logger.warning(f"模型 {model_name} 不兼容sklearn，跳过超参数优化")
                return {
                    'best_params': {},
                    'best_score': baseline_score,
                    'baseline_score': baseline_score,
                    'improvement': 0.0
                }

            # 选择优化方法
            if self.optimization_method == 'grid_search':
                optimizer = GridSearchCV(
                    model, param_grid,
                    cv=self.cv_folds,
                    scoring='accuracy',
                    n_jobs=-1
                )
            elif self.optimization_method == 'random_search':
                optimizer = RandomizedSearchCV(
                    model, param_grid,
                    n_iter=self.max_iterations,
                    cv=self.cv_folds,
                    scoring='accuracy',
                    n_jobs=-1,
                    random_state=42
                )
            else:
                return self._simple_optimization(model, X, y, param_grid, start_time)
            
            # 执行优化
            optimizer.fit(X, y)
            
            # 计算改进
            improvement = optimizer.best_score_ - baseline_score
            optimization_time = time.time() - start_time
            
            # 创建结果
            result = OptimizationResult(
                method=self.optimization_method,
                best_params=optimizer.best_params_,
                best_score=optimizer.best_score_,
                improvement=improvement,
                optimization_time=optimization_time,
                details={
                    'baseline_score': baseline_score,
                    'cv_results': optimizer.cv_results_,
                    'best_estimator': optimizer.best_estimator_
                }
            )
            
            # 记录历史
            self.optimization_history.append(result)
            
            self.logger.info(f"超参数优化完成: {self.optimization_method}")
            self.logger.info(f"最佳得分: {optimizer.best_score_:.4f} (改进: {improvement:.4f})")
            self.logger.info(f"最佳参数: {optimizer.best_params_}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"超参数优化失败: {str(e)}")
            return self._simple_optimization(model, X, y, param_grid, start_time)
    
    def _simple_optimization(self, model, X: np.ndarray, y: np.ndarray,
                           param_grid: Dict[str, List], start_time: float) -> OptimizationResult:
        """简化的超参数优化"""
        baseline_score = self._evaluate_model(model, X, y)
        
        best_score = baseline_score
        best_params = {}
        
        # 简单的网格搜索
        param_combinations = list(itertools.product(*param_grid.values()))
        param_names = list(param_grid.keys())
        
        for combination in param_combinations[:10]:  # 限制搜索次数
            params = dict(zip(param_names, combination))
            
            try:
                # 设置参数
                for param, value in params.items():
                    if hasattr(model, param):
                        setattr(model, param, value)
                
                # 评估模型
                score = self._evaluate_model(model, X, y)
                
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                    
            except Exception as e:
                self.logger.warning(f"参数组合 {params} 评估失败: {str(e)}")
        
        improvement = best_score - baseline_score
        optimization_time = time.time() - start_time
        
        result = OptimizationResult(
            method='simple_grid_search',
            best_params=best_params,
            best_score=best_score,
            improvement=improvement,
            optimization_time=optimization_time,
            details={'baseline_score': baseline_score}
        )
        
        self.optimization_history.append(result)
        
        self.logger.info(f"简化超参数优化完成")
        self.logger.info(f"最佳得分: {best_score:.4f} (改进: {improvement:.4f})")
        
        return result
    
    def _evaluate_model(self, model, X: np.ndarray, y: np.ndarray) -> float:
        """评估模型性能"""
        try:
            if hasattr(model, 'fit') and hasattr(model, 'score'):
                model.fit(X, y)
                return model.score(X, y)
            else:
                # 简化评估
                return 0.5
        except:
            return 0.5
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化总结"""
        if not self.optimization_history:
            return {'total_optimizations': 0}
        
        improvements = [opt.improvement for opt in self.optimization_history]
        times = [opt.optimization_time for opt in self.optimization_history]
        
        return {
            'total_optimizations': len(self.optimization_history),
            'average_improvement': np.mean(improvements),
            'best_improvement': max(improvements),
            'average_time': np.mean(times),
            'recent_optimizations': list(self.optimization_history)[-5:]
        }


class FeatureSelector:
    """特征选择器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化特征选择器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('feature_selection', {})
        self.logger = logging.getLogger(__name__)
        
        # 选择方法
        self.selection_methods = self.config.get('methods', ['univariate', 'rfe'])
        self.k_features = self.config.get('k_features', 10)
        
        # 特征重要性历史
        self.feature_importance_history = deque(maxlen=50)
        
    def select_features(self, X: np.ndarray, y: np.ndarray, 
                       feature_names: List[str]) -> Tuple[np.ndarray, List[FeatureImportance]]:
        """
        选择重要特征
        
        Args:
            X: 特征数据
            y: 标签数据
            feature_names: 特征名称列表
            
        Returns:
            选择后的特征数据和特征重要性列表
        """
        if not SKLEARN_AVAILABLE:
            return self._simple_feature_selection(X, y, feature_names)
        
        feature_importances = []
        selected_features = None
        
        for method in self.selection_methods:
            try:
                if method == 'univariate':
                    importances = self._univariate_selection(X, y, feature_names)
                elif method == 'rfe':
                    importances = self._rfe_selection(X, y, feature_names)
                else:
                    continue
                
                feature_importances.extend(importances)
                
            except Exception as e:
                self.logger.error(f"特征选择方法 {method} 失败: {str(e)}")
        
        # 合并和排序特征重要性
        if feature_importances:
            # 按重要性得分排序
            feature_importances.sort(key=lambda x: x.importance_score, reverse=True)
            
            # 选择前k个特征
            selected_feature_names = [fi.feature_name for fi in feature_importances[:self.k_features]]
            selected_indices = [feature_names.index(name) for name in selected_feature_names if name in feature_names]
            
            if selected_indices:
                selected_features = X[:, selected_indices]
            else:
                selected_features = X
        else:
            selected_features = X
        
        # 记录历史
        self.feature_importance_history.append({
            'timestamp': time.time(),
            'feature_importances': feature_importances,
            'selected_features': len(selected_indices) if 'selected_indices' in locals() else X.shape[1]
        })
        
        self.logger.info(f"特征选择完成: {X.shape[1]} -> {selected_features.shape[1]} 个特征")
        
        return selected_features, feature_importances
    
    def _univariate_selection(self, X: np.ndarray, y: np.ndarray, 
                            feature_names: List[str]) -> List[FeatureImportance]:
        """单变量特征选择"""
        selector = SelectKBest(score_func=f_classif, k='all')
        selector.fit(X, y)
        
        scores = selector.scores_
        importances = []
        
        for i, (name, score) in enumerate(zip(feature_names, scores)):
            importances.append(FeatureImportance(
                feature_name=name,
                importance_score=score,
                rank=i + 1,
                selection_method='univariate'
            ))
        
        return importances
    
    def _rfe_selection(self, X: np.ndarray, y: np.ndarray,
                      feature_names: List[str]) -> List[FeatureImportance]:
        """递归特征消除"""
        from sklearn.ensemble import RandomForestClassifier
        
        estimator = RandomForestClassifier(n_estimators=10, random_state=42)
        selector = RFE(estimator, n_features_to_select=min(self.k_features, len(feature_names)))
        selector.fit(X, y)
        
        rankings = selector.ranking_
        importances = []
        
        for i, (name, rank) in enumerate(zip(feature_names, rankings)):
            # 转换排名为重要性得分（排名越低，重要性越高）
            importance_score = 1.0 / rank
            
            importances.append(FeatureImportance(
                feature_name=name,
                importance_score=importance_score,
                rank=rank,
                selection_method='rfe'
            ))
        
        return importances
    
    def _simple_feature_selection(self, X: np.ndarray, y: np.ndarray,
                                feature_names: List[str]) -> Tuple[np.ndarray, List[FeatureImportance]]:
        """简化的特征选择"""
        # 计算简单的相关性
        importances = []
        
        for i, name in enumerate(feature_names):
            # 计算特征与标签的相关性
            correlation = np.corrcoef(X[:, i], y)[0, 1]
            if np.isnan(correlation):
                correlation = 0.0
            
            importances.append(FeatureImportance(
                feature_name=name,
                importance_score=abs(correlation),
                rank=i + 1,
                selection_method='correlation'
            ))
        
        # 排序并选择前k个特征
        importances.sort(key=lambda x: x.importance_score, reverse=True)
        
        selected_indices = []
        for i, importance in enumerate(importances[:self.k_features]):
            try:
                idx = feature_names.index(importance.feature_name)
                selected_indices.append(idx)
            except ValueError:
                continue
        
        if selected_indices:
            selected_features = X[:, selected_indices]
        else:
            selected_features = X
        
        return selected_features, importances
    
    def get_feature_selection_summary(self) -> Dict[str, Any]:
        """获取特征选择总结"""
        if not self.feature_importance_history:
            return {'total_selections': 0}
        
        recent_selection = self.feature_importance_history[-1]
        
        return {
            'total_selections': len(self.feature_importance_history),
            'recent_selected_features': recent_selection['selected_features'],
            'selection_methods': self.selection_methods,
            'k_features': self.k_features
        }


class ModelOptimizationManager:
    """模型优化管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化模型优化管理器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('model_optimization', {})
        self.logger = logging.getLogger(__name__)
        
        # 初始化优化器
        self.hyperparameter_optimizer = HyperparameterOptimizer(config)
        self.feature_selector = FeatureSelector(config)
        
        # 优化策略
        self.optimization_strategy = self.config.get('strategy', 'comprehensive')
        self.optimization_frequency = self.config.get('frequency', 100)  # 每100次决策优化一次
        
        # 性能监控
        self.performance_history = deque(maxlen=1000)
        self.last_optimization = 0
        
        self.logger.info("模型优化管理器初始化完成")
    
    def should_optimize(self, decision_count: int) -> bool:
        """判断是否应该进行优化"""
        # 基于决策数量的优化频率
        if decision_count - self.last_optimization >= self.optimization_frequency:
            return True
        
        # 基于性能下降的优化触发
        if len(self.performance_history) >= 20:
            recent_performance = list(self.performance_history)[-10:]
            older_performance = list(self.performance_history)[-20:-10]
            
            recent_avg = np.mean(recent_performance)
            older_avg = np.mean(older_performance)
            
            # 如果性能下降超过5%，触发优化
            if recent_avg < older_avg - 0.05:
                return True
        
        return False
    
    def optimize_models(self, models: Dict[str, Any], training_data: List[Dict[str, Any]]) -> Dict[str, OptimizationResult]:
        """
        优化模型集合
        
        Args:
            models: 模型字典
            training_data: 训练数据
            
        Returns:
            优化结果字典
        """
        # 训练期间禁用数据量检查
        # if len(training_data) < 10:
        #     self.logger.warning(f"训练数据不足({len(training_data)}条)，跳过优化")
        #     return {}
        
        self.logger.info(f"开始模型优化，数据量: {len(training_data)}")
        
        # 准备训练数据
        X, y, feature_names = self._prepare_training_data(training_data)
        
        optimization_results = {}
        
        # 特征选择
        if self.optimization_strategy in ['comprehensive', 'feature_only']:
            try:
                X_selected, feature_importances = self.feature_selector.select_features(X, y, feature_names)
                self.logger.info(f"特征选择完成: {X.shape[1]} -> {X_selected.shape[1]}")
                
                optimization_results['feature_selection'] = OptimizationResult(
                    method='feature_selection',
                    best_params={'selected_features': len(feature_importances)},
                    best_score=0.0,  # 特征选择不直接产生得分
                    improvement=0.0,
                    optimization_time=0.0,
                    details={'feature_importances': feature_importances}
                )
                
                X = X_selected  # 使用选择后的特征
                
            except Exception as e:
                self.logger.error(f"特征选择失败: {str(e)}")
        
        # 超参数优化
        if self.optimization_strategy in ['comprehensive', 'hyperparameter_only']:
            for model_name, model in models.items():
                try:
                    param_grid = self._get_param_grid(model_name)
                    if param_grid:
                        result = self.hyperparameter_optimizer.optimize_model(model, X, y, param_grid)
                        optimization_results[model_name] = result
                        
                except Exception as e:
                    self.logger.error(f"模型 {model_name} 优化失败: {str(e)}")
        
        self.last_optimization = time.time()
        
        return optimization_results
    
    def _prepare_training_data(self, training_data: List[Dict[str, Any]]) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """准备训练数据"""
        features_list = []
        labels_list = []
        
        for i, data in enumerate(training_data):
            if 'features' in data and 'actual_result' in data:
                features_list.append(data['features'])
                labels_list.append(data['actual_result'])
            else:
                self.logger.debug(f"训练数据 {i} 缺少必要字段: {list(data.keys())}")

        if not features_list:
            self.logger.error(f"没有有效的训练数据。总数据量: {len(training_data)}")
            if training_data:
                self.logger.error(f"第一条数据示例: {training_data[0]}")
            raise ValueError("没有有效的训练数据")
        
        # 获取特征名称
        feature_names = list(features_list[0].keys())
        
        # 转换为数组
        X = np.array([[features.get(name, 0.0) for name in feature_names] for features in features_list])
        y = np.array(labels_list)
        
        return X, y, feature_names
    
    def _get_param_grid(self, model_name: str) -> Dict[str, List]:
        """获取模型的参数网格"""
        param_grids = {
            'voting': {
                # 投票模型通常没有需要优化的超参数
            },
            'xgboost': {
                'n_estimators': [50, 100, 200],
                'max_depth': [3, 6, 9],
                'learning_rate': [0.01, 0.1, 0.2]
            },
            'neural_network': {
                'hidden_layer_sizes': [(50,), (100,), (50, 50)],
                'learning_rate_init': [0.001, 0.01, 0.1],
                'alpha': [0.0001, 0.001, 0.01]
            },
            'meta_learner': {
                'C': [0.1, 1.0, 10.0],
                'penalty': ['l1', 'l2']
            }
        }
        
        return param_grids.get(model_name, {})
    
    def record_performance(self, accuracy: float):
        """记录性能数据"""
        self.performance_history.append(accuracy)
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """获取优化状态"""
        hyperparameter_summary = self.hyperparameter_optimizer.get_optimization_summary()
        feature_selection_summary = self.feature_selector.get_feature_selection_summary()
        
        return {
            'optimization_strategy': self.optimization_strategy,
            'optimization_frequency': self.optimization_frequency,
            'last_optimization': self.last_optimization,
            'performance_history_length': len(self.performance_history),
            'hyperparameter_optimization': hyperparameter_summary,
            'feature_selection': feature_selection_summary,
            'should_optimize_next': len(self.performance_history) > 0 and 
                                   self.should_optimize(len(self.performance_history))
        }
