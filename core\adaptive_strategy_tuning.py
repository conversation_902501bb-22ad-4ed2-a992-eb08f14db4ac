#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应策略调优系统

实现策略权重自动调整、动态阈值优化、环境变化自适应等智能调优机制
"""

import numpy as np
import pandas as pd
import logging
import time
import json
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from enum import Enum
import warnings
from collections import deque, defaultdict

try:
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    from scipy import stats
    from scipy.optimize import minimize
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False


class TuningMethod(Enum):
    """调优方法枚举"""
    PERFORMANCE_BASED = "performance_based"
    GRADIENT_DESCENT = "gradient_descent"
    EVOLUTIONARY = "evolutionary"
    BAYESIAN = "bayesian"
    REINFORCEMENT = "reinforcement"
    ENSEMBLE = "ensemble"


class EnvironmentState(Enum):
    """环境状态枚举"""
    STABLE = "stable"
    TRENDING = "trending"
    VOLATILE = "volatile"
    REGIME_CHANGE = "regime_change"
    UNKNOWN = "unknown"


@dataclass
class StrategyPerformance:
    """策略性能指标"""
    strategy_name: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    win_rate: float
    avg_confidence: float
    consistency: float
    recent_performance: float
    trend: float
    volatility: float
    timestamp: float


@dataclass
class TuningResult:
    """调优结果"""
    tuning_id: str
    method: TuningMethod
    old_weights: Dict[str, float]
    new_weights: Dict[str, float]
    old_thresholds: Dict[str, float]
    new_thresholds: Dict[str, float]
    performance_improvement: float
    confidence: float
    reasoning: str
    timestamp: float


@dataclass
class EnvironmentDetection:
    """环境检测结果"""
    current_state: EnvironmentState
    confidence: float
    change_points: List[int]
    trend_direction: float
    volatility_level: float
    regime_probability: Dict[str, float]
    recommendations: List[str]
    timestamp: float


class PerformanceTracker:
    """性能跟踪器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('performance_tracker', {})
        self.logger = logging.getLogger(__name__)
        
        # 性能历史
        self.strategy_history = defaultdict(deque)
        self.decision_history = deque(maxlen=self.config.get('max_history', 1000))
        self.performance_window = self.config.get('performance_window', 50)
        
        # 性能指标
        self.current_performance = {}
        self.baseline_performance = {}
        
    def record_strategy_decision(self, strategy_name: str, prediction: int, 
                               confidence: float, actual: Optional[int] = None):
        """记录策略决策"""
        record = {
            'prediction': prediction,
            'confidence': confidence,
            'actual': actual,
            'timestamp': time.time(),
            'correct': actual == prediction if actual is not None else None
        }
        
        self.strategy_history[strategy_name].append(record)
        
        # 保持历史记录在合理范围内
        if len(self.strategy_history[strategy_name]) > self.performance_window * 2:
            # 移除最旧的记录
            for _ in range(self.performance_window // 2):
                self.strategy_history[strategy_name].popleft()
    
    def record_system_decision(self, decision_data: Dict[str, Any]):
        """记录系统决策"""
        self.decision_history.append({
            'timestamp': time.time(),
            'prediction': decision_data.get('prediction'),
            'confidence': decision_data.get('confidence'),
            'actual': decision_data.get('actual'),
            'strategy_outputs': decision_data.get('strategy_outputs', {}),
            'weights': decision_data.get('weights', {}),
            'features': decision_data.get('features', {})
        })
    
    def calculate_strategy_performance(self, strategy_name: str) -> StrategyPerformance:
        """计算策略性能"""
        if strategy_name not in self.strategy_history:
            return self._default_performance(strategy_name)
        
        history = list(self.strategy_history[strategy_name])
        if len(history) < 5:
            return self._default_performance(strategy_name)
        
        # 过滤有实际结果的记录
        labeled_records = [r for r in history if r['actual'] is not None]
        if len(labeled_records) < 3:
            return self._default_performance(strategy_name)
        
        # 计算基础指标
        predictions = [r['prediction'] for r in labeled_records]
        actuals = [r['actual'] for r in labeled_records]
        confidences = [r['confidence'] for r in labeled_records]
        
        try:
            accuracy = accuracy_score(actuals, predictions)
            precision = precision_score(actuals, predictions, average='weighted', zero_division=0)
            recall = recall_score(actuals, predictions, average='weighted', zero_division=0)
            f1 = f1_score(actuals, predictions, average='weighted', zero_division=0)
        except Exception:
            accuracy = precision = recall = f1 = 0.5
        
        # 计算胜率
        win_rate = sum(1 for r in labeled_records if r['correct']) / len(labeled_records)
        
        # 计算平均置信度
        avg_confidence = np.mean(confidences)
        
        # 计算一致性（预测的稳定性）
        prediction_changes = sum(1 for i in range(1, len(predictions)) 
                               if predictions[i] != predictions[i-1])
        consistency = 1.0 - (prediction_changes / max(len(predictions) - 1, 1))
        
        # 计算最近性能（最近20%的记录）
        recent_count = max(len(labeled_records) // 5, 3)
        recent_records = labeled_records[-recent_count:]
        recent_performance = sum(1 for r in recent_records if r['correct']) / len(recent_records)
        
        # 计算趋势
        if len(labeled_records) >= 10:
            # 将记录分为前半部分和后半部分
            mid_point = len(labeled_records) // 2
            first_half = labeled_records[:mid_point]
            second_half = labeled_records[mid_point:]
            
            first_half_acc = sum(1 for r in first_half if r['correct']) / len(first_half)
            second_half_acc = sum(1 for r in second_half if r['correct']) / len(second_half)
            
            trend = second_half_acc - first_half_acc
        else:
            trend = 0.0
        
        # 计算波动率
        if len(labeled_records) >= 5:
            # 计算滑动窗口准确率的标准差
            window_size = min(5, len(labeled_records) // 2)
            window_accuracies = []
            
            for i in range(len(labeled_records) - window_size + 1):
                window_records = labeled_records[i:i + window_size]
                window_acc = sum(1 for r in window_records if r['correct']) / len(window_records)
                window_accuracies.append(window_acc)
            
            volatility = np.std(window_accuracies) if len(window_accuracies) > 1 else 0.0
        else:
            volatility = 0.0
        
        return StrategyPerformance(
            strategy_name=strategy_name,
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1,
            win_rate=win_rate,
            avg_confidence=avg_confidence,
            consistency=consistency,
            recent_performance=recent_performance,
            trend=trend,
            volatility=volatility,
            timestamp=time.time()
        )
    
    def _default_performance(self, strategy_name: str) -> StrategyPerformance:
        """默认性能（数据不足时）"""
        return StrategyPerformance(
            strategy_name=strategy_name,
            accuracy=0.5,
            precision=0.5,
            recall=0.5,
            f1_score=0.5,
            win_rate=0.5,
            avg_confidence=0.6,
            consistency=0.7,
            recent_performance=0.5,
            trend=0.0,
            volatility=0.2,
            timestamp=time.time()
        )
    
    def get_all_strategy_performance(self) -> Dict[str, StrategyPerformance]:
        """获取所有策略的性能"""
        performance = {}
        for strategy_name in self.strategy_history.keys():
            performance[strategy_name] = self.calculate_strategy_performance(strategy_name)
        return performance
    
    def detect_performance_degradation(self, strategy_name: str, threshold: float = 0.1) -> bool:
        """检测性能退化"""
        if strategy_name not in self.strategy_history:
            return False
        
        performance = self.calculate_strategy_performance(strategy_name)
        
        # 检查最近性能是否显著低于历史平均
        if performance.recent_performance < performance.accuracy - threshold:
            return True
        
        # 检查趋势是否为负
        if performance.trend < -threshold:
            return True
        
        return False
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        all_performance = self.get_all_strategy_performance()
        
        if not all_performance:
            return {'total_strategies': 0, 'avg_accuracy': 0.5}
        
        accuracies = [p.accuracy for p in all_performance.values()]
        win_rates = [p.win_rate for p in all_performance.values()]
        trends = [p.trend for p in all_performance.values()]
        
        return {
            'total_strategies': len(all_performance),
            'avg_accuracy': np.mean(accuracies),
            'avg_win_rate': np.mean(win_rates),
            'avg_trend': np.mean(trends),
            'best_strategy': max(all_performance.keys(), key=lambda k: all_performance[k].accuracy),
            'worst_strategy': min(all_performance.keys(), key=lambda k: all_performance[k].accuracy),
            'degraded_strategies': [name for name, perf in all_performance.items() 
                                  if self.detect_performance_degradation(name)]
        }


class EnvironmentDetector:
    """环境检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('environment_detector', {})
        self.logger = logging.getLogger(__name__)
        
        # 环境历史
        self.market_data_history = deque(maxlen=self.config.get('max_history', 200))
        self.performance_history = deque(maxlen=self.config.get('max_history', 200))
        
        # 检测参数
        self.volatility_threshold = self.config.get('volatility_threshold', 0.3)
        self.trend_threshold = self.config.get('trend_threshold', 0.1)
        self.regime_change_threshold = self.config.get('regime_change_threshold', 0.2)
        
    def add_market_data(self, data: Dict[str, Any]):
        """添加市场数据"""
        self.market_data_history.append({
            'timestamp': time.time(),
            'features': data.get('features', {}),
            'prediction': data.get('prediction'),
            'confidence': data.get('confidence'),
            'actual': data.get('actual')
        })
    
    def add_performance_data(self, performance: Dict[str, float]):
        """添加性能数据"""
        self.performance_history.append({
            'timestamp': time.time(),
            'accuracy': performance.get('accuracy', 0.5),
            'win_rate': performance.get('win_rate', 0.5),
            'confidence': performance.get('confidence', 0.5)
        })
    
    def detect_environment_state(self) -> EnvironmentDetection:
        """检测环境状态"""
        if len(self.market_data_history) < 20:
            return self._default_detection()
        
        # 计算各种指标
        volatility = self._calculate_volatility()
        trend = self._calculate_trend()
        regime_change_prob = self._detect_regime_change()
        
        # 确定环境状态
        state = self._classify_environment_state(volatility, trend, regime_change_prob)
        
        # 检测变化点
        change_points = self._detect_change_points()
        
        # 生成建议
        recommendations = self._generate_recommendations(state, volatility, trend)
        
        return EnvironmentDetection(
            current_state=state,
            confidence=0.8,  # 简化的置信度
            change_points=change_points,
            trend_direction=trend,
            volatility_level=volatility,
            regime_probability={'stable': 1-regime_change_prob, 'changing': regime_change_prob},
            recommendations=recommendations,
            timestamp=time.time()
        )
    
    def _calculate_volatility(self) -> float:
        """计算波动率"""
        if len(self.performance_history) < 10:
            return 0.2
        
        # 使用性能数据计算波动率
        accuracies = [p['accuracy'] for p in list(self.performance_history)[-20:]]
        return np.std(accuracies)
    
    def _calculate_trend(self) -> float:
        """计算趋势"""
        if len(self.performance_history) < 10:
            return 0.0
        
        # 使用线性回归计算趋势
        recent_data = list(self.performance_history)[-20:]
        accuracies = [p['accuracy'] for p in recent_data]
        
        if len(accuracies) < 5:
            return 0.0
        
        # 简单的趋势计算：后半部分 - 前半部分
        mid_point = len(accuracies) // 2
        first_half = np.mean(accuracies[:mid_point])
        second_half = np.mean(accuracies[mid_point:])
        
        return second_half - first_half
    
    def _detect_regime_change(self) -> float:
        """检测制度变化"""
        if len(self.performance_history) < 30:
            return 0.1
        
        # 使用滑动窗口检测性能突变
        recent_data = list(self.performance_history)[-30:]
        accuracies = [p['accuracy'] for p in recent_data]
        
        # 计算前后两个窗口的差异
        window_size = len(accuracies) // 3
        if window_size < 3:
            return 0.1
        
        early_window = accuracies[:window_size]
        late_window = accuracies[-window_size:]
        
        early_mean = np.mean(early_window)
        late_mean = np.mean(late_window)
        
        # 如果差异超过阈值，认为可能发生制度变化
        change_magnitude = abs(late_mean - early_mean)
        return min(change_magnitude / self.regime_change_threshold, 1.0)
    
    def _classify_environment_state(self, volatility: float, trend: float, 
                                  regime_change_prob: float) -> EnvironmentState:
        """分类环境状态"""
        # 制度变化
        if regime_change_prob > 0.5:
            return EnvironmentState.REGIME_CHANGE
        
        # 高波动
        if volatility > self.volatility_threshold:
            return EnvironmentState.VOLATILE
        
        # 明显趋势
        if abs(trend) > self.trend_threshold:
            return EnvironmentState.TRENDING
        
        # 稳定状态
        return EnvironmentState.STABLE
    
    def _detect_change_points(self) -> List[int]:
        """检测变化点"""
        if len(self.performance_history) < 20:
            return []
        
        # 简化的变化点检测
        recent_data = list(self.performance_history)[-50:]
        accuracies = [p['accuracy'] for p in recent_data]
        
        change_points = []
        window_size = 5
        
        for i in range(window_size, len(accuracies) - window_size):
            before = np.mean(accuracies[i-window_size:i])
            after = np.mean(accuracies[i:i+window_size])
            
            if abs(after - before) > 0.15:  # 显著变化
                change_points.append(i)
        
        return change_points
    
    def _generate_recommendations(self, state: EnvironmentState, volatility: float, 
                                trend: float) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if state == EnvironmentState.VOLATILE:
            recommendations.extend([
                "增加风险控制权重",
                "降低策略权重的调整幅度",
                "提高决策阈值以减少噪音交易"
            ])
        
        elif state == EnvironmentState.TRENDING:
            if trend > 0:
                recommendations.extend([
                    "增加表现良好策略的权重",
                    "降低决策阈值以捕获更多机会"
                ])
            else:
                recommendations.extend([
                    "重新评估策略权重",
                    "考虑暂时降低整体仓位"
                ])
        
        elif state == EnvironmentState.REGIME_CHANGE:
            recommendations.extend([
                "重新训练所有模型",
                "重置策略权重到默认值",
                "增加观察期，减少调整频率"
            ])
        
        elif state == EnvironmentState.STABLE:
            recommendations.extend([
                "保持当前策略权重",
                "可以适度增加调整频率",
                "关注长期趋势变化"
            ])
        
        return recommendations
    
    def _default_detection(self) -> EnvironmentDetection:
        """默认检测结果"""
        return EnvironmentDetection(
            current_state=EnvironmentState.UNKNOWN,
            confidence=0.5,
            change_points=[],
            trend_direction=0.0,
            volatility_level=0.2,
            regime_probability={'stable': 0.7, 'changing': 0.3},
            recommendations=["需要更多数据进行环境分析"],
            timestamp=time.time()
        )


class WeightOptimizer:
    """权重优化器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('weight_optimizer', {})
        self.logger = logging.getLogger(__name__)
        
        # 优化参数
        self.learning_rate = self.config.get('learning_rate', 0.01)
        self.momentum = self.config.get('momentum', 0.9)
        self.max_weight_change = self.config.get('max_weight_change', 0.1)
        self.min_weight = self.config.get('min_weight', 0.05)
        self.max_weight = self.config.get('max_weight', 0.8)
        
        # 历史权重
        self.weight_history = deque(maxlen=100)
        self.gradient_history = defaultdict(lambda: deque(maxlen=10))
        
    def optimize_weights(self, current_weights: Dict[str, float], 
                        performance_data: Dict[str, StrategyPerformance],
                        method: TuningMethod = TuningMethod.PERFORMANCE_BASED) -> Dict[str, float]:
        """优化权重"""
        if method == TuningMethod.PERFORMANCE_BASED:
            return self._performance_based_optimization(current_weights, performance_data)
        elif method == TuningMethod.GRADIENT_DESCENT:
            return self._gradient_descent_optimization(current_weights, performance_data)
        elif method == TuningMethod.EVOLUTIONARY:
            return self._evolutionary_optimization(current_weights, performance_data)
        else:
            return self._performance_based_optimization(current_weights, performance_data)
    
    def _performance_based_optimization(self, current_weights: Dict[str, float],
                                      performance_data: Dict[str, StrategyPerformance]) -> Dict[str, float]:
        """基于性能的优化"""
        if not performance_data:
            return current_weights
        
        # 计算性能分数
        performance_scores = {}
        for strategy_name, perf in performance_data.items():
            # 综合性能分数：准确率 + 最近性能 + 趋势 - 波动率
            score = (perf.accuracy * 0.4 + 
                    perf.recent_performance * 0.3 + 
                    max(0, perf.trend) * 0.2 + 
                    perf.consistency * 0.1 - 
                    perf.volatility * 0.1)
            performance_scores[strategy_name] = max(0.1, score)
        
        # 归一化分数
        total_score = sum(performance_scores.values())
        if total_score > 0:
            normalized_scores = {k: v / total_score for k, v in performance_scores.items()}
        else:
            # 如果所有分数都是0，使用均匀分布
            normalized_scores = {k: 1.0 / len(performance_scores) 
                               for k in performance_scores.keys()}
        
        # 计算新权重（渐进式调整）
        new_weights = {}
        adjustment_rate = self.config.get('adjustment_rate', 0.2)
        
        for strategy_name in current_weights.keys():
            if strategy_name in normalized_scores:
                target_weight = normalized_scores[strategy_name]
                current_weight = current_weights[strategy_name]
                
                # 渐进式调整
                new_weight = current_weight + (target_weight - current_weight) * adjustment_rate
                
                # 应用约束
                new_weight = max(self.min_weight, min(self.max_weight, new_weight))
                
                # 限制单次调整幅度
                max_change = current_weight * self.max_weight_change
                if abs(new_weight - current_weight) > max_change:
                    if new_weight > current_weight:
                        new_weight = current_weight + max_change
                    else:
                        new_weight = current_weight - max_change
                
                new_weights[strategy_name] = new_weight
            else:
                new_weights[strategy_name] = current_weights[strategy_name]
        
        # 归一化权重
        total_weight = sum(new_weights.values())
        if total_weight > 0:
            new_weights = {k: v / total_weight for k, v in new_weights.items()}
        
        return new_weights
    
    def _gradient_descent_optimization(self, current_weights: Dict[str, float],
                                     performance_data: Dict[str, StrategyPerformance]) -> Dict[str, float]:
        """基于梯度下降的优化"""
        # 简化的梯度计算
        gradients = {}
        
        for strategy_name, perf in performance_data.items():
            if strategy_name in current_weights:
                # 基于性能趋势计算梯度
                gradient = perf.trend + (perf.recent_performance - perf.accuracy)
                gradients[strategy_name] = gradient
        
        # 更新权重
        new_weights = {}
        for strategy_name, current_weight in current_weights.items():
            if strategy_name in gradients:
                gradient = gradients[strategy_name]
                
                # 动量更新
                if strategy_name in self.gradient_history:
                    momentum_term = np.mean(list(self.gradient_history[strategy_name])) * self.momentum
                else:
                    momentum_term = 0
                
                # 权重更新
                weight_update = self.learning_rate * (gradient + momentum_term)
                new_weight = current_weight + weight_update
                
                # 应用约束
                new_weight = max(self.min_weight, min(self.max_weight, new_weight))
                new_weights[strategy_name] = new_weight
                
                # 记录梯度历史
                self.gradient_history[strategy_name].append(gradient)
            else:
                new_weights[strategy_name] = current_weight
        
        # 归一化权重
        total_weight = sum(new_weights.values())
        if total_weight > 0:
            new_weights = {k: v / total_weight for k, v in new_weights.items()}
        
        return new_weights
    
    def _evolutionary_optimization(self, current_weights: Dict[str, float],
                                 performance_data: Dict[str, StrategyPerformance]) -> Dict[str, float]:
        """基于进化算法的优化"""
        # 简化的进化算法
        strategy_names = list(current_weights.keys())
        
        # 生成候选权重
        candidates = []
        for _ in range(10):  # 生成10个候选
            candidate = {}
            for name in strategy_names:
                # 在当前权重附近随机变化
                noise = np.random.normal(0, 0.05)
                new_weight = current_weights[name] + noise
                new_weight = max(self.min_weight, min(self.max_weight, new_weight))
                candidate[name] = new_weight
            
            # 归一化
            total = sum(candidate.values())
            if total > 0:
                candidate = {k: v / total for k, v in candidate.items()}
            
            candidates.append(candidate)
        
        # 评估候选权重
        best_candidate = current_weights
        best_score = self._evaluate_weights(current_weights, performance_data)
        
        for candidate in candidates:
            score = self._evaluate_weights(candidate, performance_data)
            if score > best_score:
                best_score = score
                best_candidate = candidate
        
        return best_candidate
    
    def _evaluate_weights(self, weights: Dict[str, float], 
                         performance_data: Dict[str, StrategyPerformance]) -> float:
        """评估权重质量"""
        total_score = 0.0
        total_weight = 0.0
        
        for strategy_name, weight in weights.items():
            if strategy_name in performance_data:
                perf = performance_data[strategy_name]
                strategy_score = (perf.accuracy * 0.4 + 
                                perf.recent_performance * 0.3 + 
                                max(0, perf.trend) * 0.2 + 
                                perf.consistency * 0.1)
                total_score += weight * strategy_score
                total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else 0.0


class ThresholdOptimizer:
    """阈值优化器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('threshold_optimizer', {})
        self.logger = logging.getLogger(__name__)

        # 阈值历史
        self.threshold_history = deque(maxlen=100)
        self.performance_history = deque(maxlen=100)

        # 优化参数
        self.min_threshold = self.config.get('min_threshold', 0.3)
        self.max_threshold = self.config.get('max_threshold', 0.9)
        self.threshold_step = self.config.get('threshold_step', 0.05)

    def optimize_thresholds(self, current_thresholds: Dict[str, float],
                          performance_data: Dict[str, Any],
                          environment_state: EnvironmentState) -> Dict[str, float]:
        """优化决策阈值"""
        new_thresholds = current_thresholds.copy()

        # 根据环境状态调整策略
        if environment_state == EnvironmentState.VOLATILE:
            # 波动环境：提高阈值，减少噪音交易
            new_thresholds = self._increase_thresholds(new_thresholds, 0.05)

        elif environment_state == EnvironmentState.TRENDING:
            # 趋势环境：降低阈值，捕获更多机会
            new_thresholds = self._decrease_thresholds(new_thresholds, 0.03)

        elif environment_state == EnvironmentState.REGIME_CHANGE:
            # 制度变化：使用保守阈值
            new_thresholds = self._reset_to_conservative_thresholds(new_thresholds)

        # 基于性能微调
        new_thresholds = self._performance_based_adjustment(new_thresholds, performance_data)

        return new_thresholds

    def _increase_thresholds(self, thresholds: Dict[str, float], increase: float) -> Dict[str, float]:
        """增加阈值"""
        new_thresholds = {}
        for key, value in thresholds.items():
            new_value = min(self.max_threshold, value + increase)
            new_thresholds[key] = new_value
        return new_thresholds

    def _decrease_thresholds(self, thresholds: Dict[str, float], decrease: float) -> Dict[str, float]:
        """降低阈值"""
        new_thresholds = {}
        for key, value in thresholds.items():
            new_value = max(self.min_threshold, value - decrease)
            new_thresholds[key] = new_value
        return new_thresholds

    def _reset_to_conservative_thresholds(self, thresholds: Dict[str, float]) -> Dict[str, float]:
        """重置为保守阈值"""
        conservative_values = {
            'confidence_threshold': 0.7,
            'consensus_threshold': 0.6,
            'risk_threshold': 0.3
        }

        new_thresholds = {}
        for key, value in thresholds.items():
            if key in conservative_values:
                new_thresholds[key] = conservative_values[key]
            else:
                new_thresholds[key] = min(value + 0.1, self.max_threshold)

        return new_thresholds

    def _performance_based_adjustment(self, thresholds: Dict[str, float],
                                    performance_data: Dict[str, Any]) -> Dict[str, float]:
        """基于性能的微调"""
        if not performance_data:
            return thresholds

        # 获取最近的准确率和决策频率
        recent_accuracy = performance_data.get('recent_accuracy', 0.5)
        decision_frequency = performance_data.get('decision_frequency', 0.5)

        new_thresholds = thresholds.copy()

        # 如果准确率低，提高阈值
        if recent_accuracy < 0.45:
            new_thresholds = self._increase_thresholds(new_thresholds, 0.02)

        # 如果准确率高但决策频率低，降低阈值
        elif recent_accuracy > 0.6 and decision_frequency < 0.3:
            new_thresholds = self._decrease_thresholds(new_thresholds, 0.02)

        return new_thresholds


class AdaptiveStrategyTuner:
    """自适应策略调优器主类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化自适应策略调优器

        Args:
            config: 配置字典
        """
        self.config = config.get('adaptive_strategy_tuning', {})
        self.logger = logging.getLogger(__name__)

        # 初始化各个组件
        self.performance_tracker = PerformanceTracker(self.config)
        self.environment_detector = EnvironmentDetector(self.config)
        self.weight_optimizer = WeightOptimizer(self.config)
        self.threshold_optimizer = ThresholdOptimizer(self.config)

        # 调优历史
        self.tuning_history = deque(maxlen=self.config.get('max_tuning_history', 100))

        # 调优参数
        self.tuning_frequency = self.config.get('tuning_frequency', 50)  # 每50个决策调优一次
        self.min_data_for_tuning = self.config.get('min_data_for_tuning', 20)
        self.decision_count = 0

        # 当前状态
        self.current_weights = {}
        self.current_thresholds = {}
        self.last_tuning_time = 0

        self.logger.info("自适应策略调优器初始化完成")

    def record_decision(self, decision_data: Dict[str, Any]):
        """记录决策数据"""
        self.decision_count += 1

        # 记录系统决策
        self.performance_tracker.record_system_decision(decision_data)

        # 记录各策略决策
        strategy_outputs = decision_data.get('strategy_outputs', {})
        for strategy_name, output in strategy_outputs.items():
            if hasattr(output, 'prediction') and hasattr(output, 'confidence'):
                self.performance_tracker.record_strategy_decision(
                    strategy_name,
                    output.prediction,
                    output.confidence,
                    decision_data.get('actual')
                )

        # 记录市场数据
        self.environment_detector.add_market_data(decision_data)

        # 记录性能数据
        if 'performance' in decision_data:
            self.environment_detector.add_performance_data(decision_data['performance'])

    def should_tune(self) -> bool:
        """判断是否应该进行调优"""
        # 检查决策数量
        if self.decision_count < self.min_data_for_tuning:
            return False

        # 检查调优频率
        if self.decision_count % self.tuning_frequency != 0:
            return False

        # 检查时间间隔
        current_time = time.time()
        min_interval = self.config.get('min_tuning_interval', 300)  # 5分钟
        if current_time - self.last_tuning_time < min_interval:
            return False

        return True

    def perform_tuning(self, current_weights: Dict[str, float],
                      current_thresholds: Dict[str, float],
                      tuning_method: TuningMethod = TuningMethod.PERFORMANCE_BASED) -> TuningResult:
        """执行调优"""
        tuning_id = f"tuning_{int(time.time())}"

        try:
            # 获取性能数据
            performance_data = self.performance_tracker.get_all_strategy_performance()

            # 检测环境状态
            environment_detection = self.environment_detector.detect_environment_state()

            # 优化权重
            new_weights = self.weight_optimizer.optimize_weights(
                current_weights, performance_data, tuning_method
            )

            # 优化阈值
            performance_summary = self.performance_tracker.get_performance_summary()
            new_thresholds = self.threshold_optimizer.optimize_thresholds(
                current_thresholds, performance_summary, environment_detection.current_state
            )

            # 计算性能改进
            old_performance = self._evaluate_configuration(current_weights, current_thresholds, performance_data)
            new_performance = self._evaluate_configuration(new_weights, new_thresholds, performance_data)
            performance_improvement = new_performance - old_performance

            # 生成推理
            reasoning = self._generate_tuning_reasoning(
                environment_detection, performance_data, performance_improvement
            )

            # 创建调优结果
            tuning_result = TuningResult(
                tuning_id=tuning_id,
                method=tuning_method,
                old_weights=current_weights.copy(),
                new_weights=new_weights,
                old_thresholds=current_thresholds.copy(),
                new_thresholds=new_thresholds,
                performance_improvement=performance_improvement,
                confidence=0.8,  # 简化的置信度
                reasoning=reasoning,
                timestamp=time.time()
            )

            # 记录调优历史
            self.tuning_history.append(tuning_result)
            self.last_tuning_time = time.time()

            # 更新当前状态
            self.current_weights = new_weights
            self.current_thresholds = new_thresholds

            self.logger.info(f"调优完成: {tuning_id}, 性能改进: {performance_improvement:.3f}")

            return tuning_result

        except Exception as e:
            self.logger.error(f"调优失败: {str(e)}")
            # 返回无变化的结果
            return TuningResult(
                tuning_id=tuning_id,
                method=tuning_method,
                old_weights=current_weights,
                new_weights=current_weights,
                old_thresholds=current_thresholds,
                new_thresholds=current_thresholds,
                performance_improvement=0.0,
                confidence=0.0,
                reasoning=f"调优失败: {str(e)}",
                timestamp=time.time()
            )

    def _evaluate_configuration(self, weights: Dict[str, float], thresholds: Dict[str, float],
                              performance_data: Dict[str, StrategyPerformance]) -> float:
        """评估配置质量"""
        if not performance_data:
            return 0.5

        # 计算加权性能
        total_score = 0.0
        total_weight = 0.0

        for strategy_name, weight in weights.items():
            if strategy_name in performance_data:
                perf = performance_data[strategy_name]
                strategy_score = (perf.accuracy * 0.4 +
                                perf.recent_performance * 0.3 +
                                perf.consistency * 0.2 +
                                max(0, perf.trend) * 0.1)
                total_score += weight * strategy_score
                total_weight += weight

        base_score = total_score / total_weight if total_weight > 0 else 0.5

        # 阈值调整（简化）
        threshold_penalty = 0.0
        for threshold_value in thresholds.values():
            if threshold_value > 0.8:  # 过高的阈值可能错过机会
                threshold_penalty += 0.05
            elif threshold_value < 0.4:  # 过低的阈值可能增加噪音
                threshold_penalty += 0.03

        return max(0.0, base_score - threshold_penalty)

    def _generate_tuning_reasoning(self, environment_detection: EnvironmentDetection,
                                 performance_data: Dict[str, StrategyPerformance],
                                 performance_improvement: float) -> str:
        """生成调优推理"""
        reasoning_parts = []

        # 环境分析
        reasoning_parts.append(f"环境状态: {environment_detection.current_state.value}")
        reasoning_parts.append(f"波动率: {environment_detection.volatility_level:.3f}")
        reasoning_parts.append(f"趋势: {environment_detection.trend_direction:.3f}")

        # 性能分析
        if performance_data:
            best_strategy = max(performance_data.keys(),
                              key=lambda k: performance_data[k].accuracy)
            worst_strategy = min(performance_data.keys(),
                               key=lambda k: performance_data[k].accuracy)

            reasoning_parts.append(f"最佳策略: {best_strategy} (准确率: {performance_data[best_strategy].accuracy:.3f})")
            reasoning_parts.append(f"最差策略: {worst_strategy} (准确率: {performance_data[worst_strategy].accuracy:.3f})")

        # 调优效果
        if performance_improvement > 0.01:
            reasoning_parts.append(f"预期性能改进: +{performance_improvement:.3f}")
        elif performance_improvement < -0.01:
            reasoning_parts.append(f"预期性能下降: {performance_improvement:.3f}")
        else:
            reasoning_parts.append("性能基本保持不变")

        # 建议
        reasoning_parts.extend(environment_detection.recommendations[:2])

        return "; ".join(reasoning_parts)

    def get_tuning_summary(self) -> Dict[str, Any]:
        """获取调优总结"""
        if not self.tuning_history:
            return {
                'total_tunings': 0,
                'avg_improvement': 0.0,
                'last_tuning': None
            }

        recent_tunings = list(self.tuning_history)[-10:]
        improvements = [t.performance_improvement for t in recent_tunings]

        return {
            'total_tunings': len(self.tuning_history),
            'recent_tunings': len(recent_tunings),
            'avg_improvement': np.mean(improvements),
            'total_improvement': sum(improvements),
            'last_tuning': recent_tunings[-1].timestamp if recent_tunings else None,
            'successful_tunings': sum(1 for imp in improvements if imp > 0.01),
            'current_weights': self.current_weights,
            'current_thresholds': self.current_thresholds
        }

    def get_environment_status(self) -> Dict[str, Any]:
        """获取环境状态"""
        detection = self.environment_detector.detect_environment_state()
        performance_summary = self.performance_tracker.get_performance_summary()

        return {
            'environment_state': detection.current_state.value,
            'volatility_level': detection.volatility_level,
            'trend_direction': detection.trend_direction,
            'regime_change_probability': detection.regime_probability.get('changing', 0.0),
            'recommendations': detection.recommendations,
            'performance_summary': performance_summary,
            'degraded_strategies': performance_summary.get('degraded_strategies', [])
        }

    def force_reset(self):
        """强制重置调优器"""
        self.logger.info("强制重置自适应策略调优器")

        # 清空历史数据
        self.performance_tracker.strategy_history.clear()
        self.performance_tracker.decision_history.clear()
        self.environment_detector.market_data_history.clear()
        self.environment_detector.performance_history.clear()
        self.tuning_history.clear()

        # 重置计数器
        self.decision_count = 0
        self.last_tuning_time = 0

        # 清空当前状态
        self.current_weights.clear()
        self.current_thresholds.clear()
