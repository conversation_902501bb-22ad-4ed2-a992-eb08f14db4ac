#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型优化功能

验证超参数调优、特征选择等模型优化技术
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from core.model_optimization import ModelOptimizationManager, HyperparameterOptimizer, FeatureSelector
    from main import SimpleFusionV8
    print("✅ 成功导入模型优化模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def create_mock_training_data(num_samples: int = 200):
    """创建模拟训练数据"""
    training_data = []
    
    for i in range(num_samples):
        # 创建特征
        features = {
            'consensus_ratio': 0.3 + np.random.random() * 0.4,
            'weighted_consensus': 0.2 + np.random.random() * 0.6,
            'avg_confidence': 0.4 + np.random.random() * 0.4,
            'min_confidence': 0.3 + np.random.random() * 0.3,
            'max_confidence': 0.6 + np.random.random() * 0.3,
            'confidence_std': np.random.random() * 0.2,
            'total_divergence': np.random.random() * 2,
            'max_divergence': np.random.random() * 1,
            'strategy_1_prediction': np.random.choice([0, 1]),
            'strategy_2_prediction': np.random.choice([0, 1]),
            'strategy_6_prediction': np.random.choice([0, 1]),
            'strategy_1_confidence': 0.4 + np.random.random() * 0.4,
            'strategy_2_confidence': 0.4 + np.random.random() * 0.4,
            'strategy_6_confidence': 0.4 + np.random.random() * 0.4,
            'win_rate_10': 0.3 + np.random.random() * 0.4,
            'win_rate_5': 0.3 + np.random.random() * 0.4,
            'current_streak': np.random.randint(-5, 6),
            'strategy_1_weight': 0.2 + np.random.random() * 0.3,
            'strategy_2_weight': 0.2 + np.random.random() * 0.3,
            'strategy_6_weight': 0.2 + np.random.random() * 0.3,
            # 添加一些噪声特征
            'noise_feature_1': np.random.random(),
            'noise_feature_2': np.random.random(),
            'noise_feature_3': np.random.random()
        }
        
        # 生成标签（基于部分特征的逻辑）
        signal = (features['consensus_ratio'] * 0.4 + 
                 features['avg_confidence'] * 0.3 + 
                 features['win_rate_10'] * 0.3)
        
        actual_result = 1 if signal + np.random.normal(0, 0.1) > 0.5 else 0
        
        training_data.append({
            'features': features,
            'actual_result': actual_result,
            'timestamp': time.time() - (num_samples - i) * 10
        })
    
    return training_data


def create_mock_model():
    """创建模拟模型"""
    class MockModel:
        def __init__(self, name):
            self.name = name
            self.n_estimators = 100
            self.max_depth = 6
            self.learning_rate = 0.1
            
        def fit(self, X, y):
            # 模拟训练过程
            time.sleep(0.01)
            return self
            
        def score(self, X, y):
            # 模拟评分，基于参数返回不同的分数
            base_score = 0.6
            param_bonus = (self.n_estimators / 200 + 
                          (10 - self.max_depth) / 20 + 
                          self.learning_rate / 2) * 0.1
            return min(0.95, base_score + param_bonus + np.random.normal(0, 0.02))
    
    return MockModel("test_model")


def test_hyperparameter_optimizer():
    """测试超参数优化器"""
    print("\n🧪 测试超参数优化器...")
    
    # 创建配置
    config = {
        'hyperparameter_optimization': {
            'method': 'grid_search',
            'cv_folds': 3,
            'max_iterations': 10
        }
    }
    
    # 创建优化器
    optimizer = HyperparameterOptimizer(config)
    
    # 创建模拟数据
    training_data = create_mock_training_data(100)
    X = np.random.randn(100, 10)
    y = np.random.choice([0, 1], 100)
    
    # 创建模拟模型
    model = create_mock_model()
    
    # 定义参数网格
    param_grid = {
        'n_estimators': [50, 100, 200],
        'max_depth': [3, 6, 9],
        'learning_rate': [0.01, 0.1, 0.2]
    }
    
    print(f"📊 开始超参数优化...")
    print(f"   参数网格: {param_grid}")
    
    # 执行优化
    result = optimizer.optimize_model(model, X, y, param_grid)
    
    print(f"✅ 超参数优化完成:")
    print(f"   优化方法: {result.method}")
    print(f"   最佳参数: {result.best_params}")
    print(f"   最佳得分: {result.best_score:.4f}")
    print(f"   性能提升: {result.improvement:.4f}")
    print(f"   优化时间: {result.optimization_time:.2f}秒")
    
    # 获取优化总结
    summary = optimizer.get_optimization_summary()
    print(f"   总优化次数: {summary['total_optimizations']}")
    
    return optimizer, result


def test_feature_selector():
    """测试特征选择器"""
    print("\n🧪 测试特征选择器...")
    
    # 创建配置
    config = {
        'feature_selection': {
            'methods': ['univariate', 'rfe'],
            'k_features': 10
        }
    }
    
    # 创建特征选择器
    selector = FeatureSelector(config)
    
    # 创建模拟数据
    training_data = create_mock_training_data(150)
    
    # 准备数据
    feature_names = list(training_data[0]['features'].keys())
    X = np.array([[data['features'][name] for name in feature_names] for data in training_data])
    y = np.array([data['actual_result'] for data in training_data])
    
    print(f"📊 开始特征选择...")
    print(f"   原始特征数: {len(feature_names)}")
    print(f"   目标特征数: {config['feature_selection']['k_features']}")
    
    # 执行特征选择
    X_selected, feature_importances = selector.select_features(X, y, feature_names)
    
    print(f"✅ 特征选择完成:")
    print(f"   选择后特征数: {X_selected.shape[1]}")
    print(f"   特征重要性排序 (前10):")
    
    for i, importance in enumerate(feature_importances[:10]):
        print(f"     {i+1}. {importance.feature_name}: {importance.importance_score:.4f} ({importance.selection_method})")
    
    # 获取特征选择总结
    summary = selector.get_feature_selection_summary()
    print(f"   总选择次数: {summary['total_selections']}")
    
    return selector, feature_importances


def test_model_optimization_manager():
    """测试模型优化管理器"""
    print("\n🧪 测试模型优化管理器...")
    
    # 创建配置
    config = {
        'model_optimization': {
            'strategy': 'comprehensive',
            'frequency': 50
        },
        'hyperparameter_optimization': {
            'method': 'grid_search',
            'cv_folds': 3
        },
        'feature_selection': {
            'methods': ['univariate'],
            'k_features': 8
        }
    }
    
    # 创建优化管理器
    manager = ModelOptimizationManager(config)
    
    # 创建模拟模型
    models = {
        'model_1': create_mock_model(),
        'model_2': create_mock_model(),
        'model_3': create_mock_model()
    }
    
    # 创建训练数据
    training_data = create_mock_training_data(100)
    
    print(f"📊 开始综合模型优化...")
    print(f"   优化策略: {config['model_optimization']['strategy']}")
    print(f"   模型数量: {len(models)}")
    print(f"   训练数据: {len(training_data)} 个样本")
    
    # 执行优化
    optimization_results = manager.optimize_models(models, training_data)
    
    print(f"✅ 模型优化完成:")
    print(f"   优化组件数: {len(optimization_results)}")
    
    for name, result in optimization_results.items():
        print(f"   {name}:")
        print(f"     方法: {result.method}")
        print(f"     最佳得分: {result.best_score:.4f}")
        print(f"     性能提升: {result.improvement:.4f}")
        print(f"     优化时间: {result.optimization_time:.2f}秒")
    
    # 测试优化触发逻辑
    print(f"\n📊 测试优化触发逻辑...")
    
    # 模拟性能历史
    for i in range(30):
        # 模拟性能下降
        performance = 0.8 - i * 0.01 + np.random.normal(0, 0.02)
        manager.record_performance(performance)
    
    should_optimize = manager.should_optimize(100)
    print(f"   应该优化: {should_optimize}")
    
    # 获取优化状态
    status = manager.get_optimization_status()
    print(f"   优化状态: {status}")
    
    return manager, optimization_results


def test_v8_integration():
    """测试V8系统集成"""
    print("\n🧪 测试V8系统模型优化集成...")
    
    # 创建V8系统
    system = SimpleFusionV8()
    system.initialize()
    
    print("✅ V8系统初始化完成")
    
    # 进行多次决策以触发优化
    results = []
    
    for i in range(120):  # 超过优化频率阈值
        try:
            # 模拟外部数据
            external_data = {
                'decision_id': f"optimization_test_{i}",
                'current_boot': {'boot_id': 400 + i, 'position': i % 40}
            }
            
            # 处理决策
            decision = system.process_decision(external_data)
            
            # 模拟实际结果
            actual_result = np.random.choice([0, 1])
            
            # 更新反馈
            system.update_feedback(decision.decision_id, actual_result)
            
            results.append({
                'decision_id': decision.decision_id,
                'prediction': decision.prediction,
                'actual': actual_result,
                'correct': decision.prediction == actual_result
            })
            
            # 定期显示状态
            if i % 50 == 0 and i > 0:
                status = system.get_system_status()
                if 'model_optimization' in status:
                    opt_status = status['model_optimization']
                    print(f"   决策 {i+1}: 优化策略={opt_status['optimization_strategy']}, "
                          f"应该优化={opt_status.get('should_optimize_next', False)}")
            
        except Exception as e:
            print(f"   ❌ 决策 {i+1} 失败: {str(e)}")
    
    # 获取最终系统状态
    final_status = system.get_system_status()
    
    print(f"✅ V8模型优化集成测试完成:")
    print(f"   总决策数: {len(results)}")
    
    if results:
        accuracy = sum(r['correct'] for r in results) / len(results)
        print(f"   准确率: {accuracy:.4f}")
    
    if 'model_optimization' in final_status:
        opt_status = final_status['model_optimization']
        print(f"   优化状态:")
        print(f"     策略: {opt_status['optimization_strategy']}")
        print(f"     频率: {opt_status['optimization_frequency']}")
        print(f"     最后优化: {opt_status['last_optimization']}")
        
        if 'hyperparameter_optimization' in opt_status:
            hp_status = opt_status['hyperparameter_optimization']
            print(f"     超参数优化次数: {hp_status.get('total_optimizations', 0)}")
        
        if 'feature_selection' in opt_status:
            fs_status = opt_status['feature_selection']
            print(f"     特征选择次数: {fs_status.get('total_selections', 0)}")
    
    return system, results


def main():
    """主测试函数"""
    print("🚀 模型优化功能测试")
    print("=" * 60)
    
    # 1. 测试超参数优化器
    optimizer, hp_result = test_hyperparameter_optimizer()
    
    # 2. 测试特征选择器
    selector, feature_importances = test_feature_selector()
    
    # 3. 测试模型优化管理器
    manager, optimization_results = test_model_optimization_manager()
    
    # 4. 测试V8系统集成
    system, v8_results = test_v8_integration()
    
    print("\n" + "=" * 60)
    print("🎉 模型优化功能测试完成！")
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"   ✅ 超参数优化: 性能提升 {hp_result.improvement:.4f}")
    print(f"   ✅ 特征选择: 选择了 {len([f for f in feature_importances if f.importance_score > 0.1])} 个重要特征")
    print(f"   ✅ 综合优化: 优化了 {len(optimization_results)} 个组件")
    
    if v8_results:
        v8_accuracy = sum(r['correct'] for r in v8_results) / len(v8_results)
        print(f"   ✅ V8系统集成: 准确率 {v8_accuracy:.4f}")
    
    # 性能对比
    if optimization_results:
        total_improvement = sum(r.improvement for r in optimization_results.values() if r.improvement > 0)
        print(f"   🏆 总性能提升: {total_improvement:.4f}")
    
    return {
        'optimizer': optimizer,
        'selector': selector,
        'manager': manager,
        'system': system,
        'results': {
            'hyperparameter': hp_result,
            'feature_selection': feature_importances,
            'optimization': optimization_results,
            'v8_results': v8_results
        }
    }


if __name__ == "__main__":
    test_results = main()
