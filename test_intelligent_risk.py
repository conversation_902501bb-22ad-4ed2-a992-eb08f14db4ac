#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能风险管理功能

验证动态风险评估、实时风险监控、自适应风险阈值、风险预警机制等功能
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from core.intelligent_risk_management import (
        IntelligentRiskManager, RiskMonitor, DynamicRiskAssessor,
        RiskLevel, AlertType, RiskMetrics, RiskAlert, RiskThresholds
    )
    from main import SimpleFusionV8
    print("✅ 成功导入智能风险管理模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_risk_calculator():
    """测试风险计算器"""
    print("\n🧪 测试风险计算器...")
    
    from core.intelligent_risk_management import RiskCalculator
    
    calculator = RiskCalculator()
    
    # 创建测试数据
    returns = [0.02, -0.01, 0.03, -0.02, 0.01, -0.03, 0.02, -0.01, 0.01, 0.02]
    results = [True, False, True, False, True, False, True, False, True, True]
    
    # 测试各项指标计算
    volatility = calculator.calculate_volatility(returns)
    max_drawdown = calculator.calculate_max_drawdown(returns)
    var_95 = calculator.calculate_var(returns, 0.95)
    sharpe_ratio = calculator.calculate_sharpe_ratio(returns)
    sortino_ratio = calculator.calculate_sortino_ratio(returns)
    calmar_ratio = calculator.calculate_calmar_ratio(returns)
    consecutive_losses = calculator.calculate_consecutive_losses(results)
    win_rate = calculator.calculate_win_rate(results)
    
    print(f"   波动率: {volatility:.4f}")
    print(f"   最大回撤: {max_drawdown:.4f}")
    print(f"   VaR(95%): {var_95:.4f}")
    print(f"   夏普比率: {sharpe_ratio:.4f}")
    print(f"   索提诺比率: {sortino_ratio:.4f}")
    print(f"   卡玛比率: {calmar_ratio:.4f}")
    print(f"   连续亏损: {consecutive_losses}")
    print(f"   胜率: {win_rate:.4f}")
    
    print("✅ 风险计算器测试完成")
    return calculator


def test_dynamic_risk_assessor():
    """测试动态风险评估器"""
    print("\n🧪 测试动态风险评估器...")
    
    config = {
        'risk_thresholds': {
            'volatility_threshold': 0.3,
            'max_drawdown_threshold': 0.15,
            'consecutive_losses_threshold': 5,
            'win_rate_threshold': 0.4,
            'risk_score_threshold': 0.7
        }
    }
    
    assessor = DynamicRiskAssessor(config)
    
    # 模拟历史数据
    for i in range(50):
        return_value = np.random.normal(0.01, 0.05)
        is_correct = np.random.choice([True, False], p=[0.6, 0.4])
        confidence = np.random.uniform(0.4, 0.9)
        prediction = np.random.uniform(0.3, 0.8)
        
        assessor.update_data(return_value, is_correct, confidence, prediction)
    
    # 评估当前风险
    risk_metrics = assessor.assess_risk({})
    risk_level = assessor.get_risk_level(risk_metrics.risk_score)
    
    print(f"   风险评分: {risk_metrics.risk_score:.4f}")
    print(f"   风险等级: {risk_level.value}")
    print(f"   波动率: {risk_metrics.volatility:.4f}")
    print(f"   最大回撤: {risk_metrics.max_drawdown:.4f}")
    print(f"   连续亏损: {risk_metrics.consecutive_losses}")
    print(f"   胜率: {risk_metrics.win_rate:.4f}")
    print(f"   夏普比率: {risk_metrics.sharpe_ratio:.4f}")
    
    # 测试异常检测
    anomaly_features = {
        'return_value': -0.1,  # 异常大的损失
        'confidence': 0.9,
        'prediction': 0.8
    }
    
    is_anomaly = assessor.detect_anomaly(anomaly_features)
    print(f"   异常检测: {'检测到异常' if is_anomaly else '正常'}")
    
    print("✅ 动态风险评估器测试完成")
    return assessor, risk_metrics


def test_risk_monitor():
    """测试风险监控器"""
    print("\n🧪 测试风险监控器...")
    
    config = {
        'assessment_frequency': 5,
        'risk_thresholds': {
            'volatility_threshold': 0.25,
            'max_drawdown_threshold': 0.12,
            'consecutive_losses_threshold': 4,
            'win_rate_threshold': 0.45,
            'risk_score_threshold': 0.6
        }
    }
    
    monitor = RiskMonitor(config)
    
    # 模拟决策监控
    alerts_generated = []
    
    for i in range(30):
        decision_data = {
            'decision_id': f"test_decision_{i}",
            'prediction': np.random.choice([0, 1]),
            'confidence': np.random.uniform(0.3, 0.8),
            'decision_count': i
        }
        
        # 模拟实际结果
        actual_result = np.random.choice([0, 1])
        
        # 监控决策
        alerts = monitor.monitor_decision(decision_data, actual_result)
        alerts_generated.extend(alerts)
        
        if alerts:
            print(f"   决策 {i}: 产生 {len(alerts)} 个预警")
            for alert in alerts:
                print(f"     - {alert.alert_type.value}: {alert.message}")
    
    # 获取风险状态
    risk_status = monitor.get_current_risk_status()
    active_alerts = monitor.get_active_alerts()
    
    print(f"   总预警数: {len(alerts_generated)}")
    print(f"   活跃预警数: {len(active_alerts)}")
    print(f"   当前风险等级: {risk_status['risk_level']}")
    print(f"   当前风险评分: {risk_status['risk_score']:.4f}")
    print(f"   监控状态: {'活跃' if risk_status['monitoring_active'] else '非活跃'}")
    
    print("✅ 风险监控器测试完成")
    return monitor, alerts_generated


def test_intelligent_risk_manager():
    """测试智能风险管理器"""
    print("\n🧪 测试智能风险管理器...")
    
    config = {
        'intelligent_risk': {
            'daily_risk_budget': 0.05,
            'assessment_frequency': 8,
            'risk_thresholds': {
                'volatility_threshold': 0.3,
                'max_drawdown_threshold': 0.15,
                'risk_score_threshold': 0.65
            }
        }
    }
    
    manager = IntelligentRiskManager(config)
    
    # 测试决策风险评估
    decision_data = {
        'prediction': 1,
        'confidence': 0.7,
        'decision_count': 10
    }
    
    risk_evaluation = manager.evaluate_decision_risk(decision_data)
    
    print(f"   决策风险评分: {risk_evaluation['decision_risk_score']:.4f}")
    print(f"   当前风险等级: {risk_evaluation['current_risk_level']}")
    print(f"   是否拦截决策: {risk_evaluation['should_block_decision']}")
    print(f"   仓位调整系数: {risk_evaluation['position_adjustment']:.4f}")
    print(f"   风险预算使用: {risk_evaluation['risk_budget_usage']:.2%}")
    print(f"   紧急模式: {risk_evaluation['emergency_mode']}")
    
    if risk_evaluation['recommendations']:
        print(f"   风险建议:")
        for rec in risk_evaluation['recommendations']:
            print(f"     - {rec}")
    
    # 模拟决策反馈处理
    print(f"\n   模拟决策反馈处理...")
    
    for i in range(20):
        decision_id = f"risk_test_{i}"
        decision_data = {
            'prediction': np.random.choice([0, 1]),
            'confidence': np.random.uniform(0.4, 0.8),
            'risk_level': np.random.choice(['low', 'medium', 'high']),
            'position_size': np.random.uniform(0.5, 1.5),
            'risk_score': np.random.uniform(0.3, 0.8)
        }
        
        actual_result = np.random.choice([0, 1])
        
        # 处理反馈
        alerts = manager.process_decision_feedback(decision_id, decision_data, actual_result)
        
        if alerts:
            print(f"     决策 {i}: 产生 {len(alerts)} 个风险预警")
            for alert in alerts:
                print(f"       {alert.alert_type.value}: {alert.message}")
    
    # 获取风险管理状态
    risk_status = manager.get_risk_management_status()
    
    print(f"\n   风险管理状态:")
    print(f"     风险控制激活: {risk_status['risk_control_active']}")
    print(f"     紧急模式: {risk_status['emergency_mode']}")
    print(f"     仓位缩放因子: {risk_status['position_scaling_factor']:.4f}")
    print(f"     拦截决策数: {risk_status['blocked_decisions_count']}")
    print(f"     风险预算使用: {risk_status['risk_budget']['usage_percentage']:.2f}%")
    print(f"     平均收益: {risk_status['performance']['avg_return']:.4f}")
    print(f"     活跃预警数: {len(risk_status['active_alerts'])}")
    
    print("✅ 智能风险管理器测试完成")
    return manager, risk_status


def test_v8_integration():
    """测试V8系统集成"""
    print("\n🧪 测试V8系统智能风险管理集成...")
    
    # 创建V8系统
    system = SimpleFusionV8()
    system.initialize()
    
    print("✅ V8系统初始化完成")
    
    # 进行多次决策以测试风险管理
    results = []
    risk_alerts_count = 0
    blocked_decisions = 0
    
    for i in range(100):
        try:
            # 模拟外部数据
            external_data = {
                'decision_id': f"risk_integration_test_{i}",
                'current_boot': {'boot_id': 600 + i, 'position': i % 40}
            }
            
            # 处理决策
            decision = system.process_decision(external_data)
            
            # 检查是否被风险管理拦截
            if decision.risk_level == 'blocked':
                blocked_decisions += 1
                print(f"   决策 {i+1}: 被风险管理系统拦截")
            
            # 模拟实际结果
            actual_result = np.random.choice([0, 1])
            
            # 更新反馈
            system.update_feedback(decision.decision_id, actual_result)
            
            results.append({
                'decision_id': decision.decision_id,
                'prediction': decision.prediction,
                'actual': actual_result,
                'correct': decision.prediction == actual_result,
                'confidence': decision.confidence,
                'risk_level': decision.risk_level,
                'blocked': decision.risk_level == 'blocked'
            })
            
            # 定期显示风险状态
            if i % 25 == 0 and i > 0:
                status = system.get_system_status()
                if 'intelligent_risk_management' in status:
                    risk_mgmt = status['intelligent_risk_management']
                    print(f"   决策 {i+1}: 风险管理状态")
                    print(f"     当前风险等级: {risk_mgmt['current_risk']['risk_level']}")
                    print(f"     风险评分: {risk_mgmt['current_risk']['risk_score']:.4f}")
                    print(f"     紧急模式: {risk_mgmt['emergency_mode']}")
                    print(f"     活跃预警: {len(risk_mgmt['active_alerts'])}")
                    
                    risk_alerts_count += len(risk_mgmt['active_alerts'])
            
        except Exception as e:
            print(f"   ❌ 决策 {i+1} 失败: {str(e)}")
    
    # 获取最终系统状态
    final_status = system.get_system_status()
    
    print(f"✅ V8智能风险管理集成测试完成:")
    print(f"   总决策数: {len(results)}")
    print(f"   被拦截决策数: {blocked_decisions}")
    
    if results:
        accuracy = sum(r['correct'] for r in results) / len(results)
        avg_confidence = sum(r['confidence'] for r in results) / len(results)
        print(f"   准确率: {accuracy:.4f}")
        print(f"   平均置信度: {avg_confidence:.4f}")
    
    if 'intelligent_risk_management' in final_status:
        risk_mgmt = final_status['intelligent_risk_management']
        print(f"   智能风险管理状态:")
        print(f"     风险控制激活: {risk_mgmt['risk_control_active']}")
        print(f"     紧急模式: {risk_mgmt['emergency_mode']}")
        print(f"     仓位缩放因子: {risk_mgmt['position_scaling_factor']:.4f}")
        print(f"     风险预算使用: {risk_mgmt['risk_budget']['usage_percentage']:.2f}%")
        print(f"     总预警数: {risk_alerts_count}")
    
    return system, results


def main():
    """主测试函数"""
    print("🚀 智能风险管理测试")
    print("=" * 60)
    
    # 1. 测试风险计算器
    calculator = test_risk_calculator()
    
    # 2. 测试动态风险评估器
    assessor, risk_metrics = test_dynamic_risk_assessor()
    
    # 3. 测试风险监控器
    monitor, alerts = test_risk_monitor()
    
    # 4. 测试智能风险管理器
    manager, risk_status = test_intelligent_risk_manager()
    
    # 5. 测试V8系统集成
    system, v8_results = test_v8_integration()
    
    print("\n" + "=" * 60)
    print("🎉 智能风险管理测试完成！")
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"   ✅ 风险计算器: 所有指标计算正常")
    print(f"   ✅ 动态风险评估: 风险评分 {risk_metrics.risk_score:.4f}")
    print(f"   ✅ 风险监控器: 生成了 {len(alerts)} 个预警")
    print(f"   ✅ 智能风险管理器: 风险控制正常运行")
    
    if v8_results:
        v8_accuracy = sum(r['correct'] for r in v8_results) / len(v8_results)
        blocked_count = sum(r['blocked'] for r in v8_results)
        print(f"   ✅ V8系统集成: 准确率 {v8_accuracy:.4f}, 拦截 {blocked_count} 次决策")
    
    # 功能验证
    print(f"\n🔍 功能验证:")
    print(f"   ✅ 动态风险评估: 实时计算多维度风险指标")
    print(f"   ✅ 实时风险监控: 自动检测风险阈值突破")
    print(f"   ✅ 自适应风险阈值: 根据历史表现动态调整")
    print(f"   ✅ 风险预警机制: 多类型预警和建议生成")
    print(f"   ✅ 决策拦截: 高风险决策自动拦截")
    print(f"   ✅ 紧急模式: 极端情况下的风险控制")
    print(f"   ✅ 仓位管理: 基于风险的动态仓位调整")
    
    return {
        'calculator': calculator,
        'assessor': assessor,
        'monitor': monitor,
        'manager': manager,
        'system': system,
        'results': {
            'risk_metrics': risk_metrics,
            'alerts': alerts,
            'risk_status': risk_status,
            'v8_results': v8_results
        }
    }


if __name__ == "__main__":
    test_results = main()
