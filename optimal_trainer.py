#!/usr/bin/env python3
"""
最优策略训练器
使用最佳的3个策略: strategy_1, strategy_8, strategy_4
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class OptimalTrainer:
    """最优策略训练器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_optimal_data(self) -> pd.DataFrame:
        """加载最优策略数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            # 使用最佳的3个策略
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1,
                strategy_8,
                strategy_4,
                true_label as actual_result
            FROM strategy_results 
            WHERE strategy_1 IS NOT NULL 
                AND strategy_8 IS NOT NULL 
                AND strategy_4 IS NOT NULL
                AND true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载最优策略数据 (strategy_1, strategy_8, strategy_4)...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            for col in ['strategy_1', 'strategy_8', 'strategy_4']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            # 删除包含NaN的行
            initial_count = len(df)
            df = df.dropna()
            cleaned_count = len(df)
            
            self.logger.info(f"   清理前: {initial_count} 条记录")
            self.logger.info(f"   清理后: {cleaned_count} 条记录")
            self.logger.info(f"   删除了: {initial_count - cleaned_count} 条无效记录")
            
            if cleaned_count == 0:
                self.logger.error("❌ 清理后没有有效数据")
                return pd.DataFrame()
            
            # 确保数据类型正确
            for col in ['strategy_1', 'strategy_8', 'strategy_4', 'actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条最优策略数据")
            self.logger.info(f"   - Boot范围: {df['boot_id'].min()} - {df['boot_id'].max()}")
            self.logger.info(f"   - 正例比例: {df['actual_result'].mean():.3f}")
            
            # 检查策略相关性
            self.logger.info("\n🔍 策略相关性验证:")
            for strategy in ['strategy_1', 'strategy_8', 'strategy_4']:
                corr = df[strategy].corr(df['actual_result'])
                self.logger.info(f"   {strategy}: {corr:.6f}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def test_different_combinations(self, df: pd.DataFrame):
        """测试不同的策略组合"""
        self.logger.info("\n🧪 测试不同策略组合...")
        
        try:
            from sklearn.model_selection import train_test_split
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.metrics import accuracy_score
            
            # 测试不同组合
            combinations = [
                (['strategy_1'], "仅strategy_1"),
                (['strategy_1', 'strategy_8'], "strategy_1 + strategy_8"),
                (['strategy_1', 'strategy_4'], "strategy_1 + strategy_4"),
                (['strategy_8', 'strategy_4'], "strategy_8 + strategy_4"),
                (['strategy_1', 'strategy_8', 'strategy_4'], "全部3个策略"),
            ]
            
            results = {}
            
            for features, name in combinations:
                X = df[features].values
                y = df['actual_result'].values
                
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.2, random_state=42, stratify=y
                )
                
                # 使用随机森林快速测试
                model = RandomForestClassifier(n_estimators=100, random_state=42)
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                results[name] = accuracy
                self.logger.info(f"   {name}: {accuracy:.3f}")
            
            # 找到最佳组合
            best_combo = max(results.items(), key=lambda x: x[1])
            self.logger.info(f"\n🏆 最佳组合: {best_combo[0]} (准确率: {best_combo[1]:.3f})")
            
            return results
            
        except Exception as e:
            self.logger.error(f"❌ 组合测试失败: {e}")
            return {}
    
    def train_optimal_models(self, df: pd.DataFrame) -> Dict[str, Any]:
        """训练最优模型"""
        try:
            from sklearn.model_selection import train_test_split, cross_val_score
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
            import xgboost as xgb
            
            self.logger.info("\n🤖 开始最优模型训练...")
            
            # 使用所有3个策略
            X = df[['strategy_1', 'strategy_8', 'strategy_4']].values
            y = df['actual_result'].values
            
            self.logger.info(f"   📊 特征维度: {X.shape}")
            self.logger.info(f"   📊 标签分布: {np.bincount(y)}")
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            self.logger.info(f"   📊 数据分割: 训练集 {len(X_train)}, 测试集 {len(X_test)}")
            
            # 创建优化的模型
            models = {
                'logistic': LogisticRegression(random_state=42, max_iter=1000, C=1.0),
                'random_forest': RandomForestClassifier(
                    n_estimators=200, 
                    max_depth=8, 
                    min_samples_split=10,
                    min_samples_leaf=5,
                    random_state=42
                ),
                'gradient_boost': GradientBoostingClassifier(
                    n_estimators=150, 
                    learning_rate=0.1,
                    max_depth=5,
                    random_state=42
                ),
                'xgboost': xgb.XGBClassifier(
                    n_estimators=150,
                    max_depth=5,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42
                )
            }
            
            # 训练和评估
            results = {}
            for name, model in models.items():
                self.logger.info(f"   🔄 训练 {name}...")
                
                start_time = time.time()
                model.fit(X_train, y_train)
                train_time = time.time() - start_time
                
                # 预测和评估
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'train_time': train_time,
                    'predictions': y_pred
                }
                
                self.logger.info(f"      ✅ {name}: 准确率={accuracy:.3f}, CV={cv_scores.mean():.3f}±{cv_scores.std():.3f}")
            
            # 创建集成模型
            self.logger.info("   🔗 创建集成模型...")
            ensemble = VotingClassifier(
                estimators=[(name, result['model']) for name, result in results.items()],
                voting='soft'
            )
            
            ensemble.fit(X_train, y_train)
            ensemble_pred = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
            
            # 集成模型交叉验证
            ensemble_cv = cross_val_score(ensemble, X_train, y_train, cv=5)
            
            self.logger.info(f"   🏆 集成模型: 准确率={ensemble_accuracy:.3f}, CV={ensemble_cv.mean():.3f}±{ensemble_cv.std():.3f}")
            
            # 详细分析
            self.logger.info(f"\n📊 详细分析:")
            
            # 混淆矩阵
            cm = confusion_matrix(y_test, ensemble_pred)
            self.logger.info(f"   混淆矩阵:\n   {cm}")
            
            # 特征重要性（使用随机森林）
            rf_model = results['random_forest']['model']
            feature_names = ['strategy_1', 'strategy_8', 'strategy_4']
            importances = rf_model.feature_importances_
            
            self.logger.info(f"   特征重要性:")
            for name, importance in zip(feature_names, importances):
                self.logger.info(f"     {name}: {importance:.3f}")
            
            return {
                'individual_models': results,
                'ensemble': ensemble,
                'ensemble_accuracy': ensemble_accuracy,
                'ensemble_cv_mean': ensemble_cv.mean(),
                'ensemble_cv_std': ensemble_cv.std(),
                'feature_importances': dict(zip(feature_names, importances)),
                'confusion_matrix': cm,
                'test_data': (X_test, y_test, ensemble_pred)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 最优模型训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def run_optimal_training(self):
        """运行最优训练"""
        self.logger.info("🚀 开始最优策略训练")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载最优数据
        df = self.load_optimal_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，训练终止")
            return
        
        # 2. 测试不同组合
        combo_results = self.test_different_combinations(df)
        
        # 3. 训练最优模型
        results = self.train_optimal_models(df)
        
        if not results:
            self.logger.error("❌ 模型训练失败")
            return
        
        # 4. 生成报告
        total_time = time.time() - start_time
        self.generate_report(results, combo_results, total_time, len(df))
    
    def generate_report(self, results: Dict[str, Any], combo_results: Dict[str, float], 
                       total_time: float, total_samples: int):
        """生成训练报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 最优策略训练报告")
        self.logger.info("="*80)
        
        self.logger.info(f"\n📊 训练统计:")
        self.logger.info(f"   - 总样本数: {total_samples:,}")
        self.logger.info(f"   - 训练时间: {total_time:.1f}秒")
        self.logger.info(f"   - 使用策略: strategy_1, strategy_8, strategy_4")
        
        if combo_results:
            self.logger.info(f"\n🧪 策略组合测试结果:")
            for combo, accuracy in combo_results.items():
                self.logger.info(f"   - {combo}: {accuracy:.3f}")
        
        if 'individual_models' in results:
            self.logger.info(f"\n🤖 个别模型结果:")
            for name, model_info in results['individual_models'].items():
                self.logger.info(f"   - {name:15}: 准确率={model_info['accuracy']:.3f}, CV={model_info['cv_mean']:.3f}±{model_info['cv_std']:.3f}")
        
        if 'ensemble_accuracy' in results:
            self.logger.info(f"\n🏆 集成模型结果:")
            self.logger.info(f"   - 测试准确率: {results['ensemble_accuracy']:.3f}")
            self.logger.info(f"   - 交叉验证: {results['ensemble_cv_mean']:.3f}±{results['ensemble_cv_std']:.3f}")
        
        if 'feature_importances' in results:
            self.logger.info(f"\n🎯 特征重要性:")
            for feature, importance in results['feature_importances'].items():
                self.logger.info(f"   - {feature}: {importance:.3f}")
        
        # 评估结果
        target_accuracy = 0.60
        best_accuracy = results.get('ensemble_accuracy', 0)
        baseline_accuracy = 0.568  # 之前的基准
        
        improvement = best_accuracy - baseline_accuracy
        self.logger.info(f"\n📈 性能提升:")
        self.logger.info(f"   - 基准准确率: {baseline_accuracy:.3f}")
        self.logger.info(f"   - 当前准确率: {best_accuracy:.3f}")
        self.logger.info(f"   - 提升幅度: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        
        if best_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 训练成功！达到目标准确率 {target_accuracy:.1%}")
        else:
            gap = target_accuracy - best_accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f} ({gap/target_accuracy*100:.1f}%)")
        
        self.logger.info("="*80)

if __name__ == "__main__":
    trainer = OptimalTrainer()
    trainer.run_optimal_training()
