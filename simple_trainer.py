#!/usr/bin/env python3
"""
V8系统简化训练器
专注于核心ML模型训练，移除复杂的组件依赖
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SimpleTrainer:
    """简化的训练器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.system = None
        
    def load_historical_data(self, limit: int = 1000, offset: int = 0) -> pd.DataFrame:
        """加载历史数据"""
        try:
            # 首先尝试从数据库加载
            try:
                import pymysql

                # 数据库连接配置
                config = {
                    'host': '**************',
                    'user': 'root',
                    'password': 'Aa123456',
                    'database': 'lushu',
                    'charset': 'utf8mb4'
                }

                connection = pymysql.connect(**config)

                # 查询SQL
                sql = """
                SELECT
                    boot_id,
                    hand_id,
                    strategy_1,
                    strategy_2,
                    strategy_6,
                    actual_result
                FROM strategy_results
                WHERE strategy_1 IS NOT NULL
                    AND strategy_2 IS NOT NULL
                    AND strategy_6 IS NOT NULL
                    AND actual_result IS NOT NULL
                ORDER BY boot_id, hand_id
                LIMIT %s OFFSET %s
                """

                df = pd.read_sql(sql, connection, params=(limit, offset))
                connection.close()

                self.logger.info(f"从数据库加载了 {len(df)} 条历史数据")
                return df

            except Exception as db_error:
                self.logger.warning(f"数据库连接失败: {db_error}")
                self.logger.info("使用模拟数据进行训练...")

                # 生成模拟数据
                np.random.seed(42)

                data = []
                for boot_id in range(1, limit // 40 + 1):
                    for hand_id in range(1, 41):  # 每靴40手
                        # 生成策略输出 (0或1)
                        strategy_1 = np.random.choice([0, 1], p=[0.43, 0.57])  # 策略1偏向1
                        strategy_2 = np.random.choice([0, 1], p=[0.50, 0.50])  # 策略2平衡
                        strategy_6 = np.random.choice([0, 1], p=[0.50, 0.50])  # 策略6平衡

                        # 生成实际结果，与策略有一定相关性
                        consensus = (strategy_1 + strategy_2 + strategy_6) / 3
                        # 添加噪声，使准确率在55-65%之间
                        prob = 0.5 + (consensus - 0.5) * 0.3 + np.random.normal(0, 0.1)
                        prob = np.clip(prob, 0.1, 0.9)
                        actual_result = np.random.choice([0, 1], p=[1-prob, prob])

                        data.append({
                            'boot_id': boot_id,
                            'hand_id': hand_id,
                            'strategy_1': strategy_1,
                            'strategy_2': strategy_2,
                            'strategy_6': strategy_6,
                            'actual_result': actual_result
                        })

                        if len(data) >= limit:
                            break
                    if len(data) >= limit:
                        break

                df = pd.DataFrame(data)
                self.logger.info(f"生成了 {len(df)} 条模拟数据")
                return df

        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            return pd.DataFrame()
    
    def prepare_training_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        if df.empty:
            return np.array([]), np.array([])
        
        # 特征：3个策略的输出
        features = df[['strategy_1', 'strategy_2', 'strategy_6']].values
        
        # 标签：实际结果
        labels = df['actual_result'].values
        
        self.logger.info(f"准备了 {len(features)} 个训练样本，{features.shape[1]} 个特征")
        return features, labels
    
    def train_simple_models(self, X: np.ndarray, y: np.ndarray):
        """训练简单模型"""
        if len(X) == 0:
            self.logger.warning("没有训练数据")
            return
        
        try:
            from sklearn.model_selection import train_test_split
            from sklearn.ensemble import VotingClassifier, RandomForestClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import accuracy_score, classification_report
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            self.logger.info(f"训练集: {len(X_train)} 样本, 测试集: {len(X_test)} 样本")
            
            # 创建简单模型
            models = {
                'logistic': LogisticRegression(random_state=42, max_iter=1000),
                'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
            }
            
            # 训练和评估每个模型
            results = {}
            for name, model in models.items():
                self.logger.info(f"训练 {name} 模型...")
                
                # 训练
                start_time = time.time()
                model.fit(X_train, y_train)
                train_time = time.time() - start_time
                
                # 预测
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'train_time': train_time
                }
                
                self.logger.info(f"{name} - 准确率: {accuracy:.4f}, 训练时间: {train_time:.2f}秒")
            
            # 创建集成模型
            self.logger.info("创建集成模型...")
            ensemble = VotingClassifier(
                estimators=[(name, model['model']) for name, model in results.items()],
                voting='soft'
            )
            
            start_time = time.time()
            ensemble.fit(X_train, y_train)
            train_time = time.time() - start_time
            
            y_pred_ensemble = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, y_pred_ensemble)
            
            self.logger.info(f"集成模型 - 准确率: {ensemble_accuracy:.4f}, 训练时间: {train_time:.2f}秒")
            
            # 详细报告
            self.logger.info("\n" + "="*50)
            self.logger.info("训练结果总结:")
            self.logger.info("="*50)
            
            for name, result in results.items():
                self.logger.info(f"{name:15} - 准确率: {result['accuracy']:.4f}")
            
            self.logger.info(f"{'集成模型':15} - 准确率: {ensemble_accuracy:.4f}")
            self.logger.info("="*50)
            
            # 保存最佳模型
            best_model = ensemble if ensemble_accuracy > max(r['accuracy'] for r in results.values()) else \
                        max(results.items(), key=lambda x: x[1]['accuracy'])[1]['model']
            
            self.save_model(best_model, ensemble_accuracy)
            
            return results, ensemble, ensemble_accuracy
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            import traceback
            traceback.print_exc()
    
    def save_model(self, model, accuracy: float):
        """保存模型"""
        try:
            import joblib
            
            model_dir = Path("models")
            model_dir.mkdir(exist_ok=True)
            
            model_path = model_dir / f"simple_model_{accuracy:.4f}.pkl"
            joblib.dump(model, model_path)
            
            self.logger.info(f"模型已保存到: {model_path}")
            
        except Exception as e:
            self.logger.error(f"保存模型失败: {e}")
    
    def run_training(self, total_samples: int = 10000, batch_size: int = 1000):
        """运行训练"""
        self.logger.info("🚀 开始简化训练流程")
        self.logger.info("="*60)
        
        # 加载数据
        self.logger.info(f"📊 加载 {total_samples} 条训练数据...")
        df = self.load_historical_data(limit=total_samples)
        
        if df.empty:
            self.logger.error("❌ 无法加载训练数据")
            return
        
        # 数据统计
        self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
        self.logger.info(f"   - 正例比例: {df['actual_result'].mean():.3f}")
        self.logger.info(f"   - 策略1平均值: {df['strategy_1'].mean():.3f}")
        self.logger.info(f"   - 策略2平均值: {df['strategy_2'].mean():.3f}")
        self.logger.info(f"   - 策略6平均值: {df['strategy_6'].mean():.3f}")
        
        # 准备训练数据
        self.logger.info("\n🔧 准备训练数据...")
        X, y = self.prepare_training_data(df)
        
        if len(X) == 0:
            self.logger.error("❌ 无法准备训练数据")
            return
        
        # 训练模型
        self.logger.info("\n🤖 开始模型训练...")
        results = self.train_simple_models(X, y)
        
        self.logger.info("\n🎉 训练完成！")

if __name__ == "__main__":
    trainer = SimpleTrainer()
    trainer.run_training(total_samples=5000)  # 先用5000条数据测试
