#!/usr/bin/env python3
"""
V8系统简化训练器
专门用于训练阶段，移除复杂组件，专注于核心ML模型训练
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class SimplifiedTrainer:
    """简化训练器 - 专注于核心功能"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_all_data(self) -> pd.DataFrame:
        """加载所有历史数据"""
        try:
            import pymysql
            
            self.logger.info("🔗 连接数据库...")
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1,
                strategy_2,
                strategy_6,
                true_label as actual_result
            FROM strategy_results 
            WHERE strategy_1 IS NOT NULL 
                AND strategy_2 IS NOT NULL 
                AND strategy_6 IS NOT NULL
                AND true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载全部历史数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理和类型转换
            self.logger.info("🧹 清理数据...")

            # 清理策略列中的非数值数据
            for col in ['strategy_1', 'strategy_2', 'strategy_6']:
                # 将'-'和其他非数值替换为NaN
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 清理actual_result列
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')

            # 删除包含NaN的行
            initial_count = len(df)
            df = df.dropna()
            cleaned_count = len(df)

            self.logger.info(f"   清理前: {initial_count} 条记录")
            self.logger.info(f"   清理后: {cleaned_count} 条记录")
            self.logger.info(f"   删除了: {initial_count - cleaned_count} 条无效记录")

            if cleaned_count == 0:
                self.logger.error("❌ 清理后没有有效数据")
                return pd.DataFrame()

            # 确保数据类型正确
            for col in ['strategy_1', 'strategy_2', 'strategy_6', 'actual_result']:
                df[col] = df[col].astype(int)

            self.logger.info(f"✅ 成功加载 {len(df)} 条历史数据")
            self.logger.info(f"   - Boot范围: {df['boot_id'].min()} - {df['boot_id'].max()}")
            self.logger.info(f"   - 正例比例: {df['actual_result'].mean():.3f}")

            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def train_sklearn_models(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """训练sklearn模型"""
        try:
            from sklearn.model_selection import train_test_split, cross_val_score
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import accuracy_score, classification_report
            import xgboost as xgb
            
            self.logger.info("🤖 开始sklearn模型训练...")
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            self.logger.info(f"   📊 数据分割: 训练集 {len(X_train)}, 测试集 {len(X_test)}")
            
            # 创建优化的模型
            models = {
                'logistic': LogisticRegression(random_state=42, max_iter=1000, C=1.0),
                'random_forest': RandomForestClassifier(
                    n_estimators=300, 
                    max_depth=15, 
                    min_samples_split=5,
                    min_samples_leaf=2,
                    random_state=42
                ),
                'gradient_boost': GradientBoostingClassifier(
                    n_estimators=200, 
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42
                ),
                'xgboost': xgb.XGBClassifier(
                    n_estimators=200,
                    max_depth=6,
                    learning_rate=0.1,
                    subsample=0.8,
                    colsample_bytree=0.8,
                    random_state=42
                )
            }
            
            # 训练和评估
            results = {}
            for name, model in models.items():
                self.logger.info(f"   🔄 训练 {name}...")
                
                start_time = time.time()
                model.fit(X_train, y_train)
                train_time = time.time() - start_time
                
                # 预测和评估
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'train_time': train_time
                }
                
                self.logger.info(f"      ✅ {name}: 准确率={accuracy:.3f}, CV={cv_scores.mean():.3f}±{cv_scores.std():.3f}")
            
            # 创建集成模型
            self.logger.info("   🔗 创建集成模型...")
            ensemble = VotingClassifier(
                estimators=[(name, result['model']) for name, result in results.items()],
                voting='soft'
            )
            
            ensemble.fit(X_train, y_train)
            ensemble_pred = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
            
            # 集成模型交叉验证
            ensemble_cv = cross_val_score(ensemble, X_train, y_train, cv=5)
            
            self.logger.info(f"   🏆 集成模型: 准确率={ensemble_accuracy:.3f}, CV={ensemble_cv.mean():.3f}±{ensemble_cv.std():.3f}")
            
            return {
                'individual_models': results,
                'ensemble': ensemble,
                'ensemble_accuracy': ensemble_accuracy,
                'ensemble_cv_mean': ensemble_cv.mean(),
                'ensemble_cv_std': ensemble_cv.std(),
                'best_individual': max(results.items(), key=lambda x: x[1]['accuracy'])
            }
            
        except Exception as e:
            self.logger.error(f"❌ sklearn模型训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def save_models(self, results: Dict[str, Any]):
        """保存训练好的模型"""
        try:
            import joblib
            
            model_dir = Path("models")
            model_dir.mkdir(exist_ok=True)
            
            # 保存集成模型
            if 'ensemble' in results:
                ensemble_path = model_dir / "ensemble_model.pkl"
                joblib.dump(results['ensemble'], ensemble_path)
                self.logger.info(f"   💾 集成模型已保存: {ensemble_path}")
            
            # 保存个别模型
            if 'individual_models' in results:
                for name, model_info in results['individual_models'].items():
                    model_path = model_dir / f"{name}_model.pkl"
                    joblib.dump(model_info['model'], model_path)
                    self.logger.info(f"   💾 {name}模型已保存: {model_path}")
            
            # 保存训练结果
            results_path = model_dir / "training_results.txt"
            with open(results_path, 'w', encoding='utf-8') as f:
                f.write("V8系统训练结果\n")
                f.write("="*50 + "\n\n")
                
                if 'individual_models' in results:
                    f.write("个别模型结果:\n")
                    for name, model_info in results['individual_models'].items():
                        f.write(f"  {name}: 准确率={model_info['accuracy']:.3f}, CV={model_info['cv_mean']:.3f}±{model_info['cv_std']:.3f}\n")
                
                if 'ensemble_accuracy' in results:
                    f.write(f"\n集成模型: 准确率={results['ensemble_accuracy']:.3f}")
                    if 'ensemble_cv_mean' in results:
                        f.write(f", CV={results['ensemble_cv_mean']:.3f}±{results['ensemble_cv_std']:.3f}")
                    f.write("\n")
            
            self.logger.info(f"   📄 训练结果已保存: {results_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存模型失败: {e}")
    
    def run_simplified_training(self):
        """运行简化训练"""
        self.logger.info("🚀 开始V8系统简化训练")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df = self.load_all_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，训练终止")
            return
        
        # 2. 准备训练数据
        self.logger.info("\n📊 准备训练数据...")
        X = df[['strategy_1', 'strategy_2', 'strategy_6']].values
        y = df['actual_result'].values
        
        self.logger.info(f"   - 特征维度: {X.shape}")
        self.logger.info(f"   - 标签分布: {np.bincount(y)}")
        self.logger.info(f"   - 正例比例: {y.mean():.3f}")
        
        # 3. 训练模型
        self.logger.info("\n🤖 开始模型训练...")
        results = self.train_sklearn_models(X, y)
        
        if not results:
            self.logger.error("❌ 模型训练失败")
            return
        
        # 4. 保存模型
        self.logger.info("\n💾 保存训练结果...")
        self.save_models(results)
        
        # 5. 生成最终报告
        total_time = time.time() - start_time
        self.generate_final_report(results, total_time, len(df))
    
    def generate_final_report(self, results: Dict[str, Any], total_time: float, total_samples: int):
        """生成最终报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 V8系统简化训练最终报告")
        self.logger.info("="*80)
        
        self.logger.info(f"\n📊 训练统计:")
        self.logger.info(f"   - 总样本数: {total_samples:,}")
        self.logger.info(f"   - 训练时间: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
        self.logger.info(f"   - 处理速度: {total_samples/total_time:.0f} 样本/秒")
        
        if 'individual_models' in results:
            self.logger.info(f"\n🤖 个别模型结果:")
            for name, model_info in results['individual_models'].items():
                self.logger.info(f"   - {name:15}: 准确率={model_info['accuracy']:.3f}, CV={model_info['cv_mean']:.3f}±{model_info['cv_std']:.3f}")
        
        if 'ensemble_accuracy' in results:
            self.logger.info(f"\n🏆 集成模型结果:")
            self.logger.info(f"   - 测试准确率: {results['ensemble_accuracy']:.3f}")
            if 'ensemble_cv_mean' in results:
                self.logger.info(f"   - 交叉验证: {results['ensemble_cv_mean']:.3f}±{results['ensemble_cv_std']:.3f}")
        
        # 评估结果
        target_accuracy = 0.60
        best_accuracy = results.get('ensemble_accuracy', 0)
        
        if best_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 训练成功！达到目标准确率 {target_accuracy:.1%}")
            self.logger.info("✅ 系统已准备好进入生产环境")
        else:
            self.logger.warning(f"\n⚠️ 训练未达标：当前最佳准确率 {best_accuracy:.3f}，目标 {target_accuracy:.3f}")
            self.logger.info("🔧 建议进行超参数调优或增加更多特征")
        
        self.logger.info("="*80)

if __name__ == "__main__":
    trainer = SimplifiedTrainer()
    trainer.run_simplified_training()
