#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V8系统集成决策解释性系统测试

测试完整的V8系统，包括决策解释性功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import pandas as pd
import logging
import time
from typing import Dict, List, Any

from main import SimpleFusionV8

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_sample_external_data(decision_id: str = None):
    """创建示例外部数据"""
    if decision_id is None:
        decision_id = f"test_decision_{int(time.time())}"
    
    return {
        'decision_id': decision_id,
        'timestamp': time.time(),
        'market_data': {
            'price': 100.0,
            'volume': 1000,
            'volatility': 0.2
        },
        'external_signals': {
            'signal_1': 0.6,
            'signal_2': 0.4,
            'signal_3': 0.7
        }
    }

def test_v8_system_with_explainability():
    """测试V8系统的决策解释性功能"""
    print("\n" + "="*80)
    print("🚀 V8系统决策解释性集成测试")
    print("="*80)
    
    # 创建并初始化V8系统
    print("📦 创建V8系统实例...")
    system = SimpleFusionV8()
    
    print("🔧 初始化V8系统...")
    system.initialize()
    
    print("✅ V8系统初始化完成")
    
    # 获取系统状态
    print("\n📊 系统状态检查:")
    status = system.get_system_status()
    
    print(f"   - 系统已初始化: {status['is_initialized']}")
    print(f"   - 决策计数: {status['decision_count']}")
    print(f"   - 配置版本: {status['config_version']}")
    
    if 'decision_explainability' in status:
        explainability_status = status['decision_explainability']
        print(f"   - 解释系统状态:")
        print(f"     * 总解释数: {explainability_status['total_explanations']}")
        print(f"     * 特征重要性可用: {explainability_status['feature_importance_available']}")
        print(f"     * 已提取规则数: {explainability_status['rules_extracted']}")
        print(f"     * 支持的解释类型: {len(explainability_status['explanation_types_supported'])}")
    
    # 执行多次决策测试
    print(f"\n🎯 执行决策测试...")
    
    test_results = []
    
    for i in range(10):
        print(f"\n--- 决策 {i+1} ---")
        
        # 创建测试数据
        external_data = create_sample_external_data(f"test_decision_{i+1}")
        
        try:
            # 执行决策
            start_time = time.time()
            decision = system.process_decision(external_data)
            processing_time = time.time() - start_time
            
            print(f"✅ 决策完成:")
            print(f"   - 决策ID: {decision.decision_id}")
            print(f"   - 预测结果: {decision.prediction}")
            print(f"   - 置信度: {decision.confidence:.3f}")
            print(f"   - 风险等级: {decision.risk_level}")
            print(f"   - 是否执行: {decision.should_act}")
            print(f"   - 处理时间: {processing_time:.3f}秒")
            print(f"   - 决策理由: {decision.reasoning}")
            
            # 检查是否有解释
            if hasattr(decision, 'explanation') and decision.explanation:
                explanation = decision.explanation
                print(f"\n📖 决策解释:")
                print(f"   - 解释总结: {explanation.summary}")
                
                # 显示特征重要性
                if explanation.feature_importances:
                    print(f"   - 关键特征 (前3个):")
                    for imp in explanation.feature_importances[:3]:
                        print(f"     * {imp.feature_name}: {imp.importance_score:.3f} (排名: {imp.rank})")
                
                # 显示决策路径
                if explanation.decision_path and explanation.decision_path.steps:
                    print(f"   - 决策路径 ({len(explanation.decision_path.steps)}步):")
                    for step in explanation.decision_path.steps:
                        print(f"     * {step.layer_name}: {step.reasoning}")
                
                # 显示置信度分解
                if explanation.confidence_breakdown:
                    print(f"   - 置信度分解 (前3个):")
                    sorted_breakdown = sorted(explanation.confidence_breakdown.items(), 
                                            key=lambda x: x[1], reverse=True)
                    for component, value in sorted_breakdown[:3]:
                        print(f"     * {component}: {value:.3f}")
                
                # 显示适用规则
                if explanation.rules:
                    print(f"   - 适用规则 ({len(explanation.rules)}条):")
                    for rule in explanation.rules[:2]:
                        print(f"     * {rule.condition} -> {rule.conclusion}")
            else:
                print(f"   ⚠️  未生成决策解释")
            
            # 模拟实际结果反馈
            actual_result = np.random.choice([0, 1], p=[0.4, 0.6])  # 60%正确率
            print(f"\n🔄 反馈更新 (实际结果: {actual_result})...")
            
            system.update_feedback(decision.decision_id, actual_result)
            
            # 记录测试结果
            test_results.append({
                'decision_id': decision.decision_id,
                'prediction': decision.prediction,
                'confidence': decision.confidence,
                'risk_level': decision.risk_level,
                'should_act': decision.should_act,
                'actual_result': actual_result,
                'processing_time': processing_time,
                'has_explanation': hasattr(decision, 'explanation') and decision.explanation is not None
            })
            
            print(f"✅ 决策 {i+1} 完成")
            
        except Exception as e:
            print(f"❌ 决策 {i+1} 失败: {str(e)}")
            test_results.append({
                'decision_id': f"failed_{i+1}",
                'error': str(e)
            })
    
    # 分析测试结果
    print(f"\n" + "="*60)
    print("📊 测试结果分析")
    print("="*60)
    
    successful_decisions = [r for r in test_results if 'error' not in r]
    failed_decisions = [r for r in test_results if 'error' in r]
    
    print(f"✅ 成功决策: {len(successful_decisions)}/{len(test_results)}")
    print(f"❌ 失败决策: {len(failed_decisions)}")
    
    if successful_decisions:
        # 计算统计信息
        avg_confidence = np.mean([r['confidence'] for r in successful_decisions])
        avg_processing_time = np.mean([r['processing_time'] for r in successful_decisions])
        explanations_generated = sum(1 for r in successful_decisions if r['has_explanation'])
        
        # 计算准确率
        correct_predictions = sum(1 for r in successful_decisions 
                                if r['prediction'] == r['actual_result'])
        accuracy = correct_predictions / len(successful_decisions)
        
        # 风险等级分布
        risk_distribution = {}
        for r in successful_decisions:
            risk_level = r['risk_level']
            risk_distribution[risk_level] = risk_distribution.get(risk_level, 0) + 1
        
        print(f"\n📈 性能指标:")
        print(f"   - 平均置信度: {avg_confidence:.3f}")
        print(f"   - 平均处理时间: {avg_processing_time:.3f}秒")
        print(f"   - 预测准确率: {accuracy:.3f} ({correct_predictions}/{len(successful_decisions)})")
        print(f"   - 解释生成率: {explanations_generated/len(successful_decisions):.3f} ({explanations_generated}/{len(successful_decisions)})")
        
        print(f"\n🎯 风险等级分布:")
        for risk_level, count in risk_distribution.items():
            percentage = count / len(successful_decisions) * 100
            print(f"   - {risk_level}: {count} ({percentage:.1f}%)")
    
    # 获取最终系统状态
    print(f"\n📊 最终系统状态:")
    final_status = system.get_system_status()
    print(f"   - 总决策数: {final_status['decision_count']}")
    
    if 'decision_explainability' in final_status:
        explainability_status = final_status['decision_explainability']
        print(f"   - 解释系统:")
        print(f"     * 总解释数: {explainability_status['total_explanations']}")
        print(f"     * 平均置信度: {explainability_status['average_confidence']:.3f}")
        print(f"     * 已提取规则数: {explainability_status['rules_extracted']}")
    
    if 'intelligent_risk_management' in final_status:
        risk_status = final_status['intelligent_risk_management']
        print(f"   - 风险管理:")
        print(f"     * 当前风险等级: {risk_status.get('current_risk_level', 'unknown')}")
        print(f"     * 活跃预警数: {risk_status.get('active_alerts_count', 0)}")
        print(f"     * 紧急模式: {risk_status.get('emergency_mode', False)}")
    
    print(f"\n🎉 V8系统决策解释性集成测试完成！")
    
    return {
        'system': system,
        'test_results': test_results,
        'final_status': final_status
    }

def test_explanation_features():
    """测试解释功能的详细特性"""
    print("\n" + "="*60)
    print("🔍 解释功能详细测试")
    print("="*60)
    
    # 创建系统
    system = SimpleFusionV8()
    system.initialize()
    
    # 执行一次决策
    external_data = create_sample_external_data("detailed_explanation_test")
    decision = system.process_decision(external_data)
    
    if hasattr(decision, 'explanation') and decision.explanation:
        explanation = decision.explanation
        
        print(f"📖 详细解释分析:")
        print(f"   - 解释ID: {explanation.decision_id}")
        print(f"   - 解释类型: {explanation.explanation_type.value}")
        print(f"   - 生成时间: {time.ctime(explanation.timestamp)}")
        
        print(f"\n🔍 特征重要性详情:")
        for imp in explanation.feature_importances:
            print(f"   - {imp.feature_name}:")
            print(f"     * 重要性分数: {imp.importance_score:.3f}")
            print(f"     * 排名: {imp.rank}")
            print(f"     * 计算方法: {imp.method.value}")
            print(f"     * 描述: {imp.description}")
        
        if explanation.decision_path:
            print(f"\n🛤️  决策路径详情:")
            path = explanation.decision_path
            print(f"   - 总处理时间: {path.total_processing_time:.3f}秒")
            print(f"   - 关键特征: {path.critical_features}")
            print(f"   - 层级贡献:")
            for layer, contribution in path.decision_factors.items():
                print(f"     * {layer}: {contribution:.3f}")
        
        if explanation.counterfactuals:
            print(f"\n🔄 反事实分析:")
            for i, cf in enumerate(explanation.counterfactuals):
                print(f"   反事实 {i+1}:")
                print(f"   - 原始预测: {cf.original_prediction:.3f}")
                print(f"   - 反事实预测: {cf.counterfactual_prediction:.3f}")
                print(f"   - 可行性: {cf.feasibility_score:.3f}")
                print(f"   - 解释: {cf.explanation}")
        
        print(f"\n📖 详细推理过程:")
        print(explanation.detailed_reasoning)
        
    else:
        print("❌ 未生成详细解释")
    
    return decision

def main():
    """主测试函数"""
    print("🚀 开始V8系统决策解释性集成测试")
    
    try:
        # 基础集成测试
        integration_results = test_v8_system_with_explainability()
        
        # 详细解释功能测试
        detailed_explanation = test_explanation_features()
        
        print(f"\n" + "="*80)
        print("🎉 所有测试完成！")
        print("="*80)
        
        print(f"✅ V8系统决策解释性集成成功")
        print(f"✅ 解释功能运行正常")
        print(f"✅ 系统性能良好")
        
        return {
            'integration_results': integration_results,
            'detailed_explanation': detailed_explanation
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        raise

if __name__ == "__main__":
    results = main()
