# SimpleFusion V8 策略系统

## 🎯 系统概述

SimpleFusion V8 是基于 V6 深度分析结果构建的新一代智能策略系统。通过采用3个核心基础策略 + 机器学习集成的架构，实现更高的准确率和更强的风险控制能力。

### 核心优势
- **高准确率**: 基于最优3策略组合，目标准确率 56-58%
- **低相关性**: 策略独立性强，避免同向错误
- **强适应性**: 自动适应不同游戏环境和模式
- **智能风险控制**: 动态风险评估和止损机制

## 🏗️ 系统架构

### 四层架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    自适应决策层 (Layer 4)                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  置信度评估  │ │  风险控制   │ │  在线学习   │ │ 异常检测 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                                ↑
┌─────────────────────────────────────────────────────────────┐
│                    集成学习层 (Layer 3)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  基础投票   │ │  XGBoost    │ │  神经网络   │ │元学习器 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                                ↑
┌─────────────────────────────────────────────────────────────┐
│                    特征工程层 (Layer 2)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  一致性特征  │ │  分歧特征   │ │  历史特征   │ │动态权重 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                                ↑
┌─────────────────────────────────────────────────────────────┐
│                    基础策略层 (Layer 1)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│  │ Strategy_1  │ │ Strategy_2  │ │ Strategy_6  │             │
│  │  (56.94%)   │ │  (50.07%)   │ │  (49.98%)   │             │
│  └─────────────┘ └─────────────┘ └─────────────┘             │
└─────────────────────────────────────────────────────────────┘
```

## 📊 各层详细设计

### Layer 1: 基础策略层
**核心策略组合**: ['strategy_1', 'strategy_2', 'strategy_6']
- **选择依据**: 低相关性(0.9888) + 高互补性 + 最佳实际性能(53.59%)
- **策略特点**:
  - Strategy_1: 高胜率(56.94%)，独立性强
  - Strategy_2: 与其他策略相关性极低，互补性好
  - Strategy_6: 虽然胜率不高，但在特定情况下表现优异

### Layer 2: 特征工程层
**特征类别**:
1. **一致性特征**
   - 3策略一致度 (0, 1, 2, 3)
   - 一致性强度评分
   - 一致性历史趋势

2. **分歧特征**
   - 策略分歧模式 (1-2, 1-6, 2-6组合)
   - 分歧强度指标
   - 分歧解决历史

3. **历史特征**
   - 近N次决策的策略表现
   - 滑动窗口准确率
   - 策略稳定性指标

4. **动态权重特征**
   - 基于近期表现的权重
   - 环境适应性权重
   - 风险调整权重

### Layer 3: 集成学习层
**模型组合**:
1. **基础投票模型**
   - 简单多数投票
   - 加权投票 (基于历史表现)

2. **机器学习模型**
   - XGBoost: 处理非线性特征组合
   - LightGBM: 快速在线学习
   - 神经网络: 深度特征提取

3. **元学习器**
   - 学习何时使用哪个模型
   - 模型选择策略优化

### Layer 4: 自适应决策层
**智能决策机制**:
1. **置信度评估**
   - 基于策略一致性的置信度
   - 历史准确率置信度
   - 模型不确定性评估

2. **风险控制**
   - 动态决策阈值调整
   - 连错预测和预防
   - 智能止损机制

3. **在线学习**
   - 实时模型参数更新
   - 环境变化适应
   - 性能反馈循环

4. **异常检测**
   - 异常模式识别
   - 系统状态监控
   - 自动恢复机制

## 🔄 数据流设计

```
输入数据 → 基础策略层 → 特征工程层 → 集成学习层 → 自适应决策层 → 最终决策
    ↑                                                              ↓
    └─────────────────── 反馈学习循环 ←─────────────────────────────┘
```

## 📈 预期性能指标

| 指标 | V6当前 | V8目标 | 提升 |
|------|--------|--------|------|
| 准确率 | 53.59% | 56-58% | +2.4-4.4% |
| 最大连错 | 10次 | 6-8次 | -20-40% |
| 风险控制 | 基础 | 智能 | 显著提升 |
| 适应性 | 静态 | 动态 | 质的飞跃 |

## 🛠️ 技术栈

- **核心框架**: Python 3.8+
- **机器学习**: XGBoost, LightGBM, PyTorch
- **数据处理**: Pandas, NumPy
- **特征工程**: Scikit-learn
- **数据库**: MySQL, Redis (缓存)
- **监控**: Prometheus, Grafana
- **部署**: Docker, Kubernetes

## 📁 项目结构

```
V8/
├── README.md                 # 项目说明
├── requirements.txt          # 依赖包
├── config/                   # 配置文件
│   ├── config.yaml          # 主配置
│   └── model_config.yaml    # 模型配置
├── core/                     # 核心模块
│   ├── __init__.py
│   ├── base_strategies.py    # 基础策略层
│   ├── feature_engineering.py # 特征工程层
│   ├── ml_models.py         # 机器学习模型层
│   └── adaptive_decision.py  # 自适应决策层
├── features/                 # 特征工程
│   ├── __init__.py
│   ├── consistency_features.py
│   ├── divergence_features.py
│   ├── historical_features.py
│   └── dynamic_weights.py
├── models/                   # 模型实现
│   ├── __init__.py
│   ├── voting_models.py
│   ├── tree_models.py
│   ├── neural_models.py
│   └── meta_learner.py
├── utils/                    # 工具模块
│   ├── __init__.py
│   ├── data_processor.py
│   ├── model_persistence.py
│   └── metrics.py
├── tests/                    # 测试
│   ├── __init__.py
│   ├── test_strategies.py
│   ├── test_features.py
│   └── test_models.py
└── main.py                   # 主入口
```

## 🚀 开发计划

详见任务列表，分为6个阶段：
1. **Phase 1**: 基础架构设计 (当前)
2. **Phase 2**: 特征工程实现
3. **Phase 3**: 机器学习模型层
4. **Phase 4**: 自适应决策层
5. **Phase 5**: 系统集成测试
6. **Phase 6**: 生产部署

## 📊 成功指标

- [ ] 准确率达到 56%+
- [ ] 最大连错控制在 8次以内
- [ ] 系统响应时间 < 100ms
- [ ] 7x24小时稳定运行
- [ ] 自动适应新环境能力
