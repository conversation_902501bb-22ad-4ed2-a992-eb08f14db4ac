{"summary": {"total_iterations": 5, "best_accuracy": 0.5680068434559452, "target_accuracy": 0.6, "target_achieved": false}, "top_results": [{"iteration": 3, "timestamp": "20250624_075032", "config": {"name": "nonlinear_focus", "time_series": {"alpha_boot": 0.4, "alpha_global": 0.15, "windows": [5, 10], "trend_windows": [5]}, "interactions": {"position_interactions": true, "strategy_combinations": true, "nonlinear_transforms": true}, "statistics": {"boot_statistics": true, "global_windows": [100, 200]}}, "result": {"ensemble_accuracy": 0.5680068434559452, "feature_count": 50, "leakage_detected": false, "leakage_features": []}, "accuracy": 0.5680068434559452}, {"iteration": 4, "timestamp": "20250624_075111", "config": {"name": "long_history", "time_series": {"alpha_boot": 0.1, "alpha_global": 0.02, "windows": [10, 20, 50], "trend_windows": [10, 20]}, "interactions": {"position_interactions": true, "strategy_combinations": true, "nonlinear_transforms": false}, "statistics": {"boot_statistics": true, "global_windows": [100, 200, 500]}}, "result": {"ensemble_accuracy": 0.5680068434559452, "feature_count": 54, "leakage_detected": false, "leakage_features": []}, "accuracy": 0.5680068434559452}, {"iteration": 2, "timestamp": "20250624_074956", "config": {"name": "enhanced_timeseries", "time_series": {"alpha_boot": 0.2, "alpha_global": 0.05, "windows": [3, 5, 10, 20], "trend_windows": [3, 5, 7]}, "interactions": {"position_interactions": true, "strategy_combinations": true, "nonlinear_transforms": true}, "statistics": {"boot_statistics": true, "global_windows": [50, 100, 200]}}, "result": {"ensemble_accuracy": 0.5673652694610778, "feature_count": 64, "leakage_detected": false, "leakage_features": []}, "accuracy": 0.5673652694610778}, {"iteration": 5, "timestamp": "20250624_075211", "config": {"name": "model_tuned", "time_series": {"alpha_boot": 0.3, "alpha_global": 0.1, "windows": [3, 5, 10], "trend_windows": [3, 5]}, "interactions": {"position_interactions": true, "strategy_combinations": true, "nonlinear_transforms": true}, "statistics": {"boot_statistics": true, "global_windows": [50, 100, 200]}, "models": {"logistic_C": 0.01, "rf_n_estimators": 300, "rf_max_depth": 10, "lgb_n_estimators": 300, "lgb_learning_rate": 0.05}}, "result": {"ensemble_accuracy": 0.5669375534644996, "feature_count": 58, "leakage_detected": false, "leakage_features": []}, "accuracy": 0.5669375534644996}, {"iteration": 1, "timestamp": "20250624_074915", "config": {"name": "basic_timeseries", "time_series": {"alpha_boot": 0.3, "alpha_global": 0.1, "windows": [3, 5, 10], "trend_windows": [3, 5]}, "interactions": {"position_interactions": true, "strategy_combinations": true, "nonlinear_transforms": false}, "statistics": {"boot_statistics": true, "global_windows": [50, 100]}}, "result": {"ensemble_accuracy": 0.5667236954662105, "feature_count": 52, "leakage_detected": false, "leakage_features": []}, "accuracy": 0.5667236954662105}]}