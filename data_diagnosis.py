#!/usr/bin/env python3
"""
数据诊断脚本
检查数据泄露、特征有效性、模型差异性等问题
"""

import sys
import os
import logging
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class DataDiagnostic:
    """数据诊断器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1,
                strategy_2,
                strategy_6,
                true_label as actual_result
            FROM strategy_results 
            WHERE strategy_1 IS NOT NULL 
                AND strategy_2 IS NOT NULL 
                AND strategy_6 IS NOT NULL
                AND true_label IS NOT NULL
            ORDER BY boot_id, id
            LIMIT 1000
            """
            
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            for col in ['strategy_1', 'strategy_2', 'strategy_6']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in ['strategy_1', 'strategy_2', 'strategy_6', 'actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 加载了 {len(df)} 条数据用于诊断")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def check_data_leakage(self, df: pd.DataFrame):
        """检查数据泄露"""
        self.logger.info("\n🔍 检查数据泄露...")
        
        # 1. 检查特征与标签的完美相关性
        correlations = {}
        for col in ['strategy_1', 'strategy_2', 'strategy_6']:
            corr = df[col].corr(df['actual_result'])
            correlations[col] = corr
            self.logger.info(f"   {col} 与 actual_result 相关性: {corr:.6f}")
        
        # 2. 检查是否存在完美预测的组合
        self.logger.info("\n   检查策略组合的预测能力:")
        
        # 简单多数投票
        majority_vote = (df['strategy_1'] + df['strategy_2'] + df['strategy_6'] >= 2).astype(int)
        majority_accuracy = (majority_vote == df['actual_result']).mean()
        self.logger.info(f"   多数投票准确率: {majority_accuracy:.6f}")
        
        # 一致性投票
        unanimous_vote = ((df['strategy_1'] == df['strategy_2']) & 
                         (df['strategy_2'] == df['strategy_6'])).astype(int)
        unanimous_cases = unanimous_vote.sum()
        self.logger.info(f"   一致性案例数: {unanimous_cases}/{len(df)} ({unanimous_cases/len(df):.3f})")
        
        if unanimous_cases > 0:
            unanimous_accuracy = (df[unanimous_vote == 1]['strategy_1'] == 
                                df[unanimous_vote == 1]['actual_result']).mean()
            self.logger.info(f"   一致性案例准确率: {unanimous_accuracy:.6f}")
        
        # 3. 检查时间序列泄露
        self.logger.info("\n   检查时间序列泄露:")
        df_sorted = df.sort_values(['boot_id', 'id'])
        
        # 检查未来信息是否影响当前预测
        future_leak_check = []
        for i in range(len(df_sorted) - 1):
            current_strategies = [df_sorted.iloc[i]['strategy_1'], 
                                df_sorted.iloc[i]['strategy_2'], 
                                df_sorted.iloc[i]['strategy_6']]
            future_result = df_sorted.iloc[i + 1]['actual_result']
            
            # 检查当前策略是否"预知"了未来结果
            if sum(current_strategies) >= 2 and future_result == 1:
                future_leak_check.append(1)
            elif sum(current_strategies) < 2 and future_result == 0:
                future_leak_check.append(1)
            else:
                future_leak_check.append(0)
        
        if future_leak_check:
            future_leak_rate = np.mean(future_leak_check)
            self.logger.info(f"   疑似未来信息泄露率: {future_leak_rate:.6f}")
    
    def check_feature_validity(self, df: pd.DataFrame):
        """检查特征有效性"""
        self.logger.info("\n🔧 检查特征有效性...")
        
        # 1. 特征分布检查
        self.logger.info("   特征分布:")
        for col in ['strategy_1', 'strategy_2', 'strategy_6']:
            value_counts = df[col].value_counts().sort_index()
            self.logger.info(f"   {col}: {value_counts.to_dict()}")
        
        # 2. 特征独立性检查
        self.logger.info("\n   特征间相关性:")
        feature_cols = ['strategy_1', 'strategy_2', 'strategy_6']
        for i, col1 in enumerate(feature_cols):
            for col2 in feature_cols[i+1:]:
                corr = df[col1].corr(df[col2])
                self.logger.info(f"   {col1} vs {col2}: {corr:.6f}")
        
        # 3. 特征信息量检查
        self.logger.info("\n   特征信息量:")
        for col in feature_cols:
            # 计算信息熵
            value_counts = df[col].value_counts(normalize=True)
            entropy = -sum(p * np.log2(p) for p in value_counts if p > 0)
            self.logger.info(f"   {col} 信息熵: {entropy:.6f}")
            
            # 计算与标签的互信息
            contingency = pd.crosstab(df[col], df['actual_result'])
            self.logger.info(f"   {col} 交叉表:\n{contingency}")
    
    def check_model_differences(self, df: pd.DataFrame):
        """检查模型差异性"""
        self.logger.info("\n🤖 检查模型差异性...")
        
        try:
            from sklearn.model_selection import train_test_split
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import accuracy_score
            import xgboost as xgb
            
            X = df[['strategy_1', 'strategy_2', 'strategy_6']].values
            y = df['actual_result'].values
            
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.3, random_state=42, stratify=y
            )
            
            # 使用不同的随机种子和参数
            models = {
                'logistic_1': LogisticRegression(random_state=42, max_iter=1000),
                'logistic_2': LogisticRegression(random_state=123, max_iter=1000, C=0.1),
                'rf_1': RandomForestClassifier(n_estimators=50, random_state=42, max_depth=3),
                'rf_2': RandomForestClassifier(n_estimators=100, random_state=123, max_depth=10),
                'gb_1': GradientBoostingClassifier(n_estimators=50, random_state=42, learning_rate=0.1),
                'gb_2': GradientBoostingClassifier(n_estimators=100, random_state=123, learning_rate=0.01),
            }
            
            results = {}
            predictions = {}
            
            for name, model in models.items():
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                results[name] = accuracy
                predictions[name] = y_pred
                self.logger.info(f"   {name}: {accuracy:.6f}")
            
            # 检查预测差异
            self.logger.info("\n   预测差异性检查:")
            model_names = list(predictions.keys())
            for i, name1 in enumerate(model_names):
                for name2 in model_names[i+1:]:
                    agreement = (predictions[name1] == predictions[name2]).mean()
                    self.logger.info(f"   {name1} vs {name2} 一致性: {agreement:.6f}")
            
            # 检查是否所有模型都给出相同预测
            all_same = True
            base_pred = predictions[model_names[0]]
            for name in model_names[1:]:
                if not np.array_equal(predictions[name], base_pred):
                    all_same = False
                    break
            
            if all_same:
                self.logger.warning("   ⚠️ 所有模型预测完全相同！")
            else:
                self.logger.info("   ✅ 模型预测存在差异")
                
        except Exception as e:
            self.logger.error(f"   ❌ 模型差异性检查失败: {e}")
    
    def check_data_quality(self, df: pd.DataFrame):
        """深度数据质量检查"""
        self.logger.info("\n📊 深度数据质量检查...")
        
        # 1. 检查数据模式
        self.logger.info("   数据模式分析:")
        
        # 检查是否存在重复模式
        pattern_counts = df.groupby(['strategy_1', 'strategy_2', 'strategy_6', 'actual_result']).size()
        self.logger.info(f"   唯一模式数: {len(pattern_counts)}")
        self.logger.info(f"   最常见模式:")
        for pattern, count in pattern_counts.nlargest(5).items():
            self.logger.info(f"     {pattern}: {count} 次")
        
        # 2. 检查标签分布
        self.logger.info(f"\n   标签分布:")
        label_dist = df['actual_result'].value_counts()
        self.logger.info(f"   {label_dist.to_dict()}")
        self.logger.info(f"   平衡性: {label_dist.min()/label_dist.max():.3f}")
        
        # 3. 检查Boot内的一致性
        self.logger.info(f"\n   Boot内一致性检查:")
        boot_stats = []
        for boot_id in df['boot_id'].unique()[:10]:  # 检查前10个boot
            boot_data = df[df['boot_id'] == boot_id]
            if len(boot_data) > 1:
                # 检查策略在boot内的变化
                strategy_changes = {}
                for col in ['strategy_1', 'strategy_2', 'strategy_6']:
                    changes = (boot_data[col].diff() != 0).sum()
                    strategy_changes[col] = changes
                
                boot_stats.append({
                    'boot_id': boot_id,
                    'size': len(boot_data),
                    'changes': strategy_changes,
                    'accuracy': (boot_data['strategy_1'] == boot_data['actual_result']).mean()
                })
        
        for stat in boot_stats[:5]:
            self.logger.info(f"   Boot {stat['boot_id']}: 大小={stat['size']}, 变化={stat['changes']}, 准确率={stat['accuracy']:.3f}")
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        self.logger.info("🔍 开始数据完整诊断")
        self.logger.info("="*80)
        
        # 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，诊断终止")
            return
        
        # 运行各项检查
        self.check_data_leakage(df)
        self.check_feature_validity(df)
        self.check_model_differences(df)
        self.check_data_quality(df)
        
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 诊断完成")
        self.logger.info("="*80)

if __name__ == "__main__":
    diagnostic = DataDiagnostic()
    diagnostic.run_full_diagnosis()
