#!/usr/bin/env python3
"""
多层次融合特征训练器
重新设计特征工程，解决特征不平衡问题
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class FusionTrainer:
    """多层次融合特征训练器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_all_strategies(self) -> pd.DataFrame:
        """加载所有8个策略数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            # 加载所有8个策略
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载所有8个策略数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            # 删除包含NaN的行
            initial_count = len(df)
            df = df.dropna()
            cleaned_count = len(df)
            
            self.logger.info(f"   清理前: {initial_count} 条记录")
            self.logger.info(f"   清理后: {cleaned_count} 条记录")
            
            # 确保数据类型正确
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            
            # 检查各策略相关性
            self.logger.info("\n🔍 各策略相关性:")
            correlations = {}
            for col in strategy_cols:
                corr = df[col].corr(df['actual_result'])
                correlations[col] = corr
                self.logger.info(f"   {col}: {corr:.6f}")
            
            return df, correlations
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame(), {}
    
    def create_fusion_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建多层次融合特征"""
        self.logger.info("\n🔧 创建多层次融合特征...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. 基础统计特征
        self.logger.info("   1️⃣ 基础统计特征...")
        df['strategy_sum'] = df[strategy_cols].sum(axis=1)
        df['strategy_mean'] = df[strategy_cols].mean(axis=1)
        df['strategy_std'] = df[strategy_cols].std(axis=1)
        df['strategy_min'] = df[strategy_cols].min(axis=1)
        df['strategy_max'] = df[strategy_cols].max(axis=1)
        df['strategy_range'] = df['strategy_max'] - df['strategy_min']
        df['strategy_median'] = df[strategy_cols].median(axis=1)
        
        # 2. 投票机制特征
        self.logger.info("   2️⃣ 投票机制特征...")
        df['majority_vote'] = (df['strategy_sum'] >= 4).astype(int)
        df['supermajority_vote'] = (df['strategy_sum'] >= 6).astype(int)
        df['unanimous_vote'] = (df['strategy_sum'] == 8).astype(int)
        df['minority_vote'] = (df['strategy_sum'] <= 2).astype(int)
        df['consensus_strength'] = np.abs(df['strategy_sum'] - 4) / 4  # 距离中位数的强度
        
        # 3. 分组融合特征
        self.logger.info("   3️⃣ 分组融合特征...")
        # 高相关性组 (基于之前的分析)
        high_corr_group = ['strategy_1', 'strategy_8']
        df['high_corr_sum'] = df[high_corr_group].sum(axis=1)
        df['high_corr_mean'] = df[high_corr_group].mean(axis=1)
        
        # 中等相关性组
        mid_corr_group = ['strategy_3', 'strategy_5', 'strategy_7']
        df['mid_corr_sum'] = df[mid_corr_group].sum(axis=1)
        df['mid_corr_mean'] = df[mid_corr_group].mean(axis=1)
        
        # 低相关性组
        low_corr_group = ['strategy_2', 'strategy_4', 'strategy_6']
        df['low_corr_sum'] = df[low_corr_group].sum(axis=1)
        df['low_corr_mean'] = df[low_corr_group].mean(axis=1)
        
        # 4. 对比特征
        self.logger.info("   4️⃣ 对比特征...")
        df['high_vs_low'] = df['high_corr_mean'] - df['low_corr_mean']
        df['high_vs_mid'] = df['high_corr_mean'] - df['mid_corr_mean']
        df['mid_vs_low'] = df['mid_corr_mean'] - df['low_corr_mean']
        
        # 5. 加权融合特征
        self.logger.info("   5️⃣ 加权融合特征...")
        # 基于相关性的权重
        weights = {
            'strategy_1': 0.147, 'strategy_8': 0.038, 'strategy_4': -0.016,
            'strategy_5': 0.014, 'strategy_7': 0.007, 'strategy_3': 0.007,
            'strategy_6': -0.005, 'strategy_2': -0.002
        }
        
        df['weighted_sum'] = sum(df[col] * abs(weight) for col, weight in weights.items())
        df['signed_weighted_sum'] = sum(df[col] * weight for col, weight in weights.items())
        
        # 6. 非线性组合特征
        self.logger.info("   6️⃣ 非线性组合特征...")
        df['strategy_product'] = df[strategy_cols].prod(axis=1)
        df['strategy_geometric_mean'] = np.power(df['strategy_product'], 1/8)
        
        # 策略间交互
        df['s1_s8_interaction'] = df['strategy_1'] * df['strategy_8']
        df['s1_dominance'] = df['strategy_1'] * (8 - df['strategy_sum'])  # strategy_1在少数时的权重
        
        # 7. 序列特征 (时间序列)
        self.logger.info("   7️⃣ 序列特征...")
        df = df.sort_values(['boot_id', 'id']).reset_index(drop=True)
        
        # 滑动窗口特征
        for window in [3, 5, 10]:
            df[f'strategy_sum_ma_{window}'] = df['strategy_sum'].rolling(window=window, min_periods=1).mean()
            df[f'strategy_sum_std_{window}'] = df['strategy_sum'].rolling(window=window, min_periods=1).std().fillna(0)
            df[f'majority_vote_ma_{window}'] = df['majority_vote'].rolling(window=window, min_periods=1).mean()
        
        # 趋势特征
        df['strategy_sum_diff'] = df['strategy_sum'].diff().fillna(0)
        df['strategy_sum_momentum'] = df['strategy_sum'].rolling(window=3, min_periods=1).apply(
            lambda x: x.iloc[-1] - x.iloc[0] if len(x) > 1 else 0
        ).fillna(0)
        
        # 8. Boot内特征
        self.logger.info("   8️⃣ Boot内特征...")
        boot_stats = df.groupby('boot_id').agg({
            'strategy_sum': ['mean', 'std', 'min', 'max'],
            'majority_vote': 'mean',
            'strategy_1': 'mean'
        }).round(4)
        
        boot_stats.columns = ['_'.join(col).strip() for col in boot_stats.columns]
        boot_stats = boot_stats.add_prefix('boot_')
        
        df = df.merge(boot_stats, left_on='boot_id', right_index=True, how='left')
        
        # Boot内位置
        df['position_in_boot'] = df.groupby('boot_id').cumcount() + 1
        df['boot_progress'] = df.groupby('boot_id')['position_in_boot'].transform(lambda x: x / x.max())
        
        # 9. 平衡化特征
        self.logger.info("   9️⃣ 平衡化特征...")
        # 对strategy_1进行降权处理
        df['strategy_1_dampened'] = df['strategy_1'] * 0.5  # 降低权重
        df['other_strategies_boosted'] = (df['strategy_sum'] - df['strategy_1']) * 1.5  # 提升其他策略
        df['balanced_sum'] = df['strategy_1_dampened'] + df['other_strategies_boosted']
        
        # 填充NaN值
        df = df.fillna(0)
        df = df.replace([np.inf, -np.inf], 0)
        
        feature_count = len(df.columns) - len(strategy_cols) - 3  # 减去原始策略列和id, boot_id, actual_result
        self.logger.info(f"   ✅ 创建了 {feature_count} 个融合特征")
        
        return df
    
    def apply_feature_engineering(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """应用高级特征工程"""
        self.logger.info("\n🎯 应用高级特征工程...")
        
        # 准备特征
        exclude_cols = ['id', 'boot_id', 'actual_result'] + [f'strategy_{i}' for i in range(1, 9)]
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        
        X = df[feature_cols].values
        y = df['actual_result'].values
        
        self.logger.info(f"   原始特征数: {len(feature_cols)}")
        
        # 1. 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 2. 特征选择 - 多种方法结合
        # 方法1: 基于F统计量
        selector_f = SelectKBest(score_func=f_classif, k=min(30, len(feature_cols)))
        X_f = selector_f.fit_transform(X_scaled, y)
        selected_f = selector_f.get_support(indices=True)
        
        # 方法2: 基于互信息
        selector_mi = SelectKBest(score_func=mutual_info_classif, k=min(30, len(feature_cols)))
        X_mi = selector_mi.fit_transform(X_scaled, y)
        selected_mi = selector_mi.get_support(indices=True)
        
        # 合并选择的特征
        selected_features = list(set(selected_f) | set(selected_mi))
        X_selected = X_scaled[:, selected_features]
        selected_feature_names = [feature_cols[i] for i in selected_features]
        
        self.logger.info(f"   F统计量选择: {len(selected_f)} 个特征")
        self.logger.info(f"   互信息选择: {len(selected_mi)} 个特征")
        self.logger.info(f"   合并后: {len(selected_features)} 个特征")
        
        # 3. PCA降维 (可选)
        if len(selected_features) > 20:
            pca = PCA(n_components=0.95, random_state=42)  # 保留95%方差
            X_pca = pca.fit_transform(X_selected)
            self.logger.info(f"   PCA降维: {X_pca.shape[1]} 个主成分")
            
            # 创建PCA特征名
            pca_feature_names = [f'PCA_{i+1}' for i in range(X_pca.shape[1])]
            
            return X_pca, y, pca_feature_names
        else:
            return X_selected, y, selected_feature_names
    
    def train_fusion_models(self, X: np.ndarray, y: np.ndarray, feature_names: List[str]) -> Dict[str, Any]:
        """训练融合模型"""
        try:
            from sklearn.model_selection import train_test_split, cross_val_score
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import accuracy_score, classification_report
            import xgboost as xgb
            import lightgbm as lgb
            
            self.logger.info("\n🤖 开始融合模型训练...")
            self.logger.info(f"   特征维度: {X.shape}")
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 创建多样化的模型
            models = {
                'logistic': LogisticRegression(random_state=42, max_iter=1000, C=0.1),
                'logistic_l1': LogisticRegression(random_state=42, max_iter=1000, penalty='l1', solver='liblinear', C=0.1),
                'random_forest': RandomForestClassifier(
                    n_estimators=200, max_depth=8, min_samples_split=10,
                    min_samples_leaf=5, random_state=42
                ),
                'lightgbm': lgb.LGBMClassifier(
                    n_estimators=200, max_depth=6, learning_rate=0.1,
                    subsample=0.8, colsample_bytree=0.8, random_state=42,
                    verbose=-1  # 减少输出
                ),
                'xgboost': xgb.XGBClassifier(
                    n_estimators=150, max_depth=5, learning_rate=0.1,
                    subsample=0.8, colsample_bytree=0.8, random_state=42
                ),
                'gradient_boost': GradientBoostingClassifier(
                    n_estimators=100, learning_rate=0.1, max_depth=5, random_state=42
                )
            }
            
            # 训练和评估
            results = {}
            for name, model in models.items():
                self.logger.info(f"   🔄 训练 {name}...")
                
                start_time = time.time()
                model.fit(X_train, y_train)
                train_time = time.time() - start_time
                
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'train_time': train_time
                }
                
                self.logger.info(f"      ✅ {name}: 准确率={accuracy:.3f}, CV={cv_scores.mean():.3f}±{cv_scores.std():.3f}")
            
            # 创建多层集成
            self.logger.info("   🔗 创建多层集成模型...")
            
            # 第一层：基础模型集成
            base_ensemble = VotingClassifier(
                estimators=[(name, result['model']) for name, result in results.items()],
                voting='soft'
            )
            
            base_ensemble.fit(X_train, y_train)
            base_pred = base_ensemble.predict(X_test)
            base_accuracy = accuracy_score(y_test, base_pred)
            
            # 第二层：选择最佳模型进行加权集成
            top_models = sorted(results.items(), key=lambda x: x[1]['cv_mean'], reverse=True)[:3]
            
            weighted_ensemble = VotingClassifier(
                estimators=[(name, result['model']) for name, result in top_models],
                voting='soft'
            )
            
            weighted_ensemble.fit(X_train, y_train)
            weighted_pred = weighted_ensemble.predict(X_test)
            weighted_accuracy = accuracy_score(y_test, weighted_pred)
            
            self.logger.info(f"   🏆 基础集成: 准确率={base_accuracy:.3f}")
            self.logger.info(f"   🏆 加权集成: 准确率={weighted_accuracy:.3f}")
            
            return {
                'individual_models': results,
                'base_ensemble': base_ensemble,
                'weighted_ensemble': weighted_ensemble,
                'base_accuracy': base_accuracy,
                'weighted_accuracy': weighted_accuracy,
                'feature_names': feature_names,
                'test_data': (X_test, y_test, weighted_pred)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 融合模型训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def run_fusion_training(self):
        """运行融合训练"""
        self.logger.info("🚀 开始多层次融合训练")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df, correlations = self.load_all_strategies()
        if df.empty:
            self.logger.error("❌ 无法加载数据，训练终止")
            return
        
        # 2. 创建融合特征
        df_fusion = self.create_fusion_features(df)
        
        # 3. 高级特征工程
        X, y, feature_names = self.apply_feature_engineering(df_fusion)
        
        # 4. 训练融合模型
        results = self.train_fusion_models(X, y, feature_names)
        
        if not results:
            self.logger.error("❌ 模型训练失败")
            return
        
        # 5. 生成报告
        total_time = time.time() - start_time
        self.generate_report(results, correlations, total_time, len(df))
    
    def generate_report(self, results: Dict[str, Any], correlations: Dict[str, float],
                       total_time: float, total_samples: int):
        """生成训练报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 多层次融合训练报告")
        self.logger.info("="*80)
        
        self.logger.info(f"\n📊 训练统计:")
        self.logger.info(f"   - 总样本数: {total_samples:,}")
        self.logger.info(f"   - 训练时间: {total_time:.1f}秒")
        self.logger.info(f"   - 最终特征数: {len(results.get('feature_names', []))}")
        
        if 'individual_models' in results:
            self.logger.info(f"\n🤖 个别模型结果:")
            for name, model_info in results['individual_models'].items():
                self.logger.info(f"   - {name:15}: 准确率={model_info['accuracy']:.3f}, CV={model_info['cv_mean']:.3f}±{model_info['cv_std']:.3f}")
        
        self.logger.info(f"\n🏆 集成模型结果:")
        self.logger.info(f"   - 基础集成: {results.get('base_accuracy', 0):.3f}")
        self.logger.info(f"   - 加权集成: {results.get('weighted_accuracy', 0):.3f}")
        
        # 性能提升分析
        baseline_accuracy = 0.568
        best_accuracy = results.get('weighted_accuracy', 0)
        improvement = best_accuracy - baseline_accuracy
        
        self.logger.info(f"\n📈 性能提升:")
        self.logger.info(f"   - 基准准确率: {baseline_accuracy:.3f}")
        self.logger.info(f"   - 融合准确率: {best_accuracy:.3f}")
        self.logger.info(f"   - 提升幅度: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        
        target_accuracy = 0.60
        if best_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 训练成功！达到目标准确率 {target_accuracy:.1%}")
        else:
            gap = target_accuracy - best_accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f} ({gap/target_accuracy*100:.1f}%)")
        
        self.logger.info("="*80)

if __name__ == "__main__":
    trainer = FusionTrainer()
    trainer.run_fusion_training()
