#!/usr/bin/env python3
"""
动态策略选择器
学习在什么情况下使用哪个策略，实现理论上限62.4%的目标
"""

import sys
import os
import logging
import warnings
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class DynamicStrategySelector:
    """动态策略选择器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def create_context_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建上下文特征用于策略选择"""
        self.logger.info("🔧 创建上下文特征...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. Boot级别上下文
        df['position_in_boot'] = df.groupby('boot_id').cumcount() + 1
        df['boot_size'] = df.groupby('boot_id')['boot_id'].transform('count')
        df['boot_progress'] = df['position_in_boot'] / df['boot_size']
        
        # Boot阶段
        df['boot_stage'] = pd.cut(df['boot_progress'], 
                                 bins=[0, 0.25, 0.5, 0.75, 1.0], 
                                 labels=['early', 'mid_early', 'mid_late', 'late'],
                                 include_lowest=True)
        df['boot_stage_encoded'] = df['boot_stage'].cat.codes
        
        # 2. 历史表现上下文（简化版）
        window_sizes = [5, 10, 20]

        for window in window_sizes:
            for strategy in strategy_cols:
                # 简化的历史准确率特征
                df[f'{strategy}_ma_{window}'] = df[strategy].rolling(window=window, min_periods=1).mean()
                df[f'{strategy}_std_{window}'] = df[strategy].rolling(window=window, min_periods=1).std().fillna(0)
        
        # 3. 策略一致性上下文
        df['strategy_consensus'] = df[strategy_cols].std(axis=1)
        df['strategy_sum'] = df[strategy_cols].sum(axis=1)
        df['strategy_agreement'] = (df['strategy_sum'] == 0) | (df['strategy_sum'] == 8)
        
        # 4. 全局时间上下文
        df['global_position'] = range(len(df))
        df['global_progress'] = df['global_position'] / len(df)
        
        # 5. 策略变化上下文
        for strategy in strategy_cols:
            df[f'{strategy}_changed'] = df.groupby('boot_id')[strategy].diff().fillna(0).abs()
            df[f'{strategy}_stability'] = df.groupby('boot_id')[f'{strategy}_changed'].rolling(window=5, min_periods=1).mean().reset_index(0, drop=True).fillna(0)
        
        return df
    
    def create_strategy_performance_labels(self, df: pd.DataFrame) -> pd.DataFrame:
        """为每个样本创建最佳策略标签"""
        self.logger.info("🎯 创建策略性能标签...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 为每个样本找出最佳策略
        best_strategies = []
        strategy_accuracies = []
        
        for idx, row in df.iterrows():
            # 计算每个策略的正确性
            accuracies = {}
            for strategy in strategy_cols:
                is_correct = (row[strategy] == row['actual_result'])
                accuracies[strategy] = 1.0 if is_correct else 0.0
            
            # 找出最佳策略（如果有多个，选择strategy_1优先）
            max_acc = max(accuracies.values())
            best_strats = [s for s, acc in accuracies.items() if acc == max_acc]
            
            # 优先级排序
            priority_order = ['strategy_1', 'strategy_8', 'strategy_3', 'strategy_5', 'strategy_7', 'strategy_2', 'strategy_4', 'strategy_6']
            best_strategy = min(best_strats, key=lambda x: priority_order.index(x))
            
            best_strategies.append(best_strategy)
            strategy_accuracies.append(accuracies)
        
        df['best_strategy'] = best_strategies
        
        # 编码最佳策略
        strategy_to_id = {f'strategy_{i}': i-1 for i in range(1, 9)}
        df['best_strategy_id'] = df['best_strategy'].map(strategy_to_id)
        
        return df
    
    def train_strategy_selector(self, df: pd.DataFrame) -> Dict[str, Any]:
        """训练策略选择器"""
        try:
            from sklearn.model_selection import train_test_split, cross_val_score
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.metrics import accuracy_score, classification_report
            from sklearn.preprocessing import StandardScaler
            import lightgbm as lgb
            
            self.logger.info("🤖 训练策略选择器...")
            
            # 准备特征
            exclude_cols = ['id', 'boot_id', 'actual_result', 'best_strategy', 'best_strategy_id', 'boot_stage'] + [f'strategy_{i}' for i in range(1, 9)]
            feature_cols = [col for col in df.columns if col not in exclude_cols]
            
            X = df[feature_cols].values
            y = df['best_strategy_id'].values
            
            self.logger.info(f"   📊 特征数: {len(feature_cols)}")
            self.logger.info(f"   📊 样本数: {len(X)}")
            self.logger.info(f"   📊 策略类别: {len(np.unique(y))}")
            
            # 特征标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # 按Boot分割数据
            unique_boots = df['boot_id'].unique()
            train_boots, test_boots = train_test_split(unique_boots, test_size=0.2, random_state=42)
            
            train_mask = df['boot_id'].isin(train_boots)
            test_mask = df['boot_id'].isin(test_boots)
            
            X_train, X_test = X_scaled[train_mask], X_scaled[test_mask]
            y_train, y_test = y[train_mask], y[test_mask]
            
            self.logger.info(f"   📊 训练样本: {len(X_train)}, 测试样本: {len(X_test)}")
            
            # 训练多个策略选择器
            models = {
                'random_forest': RandomForestClassifier(
                    n_estimators=200, max_depth=10, min_samples_split=10,
                    min_samples_leaf=5, random_state=42
                ),
                'lightgbm': lgb.LGBMClassifier(
                    n_estimators=200, max_depth=8, learning_rate=0.1,
                    random_state=42, verbose=-1
                ),
                'gradient_boost': GradientBoostingClassifier(
                    n_estimators=150, learning_rate=0.1, max_depth=8,
                    random_state=42
                )
            }
            
            best_models = {}
            selector_accuracies = {}
            
            for name, model in models.items():
                self.logger.info(f"   🔄 训练 {name}...")
                
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                best_models[name] = model
                selector_accuracies[name] = accuracy
                
                self.logger.info(f"      ✅ 策略选择准确率: {accuracy:.3f}")
            
            # 选择最佳策略选择器
            best_selector_name = max(selector_accuracies, key=selector_accuracies.get)
            best_selector = best_models[best_selector_name]
            
            self.logger.info(f"   🏆 最佳策略选择器: {best_selector_name} ({selector_accuracies[best_selector_name]:.3f})")
            
            return {
                'best_selector': best_selector,
                'best_selector_name': best_selector_name,
                'selector_accuracy': selector_accuracies[best_selector_name],
                'all_models': best_models,
                'scaler': scaler,
                'feature_names': feature_cols,
                'test_data': (X_test, y_test, df[test_mask])
            }
            
        except Exception as e:
            self.logger.error(f"❌ 策略选择器训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def evaluate_dynamic_system(self, df: pd.DataFrame, selector_result: Dict[str, Any]) -> Dict[str, Any]:
        """评估动态策略选择系统"""
        self.logger.info("📈 评估动态策略选择系统...")
        
        try:
            selector = selector_result['best_selector']
            scaler = selector_result['scaler']
            feature_names = selector_result['feature_names']
            X_test, y_test, test_df = selector_result['test_data']
            
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            
            # 预测最佳策略
            predicted_strategy_ids = selector.predict(X_test)
            
            # 计算动态选择的准确率
            correct_predictions = 0
            total_predictions = len(test_df)
            
            strategy_usage = {f'strategy_{i}': 0 for i in range(1, 9)}
            
            for idx, (_, row) in enumerate(test_df.iterrows()):
                predicted_strategy_id = predicted_strategy_ids[idx]
                predicted_strategy = f'strategy_{predicted_strategy_id + 1}'
                
                # 使用预测的策略进行决策
                predicted_decision = row[predicted_strategy]
                actual_result = row['actual_result']
                
                if predicted_decision == actual_result:
                    correct_predictions += 1
                
                strategy_usage[predicted_strategy] += 1
            
            dynamic_accuracy = correct_predictions / total_predictions
            
            # 对比基准
            baseline_accuracy = 0.568  # strategy_1的准确率
            improvement = dynamic_accuracy - baseline_accuracy
            
            # 计算理论上限（完美选择）
            perfect_correct = 0
            for _, row in test_df.iterrows():
                best_strategy = row['best_strategy']
                perfect_decision = row[best_strategy]
                if perfect_decision == row['actual_result']:
                    perfect_correct += 1
            
            perfect_accuracy = perfect_correct / len(test_df)
            
            self.logger.info(f"   📊 动态选择准确率: {dynamic_accuracy:.3f}")
            self.logger.info(f"   📊 基准准确率: {baseline_accuracy:.3f}")
            self.logger.info(f"   📊 提升幅度: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
            self.logger.info(f"   📊 理论上限: {perfect_accuracy:.3f}")
            self.logger.info(f"   📊 实现比例: {(dynamic_accuracy - baseline_accuracy)/(perfect_accuracy - baseline_accuracy)*100:.1f}%")
            
            self.logger.info(f"\n   🎯 策略使用分布:")
            for strategy, count in strategy_usage.items():
                percentage = count / total_predictions * 100
                self.logger.info(f"     {strategy}: {count} ({percentage:.1f}%)")
            
            return {
                'dynamic_accuracy': dynamic_accuracy,
                'baseline_accuracy': baseline_accuracy,
                'improvement': improvement,
                'perfect_accuracy': perfect_accuracy,
                'realization_ratio': (dynamic_accuracy - baseline_accuracy)/(perfect_accuracy - baseline_accuracy),
                'strategy_usage': strategy_usage
            }
            
        except Exception as e:
            self.logger.error(f"❌ 动态系统评估失败: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    def run_dynamic_optimization(self):
        """运行动态优化"""
        self.logger.info("🚀 开始动态策略选择优化")
        self.logger.info("🎯 目标：实现理论上限62.4%的准确率")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，优化终止")
            return
        
        # 2. 创建上下文特征
        df = self.create_context_features(df)
        
        # 3. 创建策略性能标签
        df = self.create_strategy_performance_labels(df)
        
        # 4. 训练策略选择器
        selector_result = self.train_strategy_selector(df)
        
        if not selector_result:
            self.logger.error("❌ 策略选择器训练失败")
            return
        
        # 5. 评估动态系统
        evaluation_result = self.evaluate_dynamic_system(df, selector_result)
        
        # 6. 生成最终报告
        total_time = time.time() - start_time
        self.generate_final_report(evaluation_result, total_time)
    
    def generate_final_report(self, evaluation_result: Dict[str, Any], total_time: float):
        """生成最终报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 动态策略选择最终报告")
        self.logger.info("="*80)
        
        if not evaluation_result:
            self.logger.error("❌ 没有评估结果")
            return
        
        dynamic_accuracy = evaluation_result.get('dynamic_accuracy', 0)
        baseline_accuracy = evaluation_result.get('baseline_accuracy', 0.568)
        improvement = evaluation_result.get('improvement', 0)
        perfect_accuracy = evaluation_result.get('perfect_accuracy', 0.624)
        realization_ratio = evaluation_result.get('realization_ratio', 0)
        
        self.logger.info(f"\n📊 性能对比:")
        self.logger.info(f"   - 基准准确率 (strategy_1): {baseline_accuracy:.3f}")
        self.logger.info(f"   - 动态选择准确率: {dynamic_accuracy:.3f}")
        self.logger.info(f"   - 理论上限: {perfect_accuracy:.3f}")
        self.logger.info(f"   - 实际提升: {improvement:.3f} ({improvement/baseline_accuracy*100:+.1f}%)")
        self.logger.info(f"   - 潜力实现: {realization_ratio*100:.1f}%")
        
        target_accuracy = 0.60
        if dynamic_accuracy >= target_accuracy:
            self.logger.info(f"\n🎉 成功达到目标准确率 {target_accuracy:.1%}!")
            self.logger.info("✅ 动态策略选择系统已准备好投入生产！")
        else:
            gap = target_accuracy - dynamic_accuracy
            self.logger.info(f"\n📊 距离目标还差: {gap:.3f}")
            
            if improvement > 0.01:  # 提升超过1%
                self.logger.info("✅ 动态选择显著有效！建议继续优化选择器")
            else:
                self.logger.info("⚠️ 动态选择效果有限，可能需要更好的上下文特征")
        
        self.logger.info(f"\n⏱️ 总优化时间: {total_time:.1f}秒")
        self.logger.info("="*80)

if __name__ == "__main__":
    optimizer = DynamicStrategySelector()
    optimizer.run_dynamic_optimization()
