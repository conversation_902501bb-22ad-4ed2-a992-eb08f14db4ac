#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试训练后的模型性能

验证模型训练效果和预测能力
"""

import sys
import os
import time
import numpy as np
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from main import SimpleFusionV8
    from utils.data_processor import DataProcessor
    print("✅ 成功导入所需模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_model_predictions():
    """测试模型预测功能"""
    print("\n🧪 测试模型预测功能...")
    
    # 创建系统实例
    system = SimpleFusionV8()
    system.initialize()
    
    # 加载少量测试数据
    test_df = system.data_processor.load_historical_data(limit=100)
    
    if test_df.empty:
        print("❌ 无法加载测试数据")
        return False
    
    print(f"✅ 加载了 {len(test_df)} 条测试数据")
    
    # 测试决策流程
    correct_predictions = 0
    total_predictions = 0
    
    for idx, row in test_df.head(20).iterrows():
        try:
            # 模拟外部数据
            external_data = {
                'decision_id': f"test_{idx}",
                'current_boot': {'boot_id': int(row['boot_id']), 'position': idx % 40},
                'timestamp': time.time()
            }
            
            # 处理决策
            decision = system.process_decision(external_data)
            
            # 获取实际结果
            actual_result = int(row['actual_result'])
            
            # 更新反馈
            system.update_feedback(decision.decision_id, actual_result)
            
            # 统计准确率
            if decision.prediction == actual_result:
                correct_predictions += 1
            total_predictions += 1
            
            print(f"   决策 {idx}: 预测={decision.prediction}, 实际={actual_result}, "
                  f"置信度={decision.confidence:.3f}, 正确={decision.prediction == actual_result}")
            
        except Exception as e:
            print(f"   ❌ 决策 {idx} 失败: {str(e)}")
    
    # 计算准确率
    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
    
    print(f"\n📊 测试结果:")
    print(f"   总预测数: {total_predictions}")
    print(f"   正确预测: {correct_predictions}")
    print(f"   准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # 获取系统状态
    status = system.get_system_status()
    print(f"\n📋 系统状态:")
    print(f"   决策数: {status['decision_count']}")
    print(f"   当前性能: {status['current_performance']}")
    
    return accuracy > 0.5  # 期望准确率超过50%


def test_individual_models():
    """测试各个模型的性能"""
    print("\n🧪 测试各个模型性能...")
    
    # 创建系统实例
    system = SimpleFusionV8()
    system.initialize()
    
    # 创建测试特征
    test_features = {
        'consensus_ratio': 0.67,
        'weighted_consensus': 0.65,
        'avg_confidence': 0.55,
        'min_confidence': 0.4,
        'max_confidence': 0.7,
        'confidence_std': 0.1,
        'total_divergence': 1.0,
        'max_divergence': 1.0,
        'strategy_1_prediction': 1.0,
        'strategy_2_prediction': 0.0,
        'strategy_6_prediction': 1.0,
        'strategy_1_confidence': 0.6,
        'strategy_2_confidence': 0.5,
        'strategy_6_confidence': 0.55,
        'win_rate_10': 0.6,
        'win_rate_5': 0.8,
        'current_streak': 2,
        'strategy_1_weight': 0.4,
        'strategy_2_weight': 0.3,
        'strategy_6_weight': 0.3
    }
    
    print(f"📊 测试特征: {len(test_features)} 个特征")
    
    # 测试每个模型
    model_results = {}
    
    for model_name, model in system.ml_models.models.items():
        try:
            print(f"\n   测试 {model_name}...")
            
            # 获取预测
            prediction = model.predict(test_features)
            
            model_results[model_name] = {
                'success': True,
                'prediction': prediction.prediction,
                'confidence': prediction.confidence,
                'reasoning': prediction.reasoning
            }
            
            print(f"     ✅ 预测: {prediction.prediction:.4f}")
            print(f"     ✅ 置信度: {prediction.confidence:.4f}")
            print(f"     ✅ 理由: {prediction.reasoning}")
            
        except Exception as e:
            model_results[model_name] = {
                'success': False,
                'error': str(e)
            }
            print(f"     ❌ 失败: {str(e)}")
    
    # 总结结果
    successful_models = sum(1 for r in model_results.values() if r['success'])
    total_models = len(model_results)
    
    print(f"\n📊 模型测试总结:")
    print(f"   总模型数: {total_models}")
    print(f"   成功模型: {successful_models}")
    print(f"   成功率: {successful_models/total_models*100:.1f}%")
    
    return model_results


def test_performance_comparison():
    """测试性能对比"""
    print("\n🧪 测试性能对比...")
    
    # 创建系统实例
    system = SimpleFusionV8()
    system.initialize()
    
    # 加载测试数据
    test_df = system.data_processor.load_historical_data(limit=500)
    
    if test_df.empty:
        print("❌ 无法加载测试数据")
        return None
    
    print(f"✅ 加载了 {len(test_df)} 条测试数据")
    
    # 测试不同方法的准确率
    methods = {
        'V8系统': [],
        '简单投票': [],
        '策略1单独': [],
        '策略组合': []
    }
    
    for idx, row in test_df.head(100).iterrows():
        actual_result = int(row['actual_result'])
        
        # V8系统预测
        try:
            external_data = {
                'decision_id': f"perf_test_{idx}",
                'current_boot': {'boot_id': int(row['boot_id']), 'position': idx % 40}
            }
            decision = system.process_decision(external_data)
            methods['V8系统'].append(decision.prediction == actual_result)
        except:
            methods['V8系统'].append(False)
        
        # 简单投票
        s1, s2, s6 = int(row['strategy_1']), int(row['strategy_2']), int(row['strategy_6'])
        simple_vote = 1 if (s1 + s2 + s6) >= 2 else 0
        methods['简单投票'].append(simple_vote == actual_result)
        
        # 策略1单独
        methods['策略1单独'].append(s1 == actual_result)
        
        # 策略组合（加权）
        weighted_pred = 1 if (s1 * 0.4 + s2 * 0.3 + s6 * 0.3) >= 0.5 else 0
        methods['策略组合'].append(weighted_pred == actual_result)
    
    # 计算准确率
    print(f"\n📊 性能对比结果:")
    for method, results in methods.items():
        if results:
            accuracy = sum(results) / len(results)
            print(f"   {method}: {accuracy:.4f} ({accuracy*100:.2f}%)")
        else:
            print(f"   {method}: 无数据")
    
    return methods


def main():
    """主测试函数"""
    print("🚀 训练后模型性能测试")
    print("=" * 60)
    
    # 1. 测试模型预测功能
    prediction_success = test_model_predictions()
    
    # 2. 测试各个模型
    model_results = test_individual_models()
    
    # 3. 测试性能对比
    performance_comparison = test_performance_comparison()
    
    print("\n" + "=" * 60)
    print("🎉 模型性能测试完成！")
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"   ✅ 预测功能: {'正常' if prediction_success else '异常'}")
    
    if model_results:
        successful_models = sum(1 for r in model_results.values() if r['success'])
        print(f"   ✅ 模型可用性: {successful_models}/{len(model_results)}")
    
    if performance_comparison:
        v8_accuracy = sum(performance_comparison['V8系统']) / len(performance_comparison['V8系统'])
        simple_accuracy = sum(performance_comparison['简单投票']) / len(performance_comparison['简单投票'])
        print(f"   📈 V8系统准确率: {v8_accuracy:.4f}")
        print(f"   📈 简单投票准确率: {simple_accuracy:.4f}")
        print(f"   🚀 性能提升: {(v8_accuracy - simple_accuracy)*100:+.2f}%")
    
    return prediction_success, model_results, performance_comparison


if __name__ == "__main__":
    prediction_success, model_results, performance_comparison = main()
