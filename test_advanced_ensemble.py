#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级集成学习功能

验证Stacking、Blending等高级集成学习方法
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from core.advanced_ensemble import AdvancedEnsembleLayer, StackingEnsemble, BlendingEnsemble
    from core.ml_models import MLModelLayer
    from main import SimpleFusionV8
    print("✅ 成功导入高级集成学习模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def create_mock_predictions(num_samples: int = 100):
    """创建模拟的基础模型预测数据"""
    predictions_list = []
    labels_list = []
    
    for i in range(num_samples):
        # 模拟4个基础模型的预测
        predictions = {
            'voting': 0.3 + np.random.random() * 0.4,
            'xgboost': 0.2 + np.random.random() * 0.6,
            'neural_network': 0.25 + np.random.random() * 0.5,
            'meta_learner': 0.35 + np.random.random() * 0.3
        }
        
        # 生成标签（基于预测的加权平均，添加噪声）
        avg_pred = sum(predictions.values()) / len(predictions)
        label = 1 if (avg_pred + np.random.normal(0, 0.1)) > 0.5 else 0
        
        predictions_list.append(predictions)
        labels_list.append(label)
    
    return predictions_list, labels_list


def test_stacking_ensemble():
    """测试Stacking集成学习"""
    print("\n🧪 测试Stacking集成学习...")
    
    # 创建配置
    config = {
        'stacking': {
            'cv_folds': 3,
            'meta_learner': 'logistic',
            'random_state': 42
        }
    }
    
    # 创建Stacking集成
    stacking = StackingEnsemble(config)
    
    # 创建训练数据
    predictions_list, labels_list = create_mock_predictions(80)
    
    print(f"📊 训练数据: {len(predictions_list)} 个样本")
    
    # 训练Stacking模型
    stacking.train(predictions_list, labels_list)
    
    # 测试预测
    test_predictions, test_labels = create_mock_predictions(20)
    
    correct_predictions = 0
    total_predictions = len(test_predictions)
    
    for i, (pred_dict, true_label) in enumerate(zip(test_predictions, test_labels)):
        # 获取Stacking预测
        ensemble_pred = stacking.predict(pred_dict)
        
        # 转换为分类结果
        predicted_class = 1 if ensemble_pred.prediction >= 0.5 else 0
        is_correct = predicted_class == true_label
        
        if is_correct:
            correct_predictions += 1
        
        if i < 5:  # 显示前5个预测
            print(f"   预测 {i+1}: {ensemble_pred.prediction:.3f} -> {predicted_class}, "
                  f"实际: {true_label}, 正确: {is_correct}")
            print(f"     置信度: {ensemble_pred.confidence:.3f}, 理由: {ensemble_pred.reasoning}")
    
    accuracy = correct_predictions / total_predictions
    print(f"✅ Stacking测试完成:")
    print(f"   准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"   训练状态: {stacking.is_trained}")
    
    return accuracy


def test_blending_ensemble():
    """测试Blending集成学习"""
    print("\n🧪 测试Blending集成学习...")
    
    # 创建配置
    config = {
        'blending': {
            'holdout_ratio': 0.2
        }
    }
    
    # 创建Blending集成
    blending = BlendingEnsemble(config)
    
    # 创建训练数据
    predictions_list, labels_list = create_mock_predictions(100)
    
    print(f"📊 训练数据: {len(predictions_list)} 个样本")
    
    # 训练Blending模型
    blending.train(predictions_list, labels_list)
    
    # 测试预测
    test_predictions, test_labels = create_mock_predictions(20)
    
    correct_predictions = 0
    total_predictions = len(test_predictions)
    
    for i, (pred_dict, true_label) in enumerate(zip(test_predictions, test_labels)):
        # 获取Blending预测
        ensemble_pred = blending.predict(pred_dict)
        
        # 转换为分类结果
        predicted_class = 1 if ensemble_pred.prediction >= 0.5 else 0
        is_correct = predicted_class == true_label
        
        if is_correct:
            correct_predictions += 1
        
        if i < 5:  # 显示前5个预测
            print(f"   预测 {i+1}: {ensemble_pred.prediction:.3f} -> {predicted_class}, "
                  f"实际: {true_label}, 正确: {is_correct}")
            print(f"     权重: {ensemble_pred.model_weights}")
    
    accuracy = correct_predictions / total_predictions
    print(f"✅ Blending测试完成:")
    print(f"   准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"   训练状态: {blending.is_trained}")
    print(f"   优化权重: {blending.weights}")
    
    return accuracy


def test_advanced_ensemble_layer():
    """测试高级集成学习层"""
    print("\n🧪 测试高级集成学习层...")
    
    # 创建配置
    config = {
        'advanced_ensemble': {
            'min_training_samples': 20,
            'stacking': {
                'cv_folds': 3,
                'meta_learner': 'logistic'
            },
            'blending': {
                'holdout_ratio': 0.2
            }
        }
    }
    
    # 创建高级集成学习层
    ensemble_layer = AdvancedEnsembleLayer(config)
    
    # 模拟训练过程
    print("📊 模拟训练过程...")
    
    for i in range(50):
        # 创建模拟预测和标签
        predictions = {
            'voting': 0.3 + np.random.random() * 0.4,
            'xgboost': 0.2 + np.random.random() * 0.6,
            'neural_network': 0.25 + np.random.random() * 0.5,
            'meta_learner': 0.35 + np.random.random() * 0.3
        }
        
        avg_pred = sum(predictions.values()) / len(predictions)
        actual_result = 1 if (avg_pred + np.random.normal(0, 0.1)) > 0.5 else 0
        
        # 收集训练数据
        ensemble_layer.collect_training_data(predictions, actual_result)
        
        if i % 10 == 0:
            print(f"   收集了 {len(ensemble_layer.training_buffer)} 个训练样本")
    
    # 测试不同集成方法
    test_methods = ['stacking', 'blending', 'auto']
    method_results = {}
    
    for method in test_methods:
        print(f"\n   测试 {method} 方法...")
        
        correct = 0
        total = 20
        
        for i in range(total):
            # 创建测试预测
            test_pred = {
                'voting': 0.3 + np.random.random() * 0.4,
                'xgboost': 0.2 + np.random.random() * 0.6,
                'neural_network': 0.25 + np.random.random() * 0.5,
                'meta_learner': 0.35 + np.random.random() * 0.3
            }
            
            # 获取集成预测
            ensemble_result = ensemble_layer.predict(test_pred, method)
            
            # 生成真实标签
            avg_pred = sum(test_pred.values()) / len(test_pred)
            true_label = 1 if (avg_pred + np.random.normal(0, 0.1)) > 0.5 else 0
            
            # 判断预测正确性
            predicted_class = 1 if ensemble_result.prediction >= 0.5 else 0
            if predicted_class == true_label:
                correct += 1
        
        accuracy = correct / total
        method_results[method] = accuracy
        print(f"     {method} 准确率: {accuracy:.4f}")
    
    # 获取集成信息
    ensemble_info = ensemble_layer.get_ensemble_info()
    print(f"\n📊 集成学习层信息:")
    for key, value in ensemble_info.items():
        print(f"   {key}: {value}")
    
    return method_results


def test_v8_integration():
    """测试V8系统集成"""
    print("\n🧪 测试V8系统集成...")
    
    # 创建V8系统
    system = SimpleFusionV8()
    system.initialize()
    
    print("✅ V8系统初始化完成")
    
    # 测试集成预测
    results = []
    
    for i in range(10):
        try:
            # 模拟外部数据
            external_data = {
                'decision_id': f"ensemble_test_{i}",
                'current_boot': {'boot_id': 200 + i, 'position': i % 40}
            }
            
            # 处理决策
            decision = system.process_decision(external_data)
            
            # 获取基础模型预测
            base_predictions = system.ml_models.predict(decision.features)
            base_pred_dict = {name: pred.prediction for name, pred in base_predictions.items()}
            
            # 获取高级集成预测
            if hasattr(system.ml_models, 'get_ensemble_prediction'):
                ensemble_pred = system.ml_models.get_ensemble_prediction(decision.features, 'blending')
                
                results.append({
                    'decision_id': decision.decision_id,
                    'base_predictions': base_pred_dict,
                    'ensemble_prediction': ensemble_pred.prediction,
                    'ensemble_method': ensemble_pred.model_name,
                    'final_decision': decision.prediction
                })
                
                print(f"   决策 {i+1}: 集成={ensemble_pred.prediction:.3f}, "
                      f"最终={decision.prediction}, 方法={ensemble_pred.model_name}")
            
            # 模拟反馈
            actual_result = np.random.choice([0, 1])
            system.update_feedback(decision.decision_id, actual_result)
            
        except Exception as e:
            print(f"   ❌ 决策 {i+1} 失败: {str(e)}")
    
    print(f"✅ V8集成测试完成，处理了 {len(results)} 个决策")
    
    return results


def main():
    """主测试函数"""
    print("🚀 高级集成学习测试")
    print("=" * 60)
    
    # 1. 测试Stacking集成
    stacking_accuracy = test_stacking_ensemble()
    
    # 2. 测试Blending集成
    blending_accuracy = test_blending_ensemble()
    
    # 3. 测试高级集成学习层
    ensemble_results = test_advanced_ensemble_layer()
    
    # 4. 测试V8系统集成
    v8_results = test_v8_integration()
    
    print("\n" + "=" * 60)
    print("🎉 高级集成学习测试完成！")
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"   ✅ Stacking准确率: {stacking_accuracy:.4f}")
    print(f"   ✅ Blending准确率: {blending_accuracy:.4f}")
    
    if ensemble_results:
        print(f"   📊 集成方法对比:")
        for method, accuracy in ensemble_results.items():
            print(f"     {method}: {accuracy:.4f}")
    
    print(f"   ✅ V8系统集成: 处理了 {len(v8_results)} 个决策")
    
    # 性能对比
    best_method = max(ensemble_results.items(), key=lambda x: x[1]) if ensemble_results else None
    if best_method:
        print(f"   🏆 最佳集成方法: {best_method[0]} ({best_method[1]:.4f})")
    
    return {
        'stacking_accuracy': stacking_accuracy,
        'blending_accuracy': blending_accuracy,
        'ensemble_results': ensemble_results,
        'v8_results': v8_results
    }


if __name__ == "__main__":
    results = main()
