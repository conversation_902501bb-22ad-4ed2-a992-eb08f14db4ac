#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
决策解释性系统测试

测试决策解释性系统的各个组件功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import pandas as pd
import logging
import time
from typing import Dict, List, Any

from core.decision_explainability import (
    DecisionExplainer, FeatureImportanceCalculator, DecisionPathTracker,
    CounterfactualGenerator, RuleExtractor, ExplanationType, ImportanceMethod
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_test_config():
    """创建测试配置"""
    return {
        'explainability': {
            'feature_importance': {
                'methods': ['correlation', 'variance', 'permutation'],
                'top_k': 10
            },
            'decision_path': {
                'track_all_steps': True,
                'max_steps': 20
            },
            'counterfactual': {
                'max_changes': 3,
                'change_magnitude': 0.2
            },
            'rules': {
                'min_support': 5,
                'min_confidence': 0.7,
                'max_rules': 20
            }
        }
    }

def create_sample_features():
    """创建示例特征"""
    return {
        'consensus_ratio': 0.75,
        'avg_confidence': 0.68,
        'weighted_consensus': 0.72,
        'strategy_1_confidence': 0.65,
        'strategy_2_confidence': 0.71,
        'strategy_6_confidence': 0.69,
        'recent_accuracy': 0.63,
        'volatility': 0.45,
        'trend_strength': 0.58,
        'pattern_match': 0.52
    }

def test_feature_importance_calculator():
    """测试特征重要性计算器"""
    print("\n" + "="*60)
    print("🧮 测试特征重要性计算器")
    print("="*60)
    
    config = create_test_config()
    calculator = FeatureImportanceCalculator(config['explainability'])
    
    # 添加历史数据
    print("📊 添加历史数据...")
    for i in range(50):
        features = create_sample_features()
        # 添加一些随机变化
        for key in features:
            features[key] += np.random.normal(0, 0.1)
            features[key] = max(0, min(1, features[key]))
        
        prediction = 0.6 + np.random.normal(0, 0.2)
        prediction = max(0, min(1, prediction))
        
        actual = 1 if prediction > 0.5 else 0
        calculator.add_decision_data(features, prediction, actual)
    
    print(f"   ✅ 已添加 {len(calculator.history_data)} 条历史数据")
    
    # 测试不同的重要性计算方法
    test_features = create_sample_features()
    
    methods = [ImportanceMethod.CORRELATION, ImportanceMethod.VARIANCE]
    
    for method in methods:
        print(f"\n📈 测试 {method.value} 方法:")
        importances = calculator.calculate_feature_importance(test_features, method)
        
        print(f"   计算出 {len(importances)} 个特征的重要性:")
        for imp in importances[:5]:
            print(f"   - {imp.feature_name}: {imp.importance_score:.3f} (排名: {imp.rank})")
            print(f"     方法: {imp.method.value}, 描述: {imp.description}")
    
    return True

def test_decision_path_tracker():
    """测试决策路径跟踪器"""
    print("\n" + "="*60)
    print("🛤️  测试决策路径跟踪器")
    print("="*60)
    
    config = create_test_config()
    tracker = DecisionPathTracker(config['explainability'])
    
    # 开始跟踪决策
    decision_id = "test_decision_001"
    print(f"🎯 开始跟踪决策: {decision_id}")
    tracker.start_decision_tracking(decision_id)
    
    # 添加决策步骤
    layers = [
        ("策略层", {"strategies": [1, 0, 1, 0, 1, 0, 1, 1]}, {"consensus": 0.625}, "策略共识分析", 0.75, 0.8),
        ("特征层", {"consensus_ratio": 0.625}, {"enhanced_features": True}, "特征增强处理", 0.68, 0.7),
        ("ML层", {"features": "processed"}, {"predictions": {"rf": 0.72, "xgb": 0.68}}, "机器学习预测", 0.70, 0.9),
        ("融合层", {"ml_preds": [0.72, 0.68]}, {"fused_pred": 0.70}, "预测融合", 0.69, 0.85),
        ("决策层", {"fused_pred": 0.70}, {"final_decision": 0.71}, "最终决策生成", 0.71, 0.95)
    ]
    
    print("📝 添加决策步骤:")
    for layer_name, input_data, output_data, reasoning, confidence, contribution in layers:
        tracker.add_decision_step(layer_name, input_data, output_data, reasoning, confidence, contribution)
        print(f"   ✅ {layer_name}: {reasoning} (置信度: {confidence:.2f}, 贡献: {contribution:.2f})")
    
    # 完成决策路径
    final_prediction = 0.71
    final_confidence = 0.69
    
    print(f"\n🏁 完成决策路径 (预测: {final_prediction:.3f}, 置信度: {final_confidence:.3f})")
    decision_path = tracker.finalize_decision_path(final_prediction, final_confidence)
    
    if decision_path:
        print(f"   ✅ 决策路径生成成功:")
        print(f"   - 决策ID: {decision_path.decision_id}")
        print(f"   - 步骤数: {len(decision_path.steps)}")
        print(f"   - 处理时间: {decision_path.total_processing_time:.3f}秒")
        print(f"   - 关键特征: {decision_path.critical_features}")
        print(f"   - 层级贡献: {decision_path.decision_factors}")
        
        print(f"\n📋 详细步骤:")
        for step in decision_path.steps:
            print(f"   {step.step_id}: {step.layer_name} -> {step.reasoning}")
    
    return True

def test_counterfactual_generator():
    """测试反事实解释生成器"""
    print("\n" + "="*60)
    print("🔄 测试反事实解释生成器")
    print("="*60)
    
    config = create_test_config()
    generator = CounterfactualGenerator(config['explainability'])
    
    # 测试特征
    features = create_sample_features()
    prediction = 0.65
    
    print(f"🎯 原始预测: {prediction:.3f}")
    print("📊 原始特征:")
    for key, value in list(features.items())[:5]:
        print(f"   - {key}: {value:.3f}")
    
    # 生成反事实解释
    print(f"\n🔄 生成反事实解释...")
    counterfactuals = generator.generate_counterfactuals(features, prediction)
    
    print(f"   ✅ 生成了 {len(counterfactuals)} 个反事实解释:")
    
    for i, cf in enumerate(counterfactuals):
        print(f"\n   反事实 {i+1}:")
        print(f"   - 原始预测: {cf.original_prediction:.3f}")
        print(f"   - 反事实预测: {cf.counterfactual_prediction:.3f}")
        print(f"   - 变化幅度: {cf.change_magnitude:.3f}")
        print(f"   - 可行性: {cf.feasibility_score:.3f}")
        print(f"   - 解释: {cf.explanation}")
        print(f"   - 特征变化:")
        for feature, (old_val, new_val) in cf.feature_changes.items():
            change_pct = ((new_val - old_val) / old_val * 100) if old_val != 0 else 0
            print(f"     * {feature}: {old_val:.3f} -> {new_val:.3f} ({change_pct:+.1f}%)")
    
    return True

def test_rule_extractor():
    """测试规则提取器"""
    print("\n" + "="*60)
    print("📏 测试规则提取器")
    print("="*60)
    
    config = create_test_config()
    extractor = RuleExtractor(config['explainability'])
    
    # 添加历史决策记录
    print("📊 添加历史决策记录...")
    for i in range(100):
        features = create_sample_features()
        
        # 创建一些模式
        if features['consensus_ratio'] > 0.7 and features['avg_confidence'] > 0.6:
            prediction = 0.8 + np.random.normal(0, 0.1)
            confidence = 0.75 + np.random.normal(0, 0.1)
            actual = 1
        elif features['consensus_ratio'] < 0.4:
            prediction = 0.3 + np.random.normal(0, 0.1)
            confidence = 0.5 + np.random.normal(0, 0.1)
            actual = 0
        else:
            prediction = 0.5 + np.random.normal(0, 0.2)
            confidence = 0.6 + np.random.normal(0, 0.1)
            actual = 1 if prediction > 0.5 else 0
        
        # 确保值在合理范围内
        prediction = max(0, min(1, prediction))
        confidence = max(0, min(1, confidence))
        
        extractor.add_decision_record(features, prediction, confidence, actual)
    
    print(f"   ✅ 已添加 {len(extractor.decision_history)} 条决策记录")
    
    # 提取规则
    print(f"\n📏 提取决策规则...")
    rules = extractor.extract_rules(min_support=5, min_confidence=0.6)
    
    print(f"   ✅ 提取了 {len(rules)} 条规则:")
    
    for rule in rules:
        print(f"\n   规则: {rule.rule_id}")
        print(f"   - 条件: {rule.condition}")
        print(f"   - 结论: {rule.conclusion}")
        print(f"   - 置信度: {rule.confidence:.3f}")
        print(f"   - 支持度: {rule.support}")
        print(f"   - 覆盖率: {rule.coverage:.3f}")
    
    # 测试规则应用
    test_features = create_sample_features()
    test_features['consensus_ratio'] = 0.75
    test_features['avg_confidence'] = 0.68
    
    print(f"\n🎯 测试规则应用 (consensus_ratio: {test_features['consensus_ratio']:.3f}):")
    applicable_rules = extractor.get_applicable_rules(test_features)
    
    print(f"   ✅ 找到 {len(applicable_rules)} 条适用规则:")
    for rule in applicable_rules:
        print(f"   - {rule.condition} -> {rule.conclusion} (置信度: {rule.confidence:.3f})")
    
    return True

def test_decision_explainer():
    """测试完整的决策解释器"""
    print("\n" + "="*60)
    print("🎯 测试完整决策解释器")
    print("="*60)
    
    config = create_test_config()
    explainer = DecisionExplainer(config)
    
    # 添加一些历史数据
    print("📊 准备历史数据...")
    for i in range(30):
        features = create_sample_features()
        for key in features:
            features[key] += np.random.normal(0, 0.1)
            features[key] = max(0, min(1, features[key]))
        
        prediction = 0.6 + np.random.normal(0, 0.2)
        prediction = max(0, min(1, prediction))
        confidence = 0.65 + np.random.normal(0, 0.15)
        confidence = max(0, min(1, confidence))
        
        explainer.feature_calculator.add_decision_data(features, prediction)
        explainer.rule_extractor.add_decision_record(features, prediction, confidence, 
                                                   1 if prediction > 0.5 else 0)
    
    # 开始决策解释
    decision_id = "comprehensive_test_001"
    features = create_sample_features()
    prediction = 0.72
    confidence = 0.68
    
    print(f"\n🎯 开始决策解释:")
    print(f"   - 决策ID: {decision_id}")
    print(f"   - 预测结果: {prediction:.3f}")
    print(f"   - 置信度: {confidence:.3f}")
    
    # 开始跟踪
    explainer.start_decision_explanation(decision_id)
    
    # 添加层级解释
    explainer.add_layer_explanation(
        "策略层", {"strategies": [1, 0, 1, 1, 0, 1, 1, 0]}, {"consensus": 0.625},
        "8个策略中5个预测正类，共识度62.5%", 0.75, 0.8
    )
    
    explainer.add_layer_explanation(
        "特征层", {"consensus_ratio": 0.625}, features,
        "基于策略共识生成增强特征", 0.68, 0.7
    )
    
    explainer.add_layer_explanation(
        "ML层", features, {"rf_pred": 0.71, "xgb_pred": 0.73},
        "随机森林和XGBoost模型预测", 0.72, 0.9
    )
    
    explainer.add_layer_explanation(
        "融合层", {"rf_pred": 0.71, "xgb_pred": 0.73}, {"fused_pred": 0.72},
        "多模型预测融合", 0.70, 0.85
    )
    
    # 生成完整解释
    print(f"\n📝 生成完整解释...")
    explanation = explainer.generate_explanation(
        decision_id, features, prediction, confidence,
        [ExplanationType.FEATURE_IMPORTANCE, ExplanationType.DECISION_PATH, 
         ExplanationType.COUNTERFACTUAL, ExplanationType.RULE_BASED,
         ExplanationType.CONFIDENCE_BREAKDOWN]
    )
    
    # 显示解释结果
    print(f"\n✅ 解释生成完成!")
    print(f"\n📋 解释总结:")
    print(explanation.summary)
    
    print(f"\n🔍 特征重要性 (前5个):")
    for imp in explanation.feature_importances[:5]:
        print(f"   - {imp.feature_name}: {imp.importance_score:.3f} (排名: {imp.rank})")
    
    if explanation.decision_path:
        print(f"\n🛤️  决策路径:")
        print(f"   - 步骤数: {len(explanation.decision_path.steps)}")
        print(f"   - 处理时间: {explanation.decision_path.total_processing_time:.3f}秒")
        print(f"   - 关键特征: {explanation.decision_path.critical_features}")
    
    print(f"\n🔄 反事实解释 ({len(explanation.counterfactuals)}个):")
    for cf in explanation.counterfactuals[:2]:
        print(f"   - {cf.explanation}")
    
    print(f"\n📏 适用规则 ({len(explanation.rules)}条):")
    for rule in explanation.rules[:2]:
        print(f"   - {rule.condition} -> {rule.conclusion} (置信度: {rule.confidence:.3f})")
    
    print(f"\n🔢 置信度分解:")
    for component, value in explanation.confidence_breakdown.items():
        print(f"   - {component}: {value:.3f}")
    
    print(f"\n📊 层级贡献:")
    for layer, contribution in explanation.layer_contributions.items():
        print(f"   - {layer}: {contribution:.3f}")
    
    print(f"\n📖 详细推理:")
    print(explanation.detailed_reasoning)
    
    # 测试反馈更新
    print(f"\n🔄 测试反馈更新...")
    explainer.update_explanation_feedback(decision_id, 1)  # 实际结果为正类
    print(f"   ✅ 反馈更新完成")
    
    # 获取系统总结
    print(f"\n📊 系统总结:")
    summary = explainer.get_explanation_summary()
    for key, value in summary.items():
        print(f"   - {key}: {value}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始决策解释性系统测试")
    print("="*80)
    
    test_results = []
    
    # 测试各个组件
    tests = [
        ("特征重要性计算器", test_feature_importance_calculator),
        ("决策路径跟踪器", test_decision_path_tracker),
        ("反事实解释生成器", test_counterfactual_generator),
        ("规则提取器", test_rule_extractor),
        ("完整决策解释器", test_decision_explainer)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 测试: {test_name}")
            result = test_func()
            test_results.append((test_name, "✅ 成功" if result else "❌ 失败"))
            print(f"   ✅ {test_name} 测试完成")
        except Exception as e:
            test_results.append((test_name, f"❌ 错误: {str(e)}"))
            print(f"   ❌ {test_name} 测试失败: {str(e)}")
    
    # 显示测试总结
    print("\n" + "="*80)
    print("📊 测试总结")
    print("="*80)
    
    success_count = 0
    for test_name, result in test_results:
        print(f"{result} - {test_name}")
        if "成功" in result:
            success_count += 1
    
    print(f"\n🎯 测试完成: {success_count}/{len(test_results)} 个测试通过")
    
    if success_count == len(test_results):
        print("🎉 所有测试通过！决策解释性系统运行正常！")
    else:
        print("⚠️  部分测试失败，请检查相关组件")

if __name__ == "__main__":
    main()
