#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标收集器

负责收集、计算和报告系统性能指标
"""

import logging
import time
import numpy as np
from typing import Dict, List, Any, Optional
from collections import deque, defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    total_decisions: int
    correct_decisions: int
    consecutive_errors: int
    max_consecutive_errors: int
    avg_confidence: float
    avg_response_time: float


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 指标历史
        self.decision_history = deque(maxlen=10000)
        self.performance_history = deque(maxlen=1000)
        self.response_times = deque(maxlen=1000)
        
        # 实时统计
        self.total_decisions = 0
        self.correct_decisions = 0
        self.consecutive_errors = 0
        self.max_consecutive_errors = 0
        
        # 时间窗口统计
        self.hourly_stats = defaultdict(lambda: {'decisions': 0, 'correct': 0})
        self.daily_stats = defaultdict(lambda: {'decisions': 0, 'correct': 0})
        
        self.logger.info("指标收集器初始化完成")
    
    def record_decision(self, decision_data: Dict[str, Any]):
        """
        记录决策数据
        
        Args:
            decision_data: 决策数据
        """
        timestamp = time.time()
        
        # 记录决策
        decision_record = {
            'decision_id': decision_data.get('decision_id', ''),
            'prediction': decision_data.get('prediction', 0),
            'confidence': decision_data.get('confidence', 0.5),
            'risk_level': decision_data.get('risk_level', 'medium'),
            'should_act': decision_data.get('should_act', True),
            'timestamp': timestamp,
            'response_time': decision_data.get('response_time', 0),
            'actual_result': None,  # 将在反馈时更新
            'is_correct': None
        }
        
        self.decision_history.append(decision_record)
        self.total_decisions += 1
        
        # 记录响应时间
        if decision_record['response_time'] > 0:
            self.response_times.append(decision_record['response_time'])
        
        # 更新时间窗口统计
        self._update_time_window_stats(timestamp)
        
        self.logger.debug(f"记录决策: {decision_record['decision_id']}")
    
    def record_feedback(self, decision_id: str, actual_result: int):
        """
        记录决策反馈
        
        Args:
            decision_id: 决策ID
            actual_result: 实际结果
        """
        # 查找对应的决策记录
        for decision in reversed(self.decision_history):
            if decision['decision_id'] == decision_id:
                decision['actual_result'] = actual_result
                decision['is_correct'] = (decision['prediction'] == actual_result)
                
                # 更新统计
                if decision['is_correct']:
                    self.correct_decisions += 1
                    self.consecutive_errors = 0
                else:
                    self.consecutive_errors += 1
                    self.max_consecutive_errors = max(
                        self.max_consecutive_errors, 
                        self.consecutive_errors
                    )
                
                # 更新时间窗口统计
                hour_key = datetime.fromtimestamp(decision['timestamp']).strftime('%Y-%m-%d-%H')
                day_key = datetime.fromtimestamp(decision['timestamp']).strftime('%Y-%m-%d')
                
                if decision['is_correct']:
                    self.hourly_stats[hour_key]['correct'] += 1
                    self.daily_stats[day_key]['correct'] += 1
                
                self.logger.debug(f"更新反馈: {decision_id}, 结果: {actual_result}")
                break
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前性能指标"""
        if self.total_decisions == 0:
            return {
                'accuracy': 0.0,
                'total_decisions': 0,
                'correct_decisions': 0,
                'consecutive_errors': 0,
                'max_consecutive_errors': 0,
                'avg_confidence': 0.0,
                'avg_response_time': 0.0
            }
        
        # 计算基础指标
        accuracy = self.correct_decisions / self.total_decisions
        
        # 计算近期指标
        recent_decisions = [d for d in self.decision_history if d['is_correct'] is not None][-100:]
        recent_accuracy = 0.0
        if recent_decisions:
            recent_correct = sum(1 for d in recent_decisions if d['is_correct'])
            recent_accuracy = recent_correct / len(recent_decisions)
        
        # 计算平均置信度
        confidences = [d['confidence'] for d in self.decision_history if d['confidence'] is not None]
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        # 计算平均响应时间
        avg_response_time = np.mean(list(self.response_times)) if self.response_times else 0.0
        
        return {
            'accuracy': accuracy,
            'recent_accuracy': recent_accuracy,
            'total_decisions': self.total_decisions,
            'correct_decisions': self.correct_decisions,
            'consecutive_errors': self.consecutive_errors,
            'max_consecutive_errors': self.max_consecutive_errors,
            'avg_confidence': avg_confidence,
            'avg_response_time': avg_response_time
        }
    
    def get_detailed_metrics(self) -> PerformanceMetrics:
        """获取详细性能指标"""
        current = self.get_current_metrics()
        
        # 计算精确率、召回率、F1分数
        precision, recall, f1_score = self._calculate_classification_metrics()
        
        return PerformanceMetrics(
            accuracy=current['accuracy'],
            precision=precision,
            recall=recall,
            f1_score=f1_score,
            total_decisions=current['total_decisions'],
            correct_decisions=current['correct_decisions'],
            consecutive_errors=current['consecutive_errors'],
            max_consecutive_errors=current['max_consecutive_errors'],
            avg_confidence=current['avg_confidence'],
            avg_response_time=current['avg_response_time']
        )
    
    def _calculate_classification_metrics(self) -> tuple:
        """计算分类指标"""
        # 获取有反馈的决策
        decisions_with_feedback = [d for d in self.decision_history if d['is_correct'] is not None]
        
        if not decisions_with_feedback:
            return 0.0, 0.0, 0.0
        
        # 计算混淆矩阵
        tp = sum(1 for d in decisions_with_feedback if d['prediction'] == 1 and d['actual_result'] == 1)
        fp = sum(1 for d in decisions_with_feedback if d['prediction'] == 1 and d['actual_result'] == 0)
        tn = sum(1 for d in decisions_with_feedback if d['prediction'] == 0 and d['actual_result'] == 0)
        fn = sum(1 for d in decisions_with_feedback if d['prediction'] == 0 and d['actual_result'] == 1)
        
        # 计算精确率
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        
        # 计算召回率
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        
        # 计算F1分数
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return precision, recall, f1_score
    
    def get_time_window_stats(self, window: str = 'hour') -> Dict[str, Any]:
        """
        获取时间窗口统计
        
        Args:
            window: 时间窗口类型 ('hour' 或 'day')
            
        Returns:
            时间窗口统计
        """
        if window == 'hour':
            stats = dict(self.hourly_stats)
        elif window == 'day':
            stats = dict(self.daily_stats)
        else:
            return {}
        
        # 计算准确率
        for key, data in stats.items():
            if data['decisions'] > 0:
                data['accuracy'] = data['correct'] / data['decisions']
            else:
                data['accuracy'] = 0.0
        
        return stats
    
    def _update_time_window_stats(self, timestamp: float):
        """更新时间窗口统计"""
        dt = datetime.fromtimestamp(timestamp)
        hour_key = dt.strftime('%Y-%m-%d-%H')
        day_key = dt.strftime('%Y-%m-%d')
        
        self.hourly_stats[hour_key]['decisions'] += 1
        self.daily_stats[day_key]['decisions'] += 1
    
    def generate_performance_report(self) -> str:
        """生成性能报告"""
        metrics = self.get_detailed_metrics()
        current = self.get_current_metrics()
        
        report = f"""
SimpleFusion V8 性能报告
========================

基础指标:
- 总决策数: {metrics.total_decisions}
- 正确决策数: {metrics.correct_decisions}
- 准确率: {metrics.accuracy:.4f}
- 近期准确率: {current['recent_accuracy']:.4f}

分类指标:
- 精确率: {metrics.precision:.4f}
- 召回率: {metrics.recall:.4f}
- F1分数: {metrics.f1_score:.4f}

风险指标:
- 当前连错: {metrics.consecutive_errors}
- 最大连错: {metrics.max_consecutive_errors}

性能指标:
- 平均置信度: {metrics.avg_confidence:.4f}
- 平均响应时间: {metrics.avg_response_time:.3f}ms

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return report
    
    def export_metrics(self, output_file: str):
        """导出指标数据"""
        try:
            import json
            
            export_data = {
                'current_metrics': self.get_current_metrics(),
                'detailed_metrics': self.get_detailed_metrics().__dict__,
                'hourly_stats': dict(self.hourly_stats),
                'daily_stats': dict(self.daily_stats),
                'decision_history': list(self.decision_history),
                'export_time': datetime.now().isoformat()
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"指标数据已导出到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"指标导出失败: {str(e)}")
            raise
    
    def reset_metrics(self):
        """重置所有指标"""
        self.decision_history.clear()
        self.performance_history.clear()
        self.response_times.clear()
        
        self.total_decisions = 0
        self.correct_decisions = 0
        self.consecutive_errors = 0
        self.max_consecutive_errors = 0
        
        self.hourly_stats.clear()
        self.daily_stats.clear()
        
        self.logger.info("所有指标已重置")
