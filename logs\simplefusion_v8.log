2025-06-24 09:01:56,980 - SimpleFusionV8 - INFO - SimpleFusion V8 系统初始化完成
2025-06-24 09:01:56,981 - SimpleFusionV8 - INFO - 开始初始化V8系统组件...
2025-06-24 09:01:56,981 - DataProcessor - INFO - 数据处理器初始化完成
2025-06-24 09:01:56,981 - ModelPersistence - INFO - 模型持久化管理器初始化完成
2025-06-24 09:01:56,981 - MetricsCollector - INFO - 指标收集器初始化完成
2025-06-24 09:01:56,982 - BaseStrategyLayer - INFO - 基础策略层初始化完成
2025-06-24 09:01:56,982 - FeatureEngineeringLayer - INFO - 特征工程层初始化完成
2025-06-24 09:01:57,003 - core.advanced_ensemble - INFO - 初始化元学习器: logistic
2025-06-24 09:01:57,003 - core.advanced_ensemble - INFO - 高级集成学习层初始化完成
2025-06-24 09:01:57,004 - MLModelLayer - INFO - 机器学习模型层初始化完成，加载了 5 个模型
2025-06-24 09:01:57,146 - core.deep_learning_models - INFO - 初始化了 3 个深度学习模型
2025-06-24 09:01:57,146 - core.deep_learning_models - INFO - 深度学习模型层初始化完成，设备: cuda
2025-06-24 09:01:57,146 - AdaptiveDecisionLayer - INFO - 自适应决策层初始化完成
2025-06-24 09:01:57,146 - core.online_learning - INFO - 初始化 sgd 在线学习模型: voting
2025-06-24 09:01:57,146 - core.online_learning - INFO - 初始化 sgd 在线学习模型: xgboost
2025-06-24 09:01:57,147 - core.online_learning - INFO - 初始化 sgd 在线学习模型: neural_network
2025-06-24 09:01:57,147 - core.online_learning - INFO - 初始化 sgd 在线学习模型: meta_learner
2025-06-24 09:01:57,147 - core.online_learning - INFO - 初始化了 4 个在线学习器
2025-06-24 09:01:57,148 - core.online_learning - INFO - 在线学习管理器启动
2025-06-24 09:01:57,148 - core.model_optimization - INFO - 模型优化管理器初始化完成
2025-06-24 09:01:57,148 - core.multi_objective_optimization - INFO - 多目标决策管理器初始化完成
2025-06-24 09:01:57,148 - core.intelligent_risk_management - INFO - 智能风险管理器初始化完成
2025-06-24 09:01:57,148 - core.decision_explainability - INFO - 决策解释器初始化完成
2025-06-24 09:01:57,148 - core.adaptive_strategy_tuning - INFO - 自适应策略调优器初始化完成
2025-06-24 09:01:57,148 - core.streak_selector - INFO - 连胜连败策略选择器初始化完成
2025-06-24 09:01:57,148 - core.streak_selector - INFO - 配置参数: 窗口大小=[3, 5, 10, 20], 连胜奖励率=0.05
2025-06-24 09:01:57,149 - SimpleFusionV8 - INFO - 发现已保存的模型，正在加载...
2025-06-24 09:01:57,149 - SimpleFusionV8 - INFO - 模型加载完成
2025-06-24 09:01:57,149 - core.deep_learning_models - INFO - 模型 lstm 的标准化器拟合完成
2025-06-24 09:01:57,150 - core.deep_learning_models - INFO - 模型 gru 的标准化器拟合完成
2025-06-24 09:01:57,150 - core.deep_learning_models - INFO - 模型 transformer 的标准化器拟合完成
2025-06-24 09:01:57,150 - core.deep_learning_models - INFO - 深度学习模型预热完成
2025-06-24 09:01:57,150 - SimpleFusionV8 - INFO - 深度学习模型预热完成
2025-06-24 09:01:57,150 - SimpleFusionV8 - INFO - V8系统组件初始化完成
2025-06-24 09:01:58,038 - DataProcessor - INFO - 加载了 23173 条历史数据
2025-06-24 09:01:58,039 - DataProcessor - INFO - 开始数据清洗，原始数据量: 23173
2025-06-24 09:01:58,083 - DataProcessor - INFO - 数据清洗完成:
2025-06-24 09:01:58,083 - DataProcessor - INFO -   原始数据: 23173 条
2025-06-24 09:01:58,083 - DataProcessor - INFO -   清洗后: 23173 条
2025-06-24 09:01:58,083 - DataProcessor - INFO -   移除: 0 条
2025-06-24 09:01:58,083 - DataProcessor - INFO -   保留率: 100.00%
2025-06-24 09:01:58,089 - DataProcessor - INFO - 数据质量检查完成: good
2025-06-24 09:01:58,089 - DataProcessor - INFO - 准备了 23173 条训练数据，特征维度: 3
2025-06-24 09:02:35,795 - Model.xgboost - INFO - XGBoost模型训练完成，验证准确率: 0.5604
2025-06-24 09:02:37,232 - Model.neural_network - INFO - 神经网络训练完成，训练准确率: 0.570
2025-06-24 09:02:43,911 - Model.strategy_selector - INFO - 策略选择模型训练完成，训练准确率: 0.617
2025-06-24 09:02:44,059 - Model.neural_network - WARNING - 神经网络预测失败: X has 24 features, but MLPClassifier is expecting 60 features as input.
2025-06-24 09:02:44,060 - Model.strategy_selector - WARNING - 策略选择模型预测失败: X has 21 features, but RandomForestClassifier is expecting 9 features as input.
2025-06-24 09:02:44,063 - ModelPersistence - INFO - 保存模型: voting
2025-06-24 09:02:44,068 - ModelPersistence - ERROR - 模型保存失败: cannot pickle 'module' object
2025-06-24 09:02:44,068 - SimpleFusionV8 - ERROR - 模型保存失败: cannot pickle 'module' object
2025-06-24 09:28:31,667 - SimpleFusionV8 - INFO - SimpleFusion V8 系统初始化完成
2025-06-24 09:28:31,667 - SimpleFusionV8 - INFO - 开始初始化V8系统组件...
2025-06-24 09:28:31,667 - DataProcessor - INFO - 数据处理器初始化完成
2025-06-24 09:28:31,667 - ModelPersistence - INFO - 模型持久化管理器初始化完成
2025-06-24 09:28:31,667 - MetricsCollector - INFO - 指标收集器初始化完成
2025-06-24 09:28:31,668 - BaseStrategyLayer - INFO - 基础策略层初始化完成
2025-06-24 09:28:31,668 - FeatureEngineeringLayer - INFO - 特征工程层初始化完成
2025-06-24 09:28:31,689 - core.advanced_ensemble - INFO - 初始化元学习器: logistic
2025-06-24 09:28:31,689 - core.advanced_ensemble - INFO - 高级集成学习层初始化完成
2025-06-24 09:28:31,689 - MLModelLayer - INFO - 机器学习模型层初始化完成，加载了 5 个模型
2025-06-24 09:28:31,821 - core.deep_learning_models - INFO - 初始化了 3 个深度学习模型
2025-06-24 09:28:31,821 - core.deep_learning_models - INFO - 深度学习模型层初始化完成，设备: cuda
2025-06-24 09:28:31,821 - AdaptiveDecisionLayer - INFO - 自适应决策层初始化完成
2025-06-24 09:28:31,822 - core.online_learning - INFO - 初始化 sgd 在线学习模型: voting
2025-06-24 09:28:31,822 - core.online_learning - INFO - 初始化 sgd 在线学习模型: xgboost
2025-06-24 09:28:31,822 - core.online_learning - INFO - 初始化 sgd 在线学习模型: neural_network
2025-06-24 09:28:31,822 - core.online_learning - INFO - 初始化 sgd 在线学习模型: meta_learner
2025-06-24 09:28:31,822 - core.online_learning - INFO - 初始化了 4 个在线学习器
2025-06-24 09:28:31,824 - core.online_learning - INFO - 在线学习管理器启动
2025-06-24 09:28:31,824 - core.model_optimization - INFO - 模型优化管理器初始化完成
2025-06-24 09:28:31,824 - core.multi_objective_optimization - INFO - 多目标决策管理器初始化完成
2025-06-24 09:28:31,824 - core.intelligent_risk_management - INFO - 智能风险管理器初始化完成
2025-06-24 09:28:31,824 - core.decision_explainability - INFO - 决策解释器初始化完成
2025-06-24 09:28:31,824 - core.adaptive_strategy_tuning - INFO - 自适应策略调优器初始化完成
2025-06-24 09:28:31,824 - core.streak_selector - INFO - 连胜连败策略选择器初始化完成
2025-06-24 09:28:31,824 - core.streak_selector - INFO - 配置参数: 窗口大小=[3, 5, 10, 20], 连胜奖励率=0.05
2025-06-24 09:28:31,825 - SimpleFusionV8 - INFO - 发现已保存的模型，正在加载...
2025-06-24 09:28:31,825 - SimpleFusionV8 - INFO - 模型加载完成
2025-06-24 09:28:31,825 - core.deep_learning_models - INFO - 模型 lstm 的标准化器拟合完成
2025-06-24 09:28:31,827 - core.deep_learning_models - INFO - 模型 gru 的标准化器拟合完成
2025-06-24 09:28:31,827 - core.deep_learning_models - INFO - 模型 transformer 的标准化器拟合完成
2025-06-24 09:28:31,827 - core.deep_learning_models - INFO - 深度学习模型预热完成
2025-06-24 09:28:31,827 - SimpleFusionV8 - INFO - 深度学习模型预热完成
2025-06-24 09:28:31,827 - SimpleFusionV8 - INFO - V8系统组件初始化完成
2025-06-24 09:28:32,708 - DataProcessor - INFO - 加载了 23173 条历史数据
2025-06-24 09:28:32,708 - DataProcessor - INFO - 开始数据清洗，原始数据量: 23173
2025-06-24 09:28:32,745 - DataProcessor - INFO - 数据清洗完成:
2025-06-24 09:28:32,747 - DataProcessor - INFO -   原始数据: 23173 条
2025-06-24 09:28:32,747 - DataProcessor - INFO -   清洗后: 23173 条
2025-06-24 09:28:32,747 - DataProcessor - INFO -   移除: 0 条
2025-06-24 09:28:32,747 - DataProcessor - INFO -   保留率: 100.00%
2025-06-24 09:28:32,751 - DataProcessor - INFO - 数据质量检查完成: good
2025-06-24 09:28:32,752 - DataProcessor - INFO - 准备了 23173 条训练数据，特征维度: 3
2025-06-24 09:29:12,387 - Model.xgboost - INFO - XGBoost模型训练完成，验证准确率: 0.5623
2025-06-24 09:29:14,125 - Model.neural_network - INFO - 神经网络训练完成，训练准确率: 0.570
2025-06-24 09:29:29,274 - Model.strategy_selector - INFO - 策略选择模型训练完成，训练准确率: 0.584
2025-06-24 09:29:29,457 - ModelPersistence - INFO - 保存模型: voting
2025-06-24 09:29:29,460 - ModelPersistence - ERROR - 模型保存失败: cannot pickle 'module' object
2025-06-24 09:29:29,460 - SimpleFusionV8 - ERROR - 模型保存失败: cannot pickle 'module' object
2025-06-24 09:52:44,364 - SimpleFusionV8 - INFO - SimpleFusion V8 系统初始化完成
2025-06-24 09:52:44,364 - SimpleFusionV8 - INFO - 开始初始化V8系统组件...
2025-06-24 09:52:44,364 - DataProcessor - INFO - 数据处理器初始化完成
2025-06-24 09:52:44,365 - ModelPersistence - INFO - 模型持久化管理器初始化完成
2025-06-24 09:52:44,365 - MetricsCollector - INFO - 指标收集器初始化完成
2025-06-24 09:52:44,365 - BaseStrategyLayer - INFO - 基础策略层初始化完成
2025-06-24 09:52:44,365 - FeatureEngineeringLayer - INFO - 特征工程层初始化完成
2025-06-24 09:52:44,394 - core.advanced_ensemble - INFO - 初始化元学习器: logistic
2025-06-24 09:52:44,394 - core.advanced_ensemble - INFO - 高级集成学习层初始化完成
2025-06-24 09:52:44,394 - MLModelLayer - INFO - 机器学习模型层初始化完成，加载了 5 个模型
2025-06-24 09:52:44,544 - core.deep_learning_models - INFO - 初始化了 3 个深度学习模型
2025-06-24 09:52:44,544 - core.deep_learning_models - INFO - 深度学习模型层初始化完成，设备: cuda
2025-06-24 09:52:44,544 - AdaptiveDecisionLayer - INFO - 自适应决策层初始化完成
2025-06-24 09:52:44,545 - core.online_learning - INFO - 初始化 sgd 在线学习模型: voting
2025-06-24 09:52:44,545 - core.online_learning - INFO - 初始化 sgd 在线学习模型: xgboost
2025-06-24 09:52:44,545 - core.online_learning - INFO - 初始化 sgd 在线学习模型: neural_network
2025-06-24 09:52:44,545 - core.online_learning - INFO - 初始化 sgd 在线学习模型: meta_learner
2025-06-24 09:52:44,545 - core.online_learning - INFO - 初始化了 4 个在线学习器
2025-06-24 09:52:44,546 - core.online_learning - INFO - 在线学习管理器启动
2025-06-24 09:52:44,546 - core.model_optimization - INFO - 模型优化管理器初始化完成
2025-06-24 09:52:44,546 - core.multi_objective_optimization - INFO - 多目标决策管理器初始化完成
2025-06-24 09:52:44,546 - core.intelligent_risk_management - INFO - 智能风险管理器初始化完成
2025-06-24 09:52:44,546 - core.decision_explainability - INFO - 决策解释器初始化完成
2025-06-24 09:52:44,546 - core.adaptive_strategy_tuning - INFO - 自适应策略调优器初始化完成
2025-06-24 09:52:44,546 - core.streak_selector - INFO - 连胜连败策略选择器初始化完成
2025-06-24 09:52:44,546 - core.streak_selector - INFO - 配置参数: 窗口大小=[3, 5, 10, 20], 连胜奖励率=0.05
2025-06-24 09:52:44,547 - SimpleFusionV8 - INFO - 发现已保存的模型，正在加载...
2025-06-24 09:52:44,547 - SimpleFusionV8 - INFO - 模型加载完成
2025-06-24 09:52:44,547 - core.deep_learning_models - INFO - 模型 lstm 的标准化器拟合完成
2025-06-24 09:52:44,549 - core.deep_learning_models - INFO - 模型 gru 的标准化器拟合完成
2025-06-24 09:52:44,549 - core.deep_learning_models - INFO - 模型 transformer 的标准化器拟合完成
2025-06-24 09:52:44,549 - core.deep_learning_models - INFO - 深度学习模型预热完成
2025-06-24 09:52:44,549 - SimpleFusionV8 - INFO - 深度学习模型预热完成
2025-06-24 09:52:44,549 - SimpleFusionV8 - INFO - V8系统组件初始化完成
2025-06-24 09:52:45,477 - DataProcessor - INFO - 加载了 26537 条历史数据
2025-06-24 09:52:45,477 - DataProcessor - INFO - 开始数据清洗，原始数据量: 26537
2025-06-24 09:52:45,479 - DataProcessor - INFO - strategy_1 列发现 35 个无效值
2025-06-24 09:52:45,490 - DataProcessor - INFO - strategy_2 列发现 2450 个无效值
2025-06-24 09:52:45,501 - DataProcessor - INFO - strategy_3 列发现 1351 个无效值
2025-06-24 09:52:45,523 - DataProcessor - INFO - strategy_5 列发现 3100 个无效值
2025-06-24 09:52:45,533 - DataProcessor - INFO - strategy_6 列发现 3100 个无效值
2025-06-24 09:52:45,542 - DataProcessor - INFO - strategy_7 列发现 3100 个无效值
2025-06-24 09:52:45,552 - DataProcessor - INFO - strategy_8 列发现 3364 个无效值
2025-06-24 09:52:45,562 - DataProcessor - INFO - 移除 3364 条包含策略空值的数据
2025-06-24 09:52:45,579 - DataProcessor - INFO - 数据清洗完成:
2025-06-24 09:52:45,579 - DataProcessor - INFO -   原始数据: 26537 条
2025-06-24 09:52:45,579 - DataProcessor - INFO -   清洗后: 23173 条
2025-06-24 09:52:45,579 - DataProcessor - INFO -   移除: 3364 条
2025-06-24 09:52:45,579 - DataProcessor - INFO -   保留率: 87.32%
2025-06-24 09:52:45,588 - DataProcessor - INFO - 数据质量检查完成: good
2025-06-24 09:52:45,589 - DataProcessor - INFO - 准备了 23173 条训练数据，特征维度: 8
2025-06-24 09:53:26,113 - Model.xgboost - INFO - XGBoost模型训练完成，验证准确率: 0.5596
2025-06-24 09:53:28,415 - Model.neural_network - INFO - 神经网络训练完成，训练准确率: 0.570
2025-06-24 09:54:03,281 - Model.strategy_selector - INFO - 策略选择模型训练完成，训练准确率: 0.391
2025-06-24 09:54:03,538 - ModelPersistence - INFO - 保存模型: voting
2025-06-24 09:54:03,545 - ModelPersistence - ERROR - 模型保存失败: cannot pickle 'module' object
2025-06-24 09:54:03,545 - SimpleFusionV8 - ERROR - 模型保存失败: cannot pickle 'module' object
