#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在线学习机制

实现增量学习和模型自适应更新，支持实时环境变化适应
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import time
from collections import deque
import threading
import queue

try:
    from sklearn.linear_model import SGDClassifier, PassiveAggressiveClassifier
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics import accuracy_score
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


@dataclass
class OnlineLearningUpdate:
    """在线学习更新记录"""
    timestamp: float
    features: Dict[str, float]
    prediction: float
    actual_result: int
    model_name: str
    accuracy_before: float
    accuracy_after: float
    update_type: str  # 'incremental', 'batch', 'drift_adaptation'


@dataclass
class ConceptDriftDetection:
    """概念漂移检测结果"""
    drift_detected: bool
    drift_score: float
    drift_type: str  # 'gradual', 'sudden', 'none'
    confidence: float
    recommendation: str


class IncrementalLearner:
    """增量学习器"""
    
    def __init__(self, model_name: str, config: Dict[str, Any]):
        """
        初始化增量学习器
        
        Args:
            model_name: 模型名称
            config: 配置字典
        """
        self.model_name = model_name
        self.config = config.get('incremental', {})
        self.logger = logging.getLogger(__name__)
        
        # 在线模型
        self.online_model = None
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        self.is_fitted = False
        
        # 性能监控
        self.performance_window = deque(maxlen=self.config.get('performance_window', 100))
        self.update_history = deque(maxlen=self.config.get('history_size', 1000))
        
        # 学习参数
        self.learning_rate = self.config.get('learning_rate', 0.01)
        self.batch_size = self.config.get('batch_size', 10)
        self.update_threshold = self.config.get('update_threshold', 0.05)
        
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化在线学习模型"""
        if not SKLEARN_AVAILABLE:
            self.logger.warning("scikit-learn不可用，使用简化在线学习")
            self.online_model = {'type': 'simple', 'weights': None, 'bias': 0.0}
            return
        
        model_type = self.config.get('model_type', 'sgd')
        
        if model_type == 'sgd':
            self.online_model = SGDClassifier(
                loss='log_loss',
                learning_rate='adaptive',
                eta0=self.learning_rate,
                random_state=42
            )
        elif model_type == 'passive_aggressive':
            self.online_model = PassiveAggressiveClassifier(
                C=1.0,
                random_state=42
            )
        else:
            # 默认使用SGD
            self.online_model = SGDClassifier(
                loss='log_loss',
                learning_rate='adaptive',
                eta0=self.learning_rate,
                random_state=42
            )
        
        self.logger.info(f"初始化 {model_type} 在线学习模型: {self.model_name}")
    
    def partial_fit(self, features: Dict[str, float], actual_result: int) -> OnlineLearningUpdate:
        """
        增量学习更新
        
        Args:
            features: 特征字典
            actual_result: 实际结果
            
        Returns:
            更新记录
        """
        # 准备特征向量
        feature_vector = self._features_to_vector(features)
        X = np.array(feature_vector).reshape(1, -1)
        y = np.array([actual_result])
        
        # 记录更新前的性能
        accuracy_before = self._get_current_accuracy()
        
        if SKLEARN_AVAILABLE and hasattr(self.online_model, 'partial_fit'):
            # 使用scikit-learn的增量学习
            update_record = self._sklearn_partial_fit(X, y, features, actual_result, accuracy_before)
        else:
            # 使用简化的增量学习
            update_record = self._simple_partial_fit(X, y, features, actual_result, accuracy_before)
        
        # 记录更新历史
        self.update_history.append(update_record)
        
        return update_record
    
    def _sklearn_partial_fit(self, X: np.ndarray, y: np.ndarray, 
                           features: Dict[str, float], actual_result: int, 
                           accuracy_before: float) -> OnlineLearningUpdate:
        """使用scikit-learn进行增量学习"""
        try:
            # 标准化特征
            if self.scaler and self.is_fitted:
                X_scaled = self.scaler.transform(X)
            else:
                if self.scaler:
                    X_scaled = self.scaler.fit_transform(X)
                else:
                    X_scaled = X
            
            # 增量学习
            if not self.is_fitted:
                # 首次训练
                self.online_model.partial_fit(X_scaled, y, classes=[0, 1])
                self.is_fitted = True
                prediction = 0.5  # 首次预测使用默认值
            else:
                # 先预测，再更新
                prediction_proba = self.online_model.predict_proba(X_scaled)[0]
                prediction = prediction_proba[1] if len(prediction_proba) > 1 else prediction_proba[0]
                
                # 增量更新
                self.online_model.partial_fit(X_scaled, y)
            
            # 记录性能
            self.performance_window.append(actual_result == (1 if prediction >= 0.5 else 0))
            accuracy_after = self._get_current_accuracy()
            
            return OnlineLearningUpdate(
                timestamp=time.time(),
                features=features,
                prediction=prediction,
                actual_result=actual_result,
                model_name=self.model_name,
                accuracy_before=accuracy_before,
                accuracy_after=accuracy_after,
                update_type='incremental'
            )
            
        except Exception as e:
            self.logger.error(f"增量学习失败: {str(e)}")
            return self._simple_partial_fit(X, y, features, actual_result, accuracy_before)
    
    def _simple_partial_fit(self, X: np.ndarray, y: np.ndarray,
                          features: Dict[str, float], actual_result: int,
                          accuracy_before: float) -> OnlineLearningUpdate:
        """简化的增量学习"""
        if self.online_model['weights'] is None:
            # 初始化权重
            self.online_model['weights'] = np.random.randn(X.shape[1]) * 0.01
        
        # 简单的梯度下降更新
        prediction = self._simple_predict(X[0])
        error = actual_result - prediction
        
        # 更新权重
        self.online_model['weights'] += self.learning_rate * error * X[0]
        self.online_model['bias'] += self.learning_rate * error
        
        # 记录性能
        self.performance_window.append(actual_result == (1 if prediction >= 0.5 else 0))
        accuracy_after = self._get_current_accuracy()
        
        return OnlineLearningUpdate(
            timestamp=time.time(),
            features=features,
            prediction=prediction,
            actual_result=actual_result,
            model_name=self.model_name,
            accuracy_before=accuracy_before,
            accuracy_after=accuracy_after,
            update_type='incremental_simple'
        )
    
    def _simple_predict(self, feature_vector: np.ndarray) -> float:
        """简化预测"""
        if self.online_model['weights'] is None:
            return 0.5
        
        linear_output = np.dot(feature_vector, self.online_model['weights']) + self.online_model['bias']
        return 1 / (1 + np.exp(-linear_output))  # sigmoid
    
    def predict(self, features: Dict[str, float]) -> float:
        """
        在线预测
        
        Args:
            features: 特征字典
            
        Returns:
            预测概率
        """
        feature_vector = self._features_to_vector(features)
        X = np.array(feature_vector).reshape(1, -1)
        
        if SKLEARN_AVAILABLE and hasattr(self.online_model, 'predict_proba') and self.is_fitted:
            try:
                if self.scaler:
                    X_scaled = self.scaler.transform(X)
                else:
                    X_scaled = X
                
                prediction_proba = self.online_model.predict_proba(X_scaled)[0]
                return prediction_proba[1] if len(prediction_proba) > 1 else prediction_proba[0]
            except:
                return self._simple_predict(X[0])
        else:
            return self._simple_predict(X[0])
    
    def _features_to_vector(self, features: Dict[str, float]) -> List[float]:
        """将特征字典转换为向量"""
        # 定义特征顺序
        feature_names = [
            'consensus_ratio', 'weighted_consensus', 'avg_confidence',
            'min_confidence', 'max_confidence', 'confidence_std',
            'total_divergence', 'max_divergence',
            'strategy_1_prediction', 'strategy_2_prediction', 'strategy_6_prediction',
            'strategy_1_confidence', 'strategy_2_confidence', 'strategy_6_confidence',
            'win_rate_10', 'win_rate_5', 'current_streak',
            'strategy_1_weight', 'strategy_2_weight', 'strategy_6_weight'
        ]
        
        feature_vector = []
        for name in feature_names:
            feature_vector.append(features.get(name, 0.0))
        
        return feature_vector
    
    def _get_current_accuracy(self) -> float:
        """获取当前准确率"""
        if len(self.performance_window) == 0:
            return 0.5
        
        return sum(self.performance_window) / len(self.performance_window)
    
    def get_learning_stats(self) -> Dict[str, Any]:
        """获取学习统计信息"""
        return {
            'model_name': self.model_name,
            'is_fitted': self.is_fitted,
            'current_accuracy': self._get_current_accuracy(),
            'total_updates': len(self.update_history),
            'performance_window_size': len(self.performance_window),
            'learning_rate': self.learning_rate,
            'recent_updates': len([u for u in self.update_history if time.time() - u.timestamp < 3600])  # 最近1小时
        }


class ConceptDriftDetector:
    """概念漂移检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化概念漂移检测器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('drift_detection', {})
        self.logger = logging.getLogger(__name__)
        
        # 检测参数
        self.window_size = self.config.get('window_size', 100)
        self.drift_threshold = self.config.get('drift_threshold', 0.05)
        self.warning_threshold = self.config.get('warning_threshold', 0.03)
        
        # 性能历史
        self.performance_history = deque(maxlen=self.window_size * 2)
        self.drift_history = deque(maxlen=50)
        
        # 统计量
        self.baseline_accuracy = None
        self.current_accuracy = None
        
    def add_performance_point(self, prediction: float, actual_result: int):
        """添加性能数据点"""
        is_correct = (prediction >= 0.5) == (actual_result == 1)
        self.performance_history.append({
            'timestamp': time.time(),
            'correct': is_correct,
            'prediction': prediction,
            'actual': actual_result
        })
        
        # 更新统计量
        self._update_statistics()
    
    def _update_statistics(self):
        """更新统计量"""
        if len(self.performance_history) < self.window_size:
            return
        
        # 计算基线准确率（较早的窗口）
        if len(self.performance_history) >= self.window_size * 2:
            baseline_window = list(self.performance_history)[-self.window_size*2:-self.window_size]
            self.baseline_accuracy = sum(p['correct'] for p in baseline_window) / len(baseline_window)
        
        # 计算当前准确率（最近的窗口）
        current_window = list(self.performance_history)[-self.window_size:]
        self.current_accuracy = sum(p['correct'] for p in current_window) / len(current_window)
    
    def detect_drift(self) -> ConceptDriftDetection:
        """
        检测概念漂移
        
        Returns:
            概念漂移检测结果
        """
        if self.baseline_accuracy is None or self.current_accuracy is None:
            return ConceptDriftDetection(
                drift_detected=False,
                drift_score=0.0,
                drift_type='none',
                confidence=0.0,
                recommendation="数据不足，无法检测漂移"
            )
        
        # 计算性能下降
        performance_drop = self.baseline_accuracy - self.current_accuracy
        
        # 检测漂移
        if performance_drop > self.drift_threshold:
            drift_detected = True
            drift_type = 'sudden' if performance_drop > self.drift_threshold * 2 else 'gradual'
            confidence = min(1.0, performance_drop / self.drift_threshold)
            recommendation = "建议重新训练模型或调整参数"
        elif performance_drop > self.warning_threshold:
            drift_detected = False
            drift_type = 'warning'
            confidence = performance_drop / self.warning_threshold
            recommendation = "性能下降，建议密切监控"
        else:
            drift_detected = False
            drift_type = 'none'
            confidence = 0.0
            recommendation = "模型性能稳定"
        
        # 记录检测结果
        detection_result = ConceptDriftDetection(
            drift_detected=drift_detected,
            drift_score=performance_drop,
            drift_type=drift_type,
            confidence=confidence,
            recommendation=recommendation
        )
        
        self.drift_history.append({
            'timestamp': time.time(),
            'detection': detection_result
        })
        
        if drift_detected:
            self.logger.warning(f"检测到概念漂移: {drift_type}, 得分: {performance_drop:.4f}")
        
        return detection_result


class OnlineLearningManager:
    """在线学习管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化在线学习管理器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('online_learning', {})
        self.logger = logging.getLogger(__name__)
        
        # 增量学习器
        self.learners = {}
        
        # 概念漂移检测器
        self.drift_detector = ConceptDriftDetector(config)
        
        # 更新队列
        self.update_queue = queue.Queue()
        self.is_running = False
        self.update_thread = None
        
        # 批量更新
        self.batch_updates = deque(maxlen=self.config.get('batch_size', 50))
        self.last_batch_update = time.time()
        self.batch_interval = self.config.get('batch_interval', 300)  # 5分钟
        
        self._initialize_learners()
    
    def _initialize_learners(self):
        """初始化学习器"""
        model_names = ['voting', 'xgboost', 'neural_network', 'meta_learner']
        
        for model_name in model_names:
            if self.config.get(model_name, {}).get('enabled', True):
                self.learners[model_name] = IncrementalLearner(model_name, self.config)
        
        self.logger.info(f"初始化了 {len(self.learners)} 个在线学习器")
    
    def start_online_learning(self):
        """启动在线学习"""
        if self.is_running:
            return
        
        self.is_running = True
        self.update_thread = threading.Thread(target=self._update_worker, daemon=True)
        self.update_thread.start()
        
        self.logger.info("在线学习管理器启动")
    
    def stop_online_learning(self):
        """停止在线学习"""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        
        self.logger.info("在线学习管理器停止")
    
    def add_feedback(self, model_predictions: Dict[str, float], 
                    features: Dict[str, float], actual_result: int):
        """
        添加反馈数据
        
        Args:
            model_predictions: 模型预测字典
            features: 特征字典
            actual_result: 实际结果
        """
        # 添加到更新队列
        update_data = {
            'model_predictions': model_predictions,
            'features': features,
            'actual_result': actual_result,
            'timestamp': time.time()
        }
        
        self.update_queue.put(update_data)
        
        # 添加到批量更新
        self.batch_updates.append(update_data)
        
        # 更新概念漂移检测器
        if model_predictions:
            avg_prediction = sum(model_predictions.values()) / len(model_predictions)
            self.drift_detector.add_performance_point(avg_prediction, actual_result)
    
    def _update_worker(self):
        """更新工作线程"""
        while self.is_running:
            try:
                # 处理增量更新
                try:
                    update_data = self.update_queue.get(timeout=1.0)
                    self._process_incremental_update(update_data)
                except queue.Empty:
                    pass
                
                # 处理批量更新
                if time.time() - self.last_batch_update > self.batch_interval:
                    self._process_batch_update()
                
            except Exception as e:
                self.logger.error(f"在线学习更新失败: {str(e)}")
    
    def _process_incremental_update(self, update_data: Dict[str, Any]):
        """处理增量更新"""
        features = update_data['features']
        actual_result = update_data['actual_result']
        
        # 更新每个学习器
        for learner_name, learner in self.learners.items():
            try:
                update_record = learner.partial_fit(features, actual_result)
                
                # 记录显著的性能变化
                accuracy_change = abs(update_record.accuracy_after - update_record.accuracy_before)
                if accuracy_change > 0.05:
                    self.logger.info(f"{learner_name} 性能变化: "
                                   f"{update_record.accuracy_before:.3f} -> "
                                   f"{update_record.accuracy_after:.3f}")
                
            except Exception as e:
                self.logger.error(f"学习器 {learner_name} 更新失败: {str(e)}")
    
    def _process_batch_update(self):
        """处理批量更新"""
        if len(self.batch_updates) < 10:  # 批量数据不足
            return
        
        self.logger.info(f"执行批量更新，数据量: {len(self.batch_updates)}")
        
        # 检测概念漂移
        drift_detection = self.drift_detector.detect_drift()
        
        if drift_detection.drift_detected:
            self.logger.warning(f"检测到概念漂移: {drift_detection.drift_type}")
            self.logger.warning(f"建议: {drift_detection.recommendation}")
            
            # 可以在这里实现模型重置或重新训练逻辑
            # self._handle_concept_drift(drift_detection)
        
        self.last_batch_update = time.time()
    
    def get_online_predictions(self, features: Dict[str, float]) -> Dict[str, float]:
        """
        获取在线学习预测
        
        Args:
            features: 特征字典
            
        Returns:
            在线预测字典
        """
        predictions = {}
        
        for learner_name, learner in self.learners.items():
            try:
                prediction = learner.predict(features)
                predictions[f"{learner_name}_online"] = prediction
            except Exception as e:
                self.logger.error(f"在线预测失败 {learner_name}: {str(e)}")
                predictions[f"{learner_name}_online"] = 0.5
        
        return predictions
    
    def get_learning_status(self) -> Dict[str, Any]:
        """获取学习状态"""
        learner_stats = {}
        for name, learner in self.learners.items():
            learner_stats[name] = learner.get_learning_stats()
        
        drift_status = self.drift_detector.detect_drift()
        
        return {
            'is_running': self.is_running,
            'total_learners': len(self.learners),
            'queue_size': self.update_queue.qsize(),
            'batch_updates_count': len(self.batch_updates),
            'last_batch_update': self.last_batch_update,
            'drift_detection': {
                'drift_detected': drift_status.drift_detected,
                'drift_score': drift_status.drift_score,
                'drift_type': drift_status.drift_type,
                'recommendation': drift_status.recommendation
            },
            'learner_stats': learner_stats
        }
