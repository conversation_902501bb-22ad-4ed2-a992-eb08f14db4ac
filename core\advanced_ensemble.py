#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级集成学习模块

实现Stacking、Blending、动态权重等高级集成学习方法
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import time
from collections import deque

try:
    from sklearn.model_selection import KFold, StratifiedKFold
    from sklearn.linear_model import LogisticRegression
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics import accuracy_score, log_loss
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


@dataclass
class EnsemblePrediction:
    """集成预测结果"""
    prediction: float
    confidence: float
    reasoning: str
    method: str
    model_weights: Optional[Dict[str, float]] = None
    model_predictions: Optional[Dict[str, float]] = None


class StackingEnsemble:
    """Stacking集成学习"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Stacking集成
        
        Args:
            config: 配置字典
        """
        self.config = config.get('stacking', {})
        self.logger = logging.getLogger(__name__)
        
        # 元学习器
        self.meta_learner = None
        self.is_trained = False
        
        # 交叉验证配置
        self.cv_folds = self.config.get('cv_folds', 5)
        self.random_state = self.config.get('random_state', 42)
        
        # 训练数据缓存
        self.training_data = deque(maxlen=1000)
        
        self._initialize_meta_learner()
    
    def _initialize_meta_learner(self):
        """初始化元学习器"""
        if not SKLEARN_AVAILABLE:
            self.logger.warning("scikit-learn不可用，使用简化元学习器")
            self.meta_learner = {'type': 'simple', 'weights': None}
            return
        
        meta_type = self.config.get('meta_learner', 'logistic')
        
        if meta_type == 'logistic':
            self.meta_learner = LogisticRegression(
                random_state=self.random_state,
                max_iter=1000
            )
        elif meta_type == 'random_forest':
            self.meta_learner = RandomForestClassifier(
                n_estimators=50,
                random_state=self.random_state
            )
        else:
            # 默认使用逻辑回归
            self.meta_learner = LogisticRegression(
                random_state=self.random_state,
                max_iter=1000
            )
        
        self.logger.info(f"初始化元学习器: {meta_type}")
    
    def train(self, base_predictions: List[Dict[str, float]], labels: List[int]):
        """
        训练Stacking模型
        
        Args:
            base_predictions: 基础模型预测列表
            labels: 真实标签
        """
        if not SKLEARN_AVAILABLE:
            self._train_simple(base_predictions, labels)
            return
        
        if len(base_predictions) < self.cv_folds:
            self.logger.warning(f"训练数据不足，需要至少 {self.cv_folds} 个样本")
            return
        
        try:
            # 准备训练数据
            X_meta, y_meta = self._prepare_meta_features(base_predictions, labels)
            
            if len(X_meta) == 0:
                self.logger.warning("元特征为空，无法训练")
                return
            
            # 训练元学习器
            self.meta_learner.fit(X_meta, y_meta)
            self.is_trained = True
            
            # 评估性能
            train_score = self.meta_learner.score(X_meta, y_meta)
            self.logger.info(f"Stacking模型训练完成，训练准确率: {train_score:.4f}")
            
        except Exception as e:
            self.logger.error(f"Stacking训练失败: {str(e)}")
    
    def _prepare_meta_features(self, base_predictions: List[Dict[str, float]], 
                              labels: List[int]) -> Tuple[np.ndarray, np.ndarray]:
        """准备元特征"""
        if not base_predictions:
            return np.array([]), np.array([])
        
        # 获取模型名称
        model_names = list(base_predictions[0].keys())
        
        # 构建元特征矩阵
        meta_features = []
        valid_labels = []
        
        for i, (predictions, label) in enumerate(zip(base_predictions, labels)):
            # 基础预测作为元特征
            features = [predictions.get(name, 0.5) for name in model_names]
            
            # 添加交互特征
            if len(features) >= 2:
                # 预测方差
                features.append(np.var(features))
                # 预测均值
                features.append(np.mean(features))
                # 最大最小差
                features.append(max(features[:-2]) - min(features[:-2]))
            
            meta_features.append(features)
            valid_labels.append(label)
        
        return np.array(meta_features), np.array(valid_labels)
    
    def _train_simple(self, base_predictions: List[Dict[str, float]], labels: List[int]):
        """简化训练（当scikit-learn不可用时）"""
        if not base_predictions:
            return
        
        # 计算简单的加权平均权重
        model_names = list(base_predictions[0].keys())
        model_accuracies = {name: 0.0 for name in model_names}
        
        # 计算每个模型的准确率
        for predictions, label in zip(base_predictions, labels):
            for name in model_names:
                pred = 1 if predictions.get(name, 0.5) >= 0.5 else 0
                if pred == label:
                    model_accuracies[name] += 1
        
        # 归一化为权重
        total_samples = len(base_predictions)
        weights = {}
        for name in model_names:
            weights[name] = model_accuracies[name] / total_samples if total_samples > 0 else 1.0 / len(model_names)
        
        self.meta_learner = {'type': 'simple', 'weights': weights}
        self.is_trained = True
        
        self.logger.info(f"简化Stacking训练完成，权重: {weights}")
    
    def predict(self, base_predictions: Dict[str, float]) -> EnsemblePrediction:
        """
        使用Stacking进行预测
        
        Args:
            base_predictions: 基础模型预测
            
        Returns:
            集成预测结果
        """
        if not self.is_trained:
            return EnsemblePrediction(
                prediction=0.5,
                confidence=0.1,
                reasoning="Stacking模型未训练",
                method="stacking"
            )
        
        try:
            if SKLEARN_AVAILABLE and hasattr(self.meta_learner, 'predict_proba'):
                return self._predict_sklearn(base_predictions)
            else:
                return self._predict_simple(base_predictions)
                
        except Exception as e:
            self.logger.error(f"Stacking预测失败: {str(e)}")
            return EnsemblePrediction(
                prediction=0.5,
                confidence=0.1,
                reasoning=f"预测失败: {str(e)}",
                method="stacking"
            )
    
    def _predict_sklearn(self, base_predictions: Dict[str, float]) -> EnsemblePrediction:
        """使用scikit-learn进行预测"""
        # 准备元特征
        model_names = list(base_predictions.keys())
        features = [base_predictions.get(name, 0.5) for name in model_names]
        
        # 添加交互特征
        if len(features) >= 2:
            features.append(np.var(features))
            features.append(np.mean(features))
            features.append(max(features[:-2]) - min(features[:-2]))
        
        X_meta = np.array(features).reshape(1, -1)
        
        # 预测
        prediction_proba = self.meta_learner.predict_proba(X_meta)[0]
        prediction = prediction_proba[1] if len(prediction_proba) > 1 else prediction_proba[0]
        
        # 计算置信度
        confidence = max(prediction_proba) if len(prediction_proba) > 1 else abs(prediction - 0.5) + 0.5
        
        reasoning = f"Stacking集成: 元学习器预测={prediction:.3f}"
        
        return EnsemblePrediction(
            prediction=prediction,
            confidence=confidence,
            reasoning=reasoning,
            method="stacking",
            model_predictions=base_predictions
        )
    
    def _predict_simple(self, base_predictions: Dict[str, float]) -> EnsemblePrediction:
        """简化预测"""
        weights = self.meta_learner.get('weights', {})
        
        # 加权平均
        weighted_sum = 0.0
        total_weight = 0.0
        
        for name, pred in base_predictions.items():
            weight = weights.get(name, 1.0 / len(base_predictions))
            weighted_sum += pred * weight
            total_weight += weight
        
        prediction = weighted_sum / total_weight if total_weight > 0 else 0.5
        confidence = abs(prediction - 0.5) + 0.5
        
        reasoning = f"简化Stacking: 加权平均={prediction:.3f}"
        
        return EnsemblePrediction(
            prediction=prediction,
            confidence=confidence,
            reasoning=reasoning,
            method="stacking_simple",
            model_weights=weights,
            model_predictions=base_predictions
        )


class BlendingEnsemble:
    """Blending集成学习"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Blending集成
        
        Args:
            config: 配置字典
        """
        self.config = config.get('blending', {})
        self.logger = logging.getLogger(__name__)
        
        # 权重
        self.weights = None
        self.is_trained = False
        
        # 验证集比例
        self.holdout_ratio = self.config.get('holdout_ratio', 0.2)
        
    def train(self, base_predictions: List[Dict[str, float]], labels: List[int]):
        """
        训练Blending模型
        
        Args:
            base_predictions: 基础模型预测列表
            labels: 真实标签
        """
        if len(base_predictions) < 10:
            self.logger.warning("Blending训练数据不足")
            return
        
        try:
            # 分割数据
            split_idx = int(len(base_predictions) * (1 - self.holdout_ratio))
            
            train_predictions = base_predictions[:split_idx]
            train_labels = labels[:split_idx]
            val_predictions = base_predictions[split_idx:]
            val_labels = labels[split_idx:]
            
            # 在验证集上优化权重
            self.weights = self._optimize_weights(val_predictions, val_labels)
            self.is_trained = True
            
            # 评估性能
            val_accuracy = self._evaluate_weights(val_predictions, val_labels, self.weights)
            self.logger.info(f"Blending模型训练完成，验证准确率: {val_accuracy:.4f}")
            self.logger.info(f"优化权重: {self.weights}")
            
        except Exception as e:
            self.logger.error(f"Blending训练失败: {str(e)}")
    
    def _optimize_weights(self, predictions: List[Dict[str, float]], 
                         labels: List[int]) -> Dict[str, float]:
        """优化权重"""
        if not predictions:
            return {}
        
        model_names = list(predictions[0].keys())
        best_weights = None
        best_accuracy = 0.0
        
        # 网格搜索权重
        weight_options = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
        
        # 简化搜索：只考虑均匀权重和基于准确率的权重
        weight_combinations = []
        
        # 均匀权重
        uniform_weight = 1.0 / len(model_names)
        weight_combinations.append({name: uniform_weight for name in model_names})
        
        # 基于单模型准确率的权重
        for focus_model in model_names:
            weights = {name: 0.1 for name in model_names}
            weights[focus_model] = 0.7
            weight_combinations.append(weights)
        
        # 评估每种权重组合
        for weights in weight_combinations:
            accuracy = self._evaluate_weights(predictions, labels, weights)
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                best_weights = weights.copy()
        
        return best_weights if best_weights else {name: uniform_weight for name in model_names}
    
    def _evaluate_weights(self, predictions: List[Dict[str, float]], 
                         labels: List[int], weights: Dict[str, float]) -> float:
        """评估权重组合的准确率"""
        correct = 0
        total = len(predictions)
        
        for pred_dict, label in zip(predictions, labels):
            # 加权平均预测
            weighted_sum = sum(pred_dict.get(name, 0.5) * weight 
                             for name, weight in weights.items())
            total_weight = sum(weights.values())
            
            final_pred = weighted_sum / total_weight if total_weight > 0 else 0.5
            predicted_class = 1 if final_pred >= 0.5 else 0
            
            if predicted_class == label:
                correct += 1
        
        return correct / total if total > 0 else 0.0
    
    def predict(self, base_predictions: Dict[str, float]) -> EnsemblePrediction:
        """
        使用Blending进行预测
        
        Args:
            base_predictions: 基础模型预测
            
        Returns:
            集成预测结果
        """
        if not self.is_trained or not self.weights:
            # 使用均匀权重
            uniform_weight = 1.0 / len(base_predictions) if base_predictions else 0.5
            weights = {name: uniform_weight for name in base_predictions.keys()}
        else:
            weights = self.weights
        
        # 加权平均
        weighted_sum = 0.0
        total_weight = 0.0
        
        for name, pred in base_predictions.items():
            weight = weights.get(name, 0.0)
            weighted_sum += pred * weight
            total_weight += weight
        
        prediction = weighted_sum / total_weight if total_weight > 0 else 0.5
        confidence = abs(prediction - 0.5) + 0.5
        
        reasoning = f"Blending集成: 加权平均={prediction:.3f}"
        
        return EnsemblePrediction(
            prediction=prediction,
            confidence=confidence,
            reasoning=reasoning,
            method="blending",
            model_weights=weights,
            model_predictions=base_predictions
        )


class AdvancedEnsembleLayer:
    """高级集成学习层"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化高级集成学习层
        
        Args:
            config: 配置字典
        """
        self.config = config.get('advanced_ensemble', {})
        self.logger = logging.getLogger(__name__)
        
        # 集成方法
        self.stacking = StackingEnsemble(config)
        self.blending = BlendingEnsemble(config)
        
        # 训练数据收集
        self.training_buffer = deque(maxlen=1000)
        self.min_training_samples = self.config.get('min_training_samples', 50)
        
        # 性能监控
        self.performance_history = deque(maxlen=100)
        
        self.logger.info("高级集成学习层初始化完成")
    
    def collect_training_data(self, base_predictions: Dict[str, float], actual_result: int):
        """收集训练数据"""
        self.training_buffer.append({
            'predictions': base_predictions.copy(),
            'label': actual_result,
            'timestamp': time.time()
        })
        
        # 定期重训练
        if len(self.training_buffer) >= self.min_training_samples:
            if len(self.training_buffer) % 20 == 0:  # 每20个样本重训练一次
                self._retrain_models()
    
    def _retrain_models(self):
        """重新训练集成模型"""
        try:
            # 准备训练数据
            predictions_list = [item['predictions'] for item in self.training_buffer]
            labels_list = [item['label'] for item in self.training_buffer]
            
            # 训练Stacking
            self.stacking.train(predictions_list, labels_list)
            
            # 训练Blending
            self.blending.train(predictions_list, labels_list)
            
            self.logger.info(f"集成模型重训练完成，使用 {len(predictions_list)} 个样本")
            
        except Exception as e:
            self.logger.error(f"集成模型重训练失败: {str(e)}")
    
    def predict(self, base_predictions: Dict[str, float], 
                method: str = 'auto') -> EnsemblePrediction:
        """
        高级集成预测
        
        Args:
            base_predictions: 基础模型预测
            method: 集成方法 ('stacking', 'blending', 'auto')
            
        Returns:
            集成预测结果
        """
        if method == 'auto':
            # 自动选择最佳方法
            method = self._select_best_method()
        
        if method == 'stacking':
            return self.stacking.predict(base_predictions)
        elif method == 'blending':
            return self.blending.predict(base_predictions)
        else:
            # 默认使用简单平均
            avg_pred = sum(base_predictions.values()) / len(base_predictions)
            return EnsemblePrediction(
                prediction=avg_pred,
                confidence=0.5,
                reasoning="简单平均集成",
                method="average"
            )
    
    def _select_best_method(self) -> str:
        """自动选择最佳集成方法"""
        # 基于历史性能选择
        if len(self.performance_history) < 10:
            return 'blending'  # 默认使用Blending
        
        # 这里可以实现更复杂的选择逻辑
        # 暂时返回Blending
        return 'blending'
    
    def get_ensemble_info(self) -> Dict[str, Any]:
        """获取集成信息"""
        return {
            'stacking_trained': self.stacking.is_trained,
            'blending_trained': self.blending.is_trained,
            'training_samples': len(self.training_buffer),
            'performance_history_length': len(self.performance_history),
            'stacking_weights': getattr(self.stacking.meta_learner, 'weights', None) if hasattr(self.stacking.meta_learner, 'weights') else None,
            'blending_weights': self.blending.weights
        }
