#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SimpleFusion V8 主入口文件

基于V6深度分析结果构建的新一代智能策略系统
采用3个核心基础策略 + 机器学习集成的四层架构
"""

import os
import sys
import time
import yaml
import logging
import warnings
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional

# 过滤NumPy数值计算警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from core.base_strategies import BaseStrategyLayer
from core.feature_engineering import FeatureEngineeringLayer
from core.ml_models import MLModelLayer
from core.deep_learning_models import DeepLearningModelLayer
from core.adaptive_decision import AdaptiveDecisionLayer, FinalDecision
from core.online_learning import OnlineLearningManager
from core.model_optimization import ModelOptimizationManager
from core.multi_objective_optimization import MultiObjectiveDecisionManager
from core.intelligent_risk_management import IntelligentRiskManager
from core.decision_explainability import DecisionExplainer, ExplanationType
from core.adaptive_strategy_tuning import AdaptiveStrategyTuner, TuningMethod
from utils.data_processor import DataProcessor
from utils.model_persistence import ModelPersistence
from utils.metrics import MetricsCollector

# 导入连胜连败选择器
from core.streak_selector import StreakBasedStrategySelector


class SimpleFusionV8:
    """
    SimpleFusion V8 主系统类
    
    四层架构：
    1. 基础策略层：核心3策略 (strategy_1, strategy_2, strategy_6)
    2. 特征工程层：一致性、分歧、历史、动态权重特征
    3. 机器学习模型层：投票、XGBoost、神经网络、元学习器
    4. 自适应决策层：置信度评估、风险控制、在线学习、异常检测
    """
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化V8系统
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self._setup_logging()
        
        # 初始化各层组件
        self.base_strategies = None
        self.feature_engineering = None
        self.ml_models = None
        self.adaptive_decision = None

        # 连胜连败选择器
        self.streak_selector = None
        
        # 工具组件
        self.data_processor = None
        self.model_persistence = None
        self.metrics_collector = None
        
        # 系统状态
        self.is_initialized = False
        self.decision_count = 0
        self.decision_history = {}  # 存储决策历史
        self.performance_history = []
        
        self.logger.info(f"SimpleFusion V8 系统初始化完成")
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_file = PROJECT_ROOT / self.config_path
        
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
            
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        return config
    
    def _setup_logging(self):
        """设置日志系统"""
        log_config = self.config.get('logging', {})
        
        # 创建日志目录
        log_file = PROJECT_ROOT / log_config.get('file', 'logs/simplefusion_v8.log')
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def initialize(self):
        """初始化所有系统组件"""
        try:
            self.logger.info("开始初始化V8系统组件...")
            
            # 初始化工具组件
            self.data_processor = DataProcessor(self.config)
            self.model_persistence = ModelPersistence(self.config)
            self.metrics_collector = MetricsCollector(self.config)
            
            # 初始化四层架构
            self.base_strategies = BaseStrategyLayer(self.config)
            self.feature_engineering = FeatureEngineeringLayer(self.config)
            self.ml_models = MLModelLayer(self.config)
            self.deep_learning_models = DeepLearningModelLayer(self.config)  # 新增深度学习层
            self.adaptive_decision = AdaptiveDecisionLayer(self.config)

            # 在线学习管理器
            self.online_learning = OnlineLearningManager(self.config)
            self.online_learning.start_online_learning()

            # 模型优化管理器
            self.model_optimization = ModelOptimizationManager(self.config)

            # 多目标决策管理器
            self.multi_objective_manager = MultiObjectiveDecisionManager(self.config)

            # 智能风险管理器
            self.intelligent_risk_manager = IntelligentRiskManager(self.config)

            # 决策解释性系统
            self.decision_explainer = DecisionExplainer(self.config)

            # 自适应策略调优系统
            self.adaptive_tuner = AdaptiveStrategyTuner(self.config)

            # 连胜连败选择器
            self.streak_selector = StreakBasedStrategySelector(self.config)

            # 加载已保存的模型
            self._load_saved_models()

            # 预热深度学习模型
            self._warm_up_models()

            self.is_initialized = True
            self.logger.info("V8系统组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {str(e)}")
            raise
    
    def _load_saved_models(self):
        """加载已保存的模型"""
        try:
            # 尝试加载已训练的模型
            if self.model_persistence.has_saved_models():
                self.logger.info("发现已保存的模型，正在加载...")
                self.ml_models.load_models(self.model_persistence)
                self.logger.info("模型加载完成")
            else:
                self.logger.info("未发现已保存的模型，将使用默认配置")
                
        except Exception as e:
            self.logger.warning(f"模型加载失败，使用默认配置: {str(e)}")

    def _warm_up_models(self):
        """预热深度学习模型"""
        try:
            # 创建样本特征数据
            sample_features = {
                'consensus_ratio': 0.5,
                'weighted_consensus': 0.5,
                'avg_confidence': 0.6,
                'min_confidence': 0.4,
                'max_confidence': 0.8,
                'confidence_std': 0.1,
                'total_divergence': 0.2,
                'max_divergence': 0.3,
                'strategy_1_prediction': 0.6,
                'strategy_2_prediction': 0.4,
                'strategy_6_prediction': 0.7,
                'strategy_1_confidence': 0.7,
                'strategy_2_confidence': 0.6,
                'strategy_6_confidence': 0.8,
                'win_rate_10': 0.55,
                'win_rate_5': 0.6,
                'current_streak': 2,
                'strategy_1_weight': 0.33,
                'strategy_2_weight': 0.33,
                'strategy_6_weight': 0.34
            }

            # 预热深度学习模型
            if hasattr(self.deep_learning_models, 'warm_up_models'):
                self.deep_learning_models.warm_up_models(sample_features)
                self.logger.info("深度学习模型预热完成")

        except Exception as e:
            self.logger.warning(f"模型预热失败: {str(e)}")
    
    def process_decision(self, external_data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        处理单次决策
        
        Args:
            external_data: 外部输入数据 (可选)
            
        Returns:
            决策结果字典
        """
        if not self.is_initialized:
            raise RuntimeError("系统未初始化，请先调用 initialize() 方法")
        
        try:
            # 开始决策解释跟踪
            decision_id = external_data.get('decision_id', f'decision_{self.decision_count}')
            self.decision_explainer.start_decision_explanation(decision_id)

            # 立即保存决策历史记录（确保update_feedback能找到）
            self.decision_history[decision_id] = {
                'prediction': 0.5,  # 临时值，稍后更新
                'confidence': 0.5,  # 临时值，稍后更新
                'risk_level': 'medium',  # 临时值，稍后更新
                'should_act': False,  # 临时值，稍后更新
                'timestamp': time.time()
            }

            # 第一层：基础策略层
            strategy_outputs = self.base_strategies.get_strategy_outputs(external_data)

            # 保存策略输出供反馈使用
            self._last_strategy_outputs = strategy_outputs

            # 连胜连败策略选择 (新增核心功能)
            if hasattr(self, 'streak_selector') and self.streak_selector:
                # 提取策略预测
                strategy_predictions = {}
                for i in range(1, 9):
                    strategy_name = f'strategy_{i}'
                    if strategy_name in strategy_outputs:
                        strategy_output = strategy_outputs[strategy_name]
                        if hasattr(strategy_output, 'prediction'):
                            strategy_predictions[strategy_name] = strategy_output.prediction
                        elif isinstance(strategy_output, dict):
                            strategy_predictions[strategy_name] = strategy_output.get('prediction', 0)
                        else:
                            strategy_predictions[strategy_name] = 0
                    else:
                        strategy_predictions[strategy_name] = 0

                # 使用连胜连败选择器
                streak_selection = self.streak_selector.select_best_strategy(strategy_predictions)

                # 保存选择结果供反馈使用
                self._last_streak_selection = streak_selection

                self.logger.info(f"🎯 连胜连败选择: {streak_selection.selected_strategy} -> {streak_selection.prediction} "
                               f"(置信度: {streak_selection.confidence:.3f}, 原因: {streak_selection.reason})")
            else:
                self._last_streak_selection = None

            # 记录策略层解释
            self.decision_explainer.add_layer_explanation(
                "策略层",
                {"external_data": external_data},
                strategy_outputs,
                f"基础策略处理：{len(strategy_outputs.get('strategies', []))}个策略输出",
                strategy_outputs.get('avg_confidence', 0.5),
                0.7
            )
            
            # 第二层：特征工程层
            features = self.feature_engineering.extract_features(
                strategy_outputs,
                self.base_strategies.get_history()
            )

            # 记录特征工程层解释
            self.decision_explainer.add_layer_explanation(
                "特征工程层",
                {"strategy_outputs": strategy_outputs},
                {"features": features},
                f"特征提取：生成{len(features)}个特征",
                features.get('avg_confidence', 0.5),
                0.6
            )
            
            # 第三层：机器学习模型层
            ml_predictions = self.ml_models.predict(features)

            # 保存特征和ML预测供反馈使用
            self._last_features = features.copy()
            self._last_ml_predictions = ml_predictions.copy()

            # 记录ML层解释
            ml_confidence = np.mean([pred.confidence for pred in ml_predictions.values() if hasattr(pred, 'confidence')])
            self.decision_explainer.add_layer_explanation(
                "机器学习层",
                {"features": features},
                {"ml_predictions": {k: v.prediction if hasattr(v, 'prediction') else v for k, v in ml_predictions.items()}},
                f"ML预测：{len(ml_predictions)}个模型预测",
                ml_confidence,
                0.8
            )

            # 第三层增强：深度学习模型层
            dl_predictions = self.deep_learning_models.get_ensemble_prediction(
                self.feature_engineering.feature_history
            )

            # 记录深度学习层解释
            dl_prediction_value = getattr(dl_predictions, 'ensemble_prediction', 'N/A') if dl_predictions else 'N/A'
            dl_confidence_value = getattr(dl_predictions, 'ensemble_confidence', 0.5) if dl_predictions else 0.5

            self.decision_explainer.add_layer_explanation(
                "深度学习层",
                {"feature_history": "processed"},
                {"dl_predictions": dl_prediction_value},
                f"深度学习集成预测：{dl_prediction_value}",
                dl_confidence_value,
                0.7
            )

            # 第四层：多目标优化决策参数
            decision_context = {
                'strategy_outputs': strategy_outputs,
                'features': features,
                'ml_predictions': ml_predictions,
                'dl_predictions': dl_predictions,
                'external_data': external_data
            }

            # 检查是否需要优化决策参数
            if self.multi_objective_manager.should_optimize(self.decision_count):
                optimized_solution = self.multi_objective_manager.optimize_decision_parameters(decision_context)
                decision_context = self.multi_objective_manager.apply_solution(optimized_solution, decision_context)
            else:
                # 使用当前最优参数
                current_params = self.multi_objective_manager.get_current_parameters()
                decision_context.update(current_params)

            # 第五层：智能风险评估
            meta_learner_pred = ml_predictions.get('meta_learner')
            if meta_learner_pred and hasattr(meta_learner_pred, 'prediction'):
                prediction_value = meta_learner_pred.prediction
            else:
                prediction_value = 0.5

            risk_evaluation = self.intelligent_risk_manager.evaluate_decision_risk({
                'prediction': prediction_value,
                'confidence': features.get('avg_confidence', 0.5),
                'decision_count': self.decision_count
            })

            # 检查是否应该拦截决策
            if risk_evaluation['should_block_decision']:
                self.logger.warning(f"决策被风险管理系统拦截: {risk_evaluation['recommendations']}")
                # 返回保守决策
                final_decision = FinalDecision(
                    decision_id=external_data['decision_id'],
                    prediction=0,  # 保守预测
                    confidence=0.1,  # 极低置信度
                    risk_level='blocked',
                    should_act=False,
                    reasoning=f"决策被风险管理系统拦截: {'; '.join(risk_evaluation['recommendations'])}",
                    strategy_outputs=strategy_outputs,
                    ml_predictions=ml_predictions,
                    features=features,
                    timestamp=time.time()
                )
            else:
                # 应用风险调整的仓位
                decision_context['position_adjustment'] = risk_evaluation['position_adjustment']
                decision_context['risk_recommendations'] = risk_evaluation['recommendations']

                # 第六层：自适应决策层（集成连胜连败选择）
                if hasattr(self, '_last_streak_selection') and self._last_streak_selection:
                    # 使用连胜连败选择结果
                    streak_selection = self._last_streak_selection
                    final_decision = FinalDecision(
                        decision_id=external_data['decision_id'],
                        prediction=streak_selection.prediction,
                        confidence=streak_selection.confidence,
                        risk_level='low' if streak_selection.confidence > 0.7 else 'medium',
                        should_act=streak_selection.confidence > 0.5,
                        reasoning=f"连胜连败选择: {streak_selection.reason}",
                        strategy_outputs=strategy_outputs,
                        ml_predictions=ml_predictions,
                        features=features,
                        timestamp=time.time()
                    )
                    self.logger.info(f"🎯 使用连胜连败选择结果: {streak_selection.selected_strategy} -> {streak_selection.prediction}")
                else:
                    # 回退到原有的自适应决策层
                    final_decision = self.adaptive_decision.make_decision(
                        strategy_outputs=strategy_outputs,
                        features=features,
                        ml_predictions=ml_predictions,
                        dl_predictions=dl_predictions,
                        decision_count=self.decision_count,
                        optimization_context=decision_context  # 传递优化上下文
                    )
            
            # 生成决策解释
            try:
                explanation = self.decision_explainer.generate_explanation(
                    decision_id,
                    features,
                    final_decision.prediction,
                    final_decision.confidence,
                    [ExplanationType.FEATURE_IMPORTANCE, ExplanationType.DECISION_PATH,
                     ExplanationType.RULE_BASED, ExplanationType.CONFIDENCE_BREAKDOWN]
                )

                # 将解释添加到决策结果中
                final_decision.explanation = explanation

                # 记录解释日志（如果启用详细日志）
                if self.config.get('logging', {}).get('detailed_logging', {}).get('decision_details', False):
                    self.logger.info(f"决策解释 #{self.decision_count}:")
                    self.logger.info(f"  总结: {explanation.summary}")
                    self.logger.info(f"  关键特征: {[imp.feature_name for imp in explanation.feature_importances[:3]]}")

            except Exception as e:
                self.logger.warning(f"决策解释生成失败: {str(e)}")
                final_decision.explanation = None

            # 记录自适应调优数据
            try:
                tuning_data = {
                    'decision_id': decision_id,
                    'prediction': final_decision.prediction,
                    'confidence': final_decision.confidence,
                    'strategy_outputs': strategy_outputs,
                    'weights': self.adaptive_decision.get_current_weights(),
                    'features': features,
                    'performance': {
                        'accuracy': getattr(self, 'recent_accuracy', 0.5),
                        'win_rate': getattr(self, 'recent_win_rate', 0.5),
                        'confidence': final_decision.confidence
                    }
                }
                self.adaptive_tuner.record_decision(tuning_data)

                # 检查是否需要调优
                if self.adaptive_tuner.should_tune():
                    current_weights = self.adaptive_decision.get_current_weights()
                    current_thresholds = {
                        'confidence_threshold': self.config.get('adaptive_decision', {}).get('confidence_threshold', 0.6),
                        'consensus_threshold': self.config.get('adaptive_decision', {}).get('consensus_threshold', 0.5),
                        'risk_threshold': self.config.get('intelligent_risk_management', {}).get('intelligent_risk_manager', {}).get('risk_threshold', 0.7)
                    }

                    tuning_result = self.adaptive_tuner.perform_tuning(
                        current_weights, current_thresholds, TuningMethod.PERFORMANCE_BASED
                    )

                    # 应用调优结果
                    if tuning_result.performance_improvement > 0.01:
                        self.adaptive_decision.update_weights(tuning_result.new_weights)
                        self.logger.info(f"应用调优结果: 性能改进 {tuning_result.performance_improvement:.3f}")

            except Exception as e:
                self.logger.warning(f"自适应调优失败: {str(e)}")

            # 更新决策计数
            self.decision_count += 1

            # 更新决策历史记录
            if final_decision.decision_id in self.decision_history:
                self.decision_history[final_decision.decision_id].update({
                    'prediction': final_decision.prediction,
                    'confidence': final_decision.confidence,
                    'risk_level': final_decision.risk_level,
                    'should_act': final_decision.should_act,
                    'timestamp': final_decision.timestamp
                })
            else:
                # 如果由于某种原因记录不存在，创建新记录
                self.decision_history[final_decision.decision_id] = {
                    'prediction': final_decision.prediction,
                    'confidence': final_decision.confidence,
                    'risk_level': final_decision.risk_level,
                    'should_act': final_decision.should_act,
                    'timestamp': final_decision.timestamp
                }

            # 记录指标
            decision_data = {
                'decision_id': final_decision.decision_id,
                'prediction': final_decision.prediction,
                'confidence': final_decision.confidence,
                'risk_level': final_decision.risk_level,
                'should_act': final_decision.should_act,
                'response_time': 0  # 可以后续添加响应时间计算
            }
            self.metrics_collector.record_decision(decision_data)
            
            # 详细日志
            if self.config.get('logging', {}).get('detailed_logging', {}).get('decision_details', False):
                self.logger.info(f"决策 #{self.decision_count}: {final_decision}")
            
            return final_decision
            
        except Exception as e:
            self.logger.error(f"决策处理失败: {str(e)}")
            raise
    
    def update_feedback(self, decision_id: str, actual_result: int):
        """
        更新决策反馈
        
        Args:
            decision_id: 决策ID
            actual_result: 实际结果 (0 或 1)
        """
        try:
            # 更新各层的反馈
            self.base_strategies.update_feedback(decision_id, actual_result)
            self.ml_models.update_feedback(decision_id, actual_result)
            self.adaptive_decision.update_feedback(decision_id, actual_result)

            # 更新多目标优化管理器和智能风险管理器
            if decision_id in self.decision_history:
                decision_record = self.decision_history[decision_id]
                prediction = decision_record.get('prediction', 0)

                # 计算收益和损失（简化计算）
                return_value = 1.0 if prediction == actual_result else -1.0
                loss = 0.0 if prediction == actual_result else 1.0

                # 更新多目标优化管理器
                self.multi_objective_manager.update_feedback(
                    decision_id, prediction, actual_result, return_value, loss
                )

                # 更新智能风险管理器
                decision_data = {
                    'prediction': prediction,
                    'confidence': decision_record.get('confidence', 0.5),
                    'risk_level': decision_record.get('risk_level', 'medium'),
                    'position_size': 1.0,  # 可以从决策记录中获取
                    'risk_score': 0.5  # 可以从决策记录中获取
                }

                risk_alerts = self.intelligent_risk_manager.process_decision_feedback(
                    decision_id, decision_data, actual_result
                )

                # 记录风险预警
                if risk_alerts:
                    self.logger.warning(f"决策 {decision_id} 产生了 {len(risk_alerts)} 个风险预警")
                    for alert in risk_alerts:
                        self.logger.warning(f"风险预警: {alert.message}")
                        self.logger.info(f"建议: {'; '.join(alert.recommendations)}")
            
            # 更新决策解释系统的反馈
            try:
                self.decision_explainer.update_explanation_feedback(decision_id, actual_result)
            except Exception as e:
                self.logger.warning(f"解释系统反馈更新失败: {str(e)}")

            # 更新自适应调优系统的反馈
            try:
                # 更新决策记录中的实际结果
                for record in self.adaptive_tuner.performance_tracker.decision_history:
                    if record.get('decision_id') == decision_id:
                        record['actual'] = actual_result
                        break

                # 更新策略性能记录
                if hasattr(self, '_last_strategy_outputs'):
                    for strategy_name, output in self._last_strategy_outputs.items():
                        if hasattr(output, 'prediction') and hasattr(output, 'confidence'):
                            self.adaptive_tuner.performance_tracker.record_strategy_decision(
                                strategy_name, output.prediction, output.confidence, actual_result
                            )

            except Exception as e:
                self.logger.warning(f"自适应调优反馈更新失败: {str(e)}")

            # 记录性能
            self.metrics_collector.record_feedback(decision_id, actual_result)

            # 更新在线学习管理器
            if decision_id in self.decision_history:
                decision_record = self.decision_history[decision_id]

                # 获取决策时的特征和ML预测
                if hasattr(self, '_last_features') and hasattr(self, '_last_ml_predictions'):
                    # 提取ML预测值
                    model_predictions = {}
                    for model_name, prediction in self._last_ml_predictions.items():
                        if hasattr(prediction, 'prediction'):
                            model_predictions[model_name] = prediction.prediction
                        else:
                            model_predictions[model_name] = float(prediction)

                    # 添加到在线学习
                    self.online_learning.add_feedback(
                        model_predictions=model_predictions,
                        features=self._last_features,
                        actual_result=actual_result
                    )

                    # 调试信息
                    if self.decision_count % 100 == 0:
                        batch_count = len(self.online_learning.batch_updates)
                        self.logger.info(f"在线学习数据: {batch_count} 条批量更新")
                else:
                    self.logger.warning(f"决策 {decision_id} 缺少特征或ML预测数据")
            else:
                self.logger.warning(f"决策 {decision_id} 不在历史记录中")

            # 在线学习
            if self.config.get('adaptive_decision', {}).get('online_learning', {}).get('enabled', True):
                self._trigger_online_learning()
                
        except Exception as e:
            self.logger.error(f"反馈更新失败: {str(e)}")
            raise
    
    def _trigger_online_learning(self):
        """触发在线学习和模型优化"""
        # 在线学习管理器会自动处理增量更新
        # 这里可以添加额外的触发逻辑
        online_config = self.config.get('online_learning', {})

        if self.decision_count % online_config.get('status_check_frequency', 50) == 0:
            # 定期检查在线学习状态
            status = self.online_learning.get_learning_status()
            self.logger.info(f"在线学习状态检查 (决策数: {self.decision_count})")

            # 检查概念漂移
            if status['drift_detection']['drift_detected']:
                self.logger.warning(f"检测到概念漂移: {status['drift_detection']['drift_type']}")
                self.logger.warning(f"建议: {status['drift_detection']['recommendation']}")

        # 检查是否需要模型优化
        if self.model_optimization.should_optimize(self.decision_count):
            self.logger.info(f"触发模型优化 (决策数: {self.decision_count})")
            self._perform_model_optimization()

    def _perform_model_optimization(self):
        """执行模型优化"""
        try:
            # 获取训练数据
            training_data = list(self.online_learning.batch_updates)

            # 训练期间禁用数据量检查，让模型正常训练
            # if len(training_data) < 10:
            #     self.logger.warning(f"训练数据不足({len(training_data)}条)，跳过模型优化")
            #     return

            # 获取模型字典
            models = {}
            if hasattr(self.ml_models, 'models'):
                models.update(self.ml_models.models)

            # 执行优化
            optimization_results = self.model_optimization.optimize_models(models, training_data)

            # 记录优化结果
            if optimization_results:
                self.logger.info(f"模型优化完成，优化了 {len(optimization_results)} 个组件")
                for name, result in optimization_results.items():
                    if result.improvement > 0:
                        self.logger.info(f"{name}: 性能提升 {result.improvement:.4f}")

        except Exception as e:
            self.logger.error(f"模型优化失败: {str(e)}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            'is_initialized': self.is_initialized,
            'decision_count': self.decision_count,
            'current_performance': self.metrics_collector.get_current_metrics(),
            'system_health': self._check_system_health(),
            'model_status': self.ml_models.get_model_status() if self.ml_models else None,
            'config_version': self.config.get('system', {}).get('version', 'unknown')
        }

        # 添加在线学习状态
        if hasattr(self, 'online_learning') and self.online_learning:
            status['online_learning'] = self.online_learning.get_learning_status()

        # 添加模型优化状态
        if hasattr(self, 'model_optimization') and self.model_optimization:
            status['model_optimization'] = self.model_optimization.get_optimization_status()

        # 添加多目标优化状态
        if hasattr(self, 'multi_objective_manager') and self.multi_objective_manager:
            status['multi_objective_optimization'] = self.multi_objective_manager.get_optimization_status()

        # 添加智能风险管理状态
        if hasattr(self, 'intelligent_risk_manager') and self.intelligent_risk_manager:
            status['intelligent_risk_management'] = self.intelligent_risk_manager.get_risk_management_status()

        # 添加决策解释性系统状态
        if hasattr(self, 'decision_explainer') and self.decision_explainer:
            status['decision_explainability'] = self.decision_explainer.get_explanation_summary()

        # 添加自适应策略调优状态
        if hasattr(self, 'adaptive_tuner') and self.adaptive_tuner:
            status['adaptive_strategy_tuning'] = self.adaptive_tuner.get_tuning_summary()
            status['environment_status'] = self.adaptive_tuner.get_environment_status()

        return status
    
    def _check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        health = {
            'status': 'healthy',
            'issues': [],
            'warnings': []
        }
        
        # 检查各种健康指标
        if self.metrics_collector:
            metrics = self.metrics_collector.get_current_metrics()
            
            # 检查准确率
            if metrics.get('accuracy', 0) < 0.5:
                health['issues'].append('准确率过低')
                health['status'] = 'unhealthy'
            
            # 检查连错次数
            if metrics.get('consecutive_errors', 0) > 10:
                health['warnings'].append('连错次数较高')
                if health['status'] == 'healthy':
                    health['status'] = 'warning'
        
        return health
    
    def save_models(self):
        """保存当前模型"""
        try:
            self.model_persistence.save_models(self.ml_models)
            self.logger.info("模型保存完成")
        except Exception as e:
            self.logger.error(f"模型保存失败: {str(e)}")
            raise


def main():
    """主函数 - 用于测试和演示"""
    print("🚀 SimpleFusion V8 策略系统启动")
    
    try:
        # 创建系统实例
        system = SimpleFusionV8()
        
        # 初始化系统
        system.initialize()
        
        # 显示系统状态
        status = system.get_system_status()
        print(f"📊 系统状态: {status}")
        
        print("✅ V8系统启动成功！")
        print("💡 使用 system.process_decision() 进行决策")
        print("💡 使用 system.update_feedback(decision_id, actual_result) 更新反馈")
        
        return system
        
    except Exception as e:
        print(f"❌ 系统启动失败: {str(e)}")
        raise


if __name__ == "__main__":
    system = main()
