#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级特征工程功能

验证新增的时序、交互、上下文、统计特征
"""

import sys
import os
import time
import numpy as np
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List, Any

# 添加项目根目录到Python路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from core.feature_engineering import FeatureEngineeringLayer
    from core.base_strategies import StrategyOutput
    print("✅ 成功导入特征工程模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def create_mock_strategy_outputs() -> Dict[str, StrategyOutput]:
    """创建模拟策略输出"""
    return {
        'strategy_1': StrategyOutput(
            strategy_name='strategy_1',
            prediction=1,
            confidence=0.7,
            reasoning='测试策略1',
            timestamp=time.time()
        ),
        'strategy_2': StrategyOutput(
            strategy_name='strategy_2',
            prediction=0,
            confidence=0.6,
            reasoning='测试策略2',
            timestamp=time.time()
        ),
        'strategy_6': StrategyOutput(
            strategy_name='strategy_6',
            prediction=1,
            confidence=0.8,
            reasoning='测试策略6',
            timestamp=time.time()
        )
    }


def create_mock_history(length: int = 20) -> List[Dict[str, Any]]:
    """创建模拟历史数据"""
    history = []
    base_time = time.time() - length * 60  # 每分钟一条记录
    
    for i in range(length):
        record = {
            'result': np.random.choice([0, 1]),
            'decision': np.random.choice([0, 1]),
            'timestamp': base_time + i * 60,
            'confidence': 0.5 + np.random.random() * 0.4
        }
        history.append(record)
    
    return history


def test_basic_features():
    """测试基础特征提取"""
    print("\n🧪 测试基础特征提取...")
    
    # 创建特征工程层
    config = {
        'feature_engineering': {
            'consistency': {'enabled': True},
            'divergence': {'enabled': True},
            'historical': {'enabled': True, 'lookback_windows': [3, 5, 10]},
            'dynamic_weights': {'enabled': True}
        }
    }
    
    feature_layer = FeatureEngineeringLayer(config)
    
    # 创建测试数据
    strategy_outputs = create_mock_strategy_outputs()
    history = create_mock_history(15)
    
    # 提取特征
    features = feature_layer.extract_features(strategy_outputs, history)
    
    print(f"✅ 提取了 {len(features)} 个基础特征")
    
    # 显示部分特征
    important_features = ['consensus_ratio', 'weighted_consensus', 'avg_confidence', 'total_divergence']
    for feature in important_features:
        if feature in features:
            print(f"   {feature}: {features[feature]:.4f}")
    
    return features


def test_advanced_features():
    """测试高级特征提取"""
    print("\n🧪 测试高级特征提取...")
    
    # 创建特征工程层，启用所有高级特征
    config = {
        'feature_engineering': {
            'consistency': {'enabled': True},
            'divergence': {'enabled': True},
            'historical': {'enabled': True, 'lookback_windows': [3, 5, 10]},
            'dynamic_weights': {'enabled': True},
            'temporal': {'enabled': True},
            'interaction': {'enabled': True},
            'context': {'enabled': True},
            'statistical': {'enabled': True}
        }
    }
    
    feature_layer = FeatureEngineeringLayer(config)
    
    # 创建测试数据
    strategy_outputs = create_mock_strategy_outputs()
    history = create_mock_history(25)
    
    # 多次提取特征以建立历史
    for i in range(5):
        features = feature_layer.extract_features(strategy_outputs, history)
        time.sleep(0.1)  # 模拟时间间隔
    
    print(f"✅ 提取了 {len(features)} 个高级特征")
    
    # 按类别显示特征
    feature_categories = {
        'temporal': ['avg_time_interval', 'periodicity_2', 'exponential_weighted_avg'],
        'interaction': ['strategy_1_pred_conf_product', 'confidence_range', 'high_conf_consensus'],
        'context': ['consensus_trend', 'consensus_stability', 'confidence_shift'],
        'statistical': ['prediction_skewness', 'confidence_skewness', 'consensus_trend_slope']
    }
    
    for category, feature_names in feature_categories.items():
        print(f"\n   📊 {category.upper()} 特征:")
        for feature in feature_names:
            if feature in features:
                print(f"     {feature}: {features[feature]:.4f}")
    
    return features


def test_feature_evolution():
    """测试特征随时间的演化"""
    print("\n🧪 测试特征演化...")
    
    config = {
        'feature_engineering': {
            'consistency': {'enabled': True},
            'divergence': {'enabled': True},
            'historical': {'enabled': True},
            'dynamic_weights': {'enabled': True},
            'temporal': {'enabled': True},
            'interaction': {'enabled': True},
            'context': {'enabled': True},
            'statistical': {'enabled': True}
        }
    }
    
    feature_layer = FeatureEngineeringLayer(config)
    
    # 模拟一系列决策
    evolution_data = []
    
    for step in range(10):
        # 创建变化的策略输出
        strategy_outputs = {
            'strategy_1': StrategyOutput(
                strategy_name='strategy_1',
                prediction=1 if step % 3 == 0 else 0,
                confidence=0.6 + (step % 4) * 0.1,
                reasoning=f'步骤{step}策略1',
                timestamp=time.time()
            ),
            'strategy_2': StrategyOutput(
                strategy_name='strategy_2',
                prediction=1 if step % 2 == 0 else 0,
                confidence=0.5 + (step % 3) * 0.15,
                reasoning=f'步骤{step}策略2',
                timestamp=time.time()
            ),
            'strategy_6': StrategyOutput(
                strategy_name='strategy_6',
                prediction=1 if step % 4 == 0 else 0,
                confidence=0.7 + (step % 2) * 0.1,
                reasoning=f'步骤{step}策略6',
                timestamp=time.time()
            )
        }
        
        # 创建历史数据
        history = create_mock_history(15 + step)
        
        # 提取特征
        features = feature_layer.extract_features(strategy_outputs, history)
        
        evolution_data.append({
            'step': step,
            'consensus_ratio': features.get('consensus_ratio', 0),
            'avg_confidence': features.get('avg_confidence', 0),
            'total_divergence': features.get('total_divergence', 0),
            'consensus_trend': features.get('consensus_trend', 0),
            'feature_count': len(features)
        })
        
        time.sleep(0.05)  # 短暂延迟
    
    print(f"✅ 完成 {len(evolution_data)} 步特征演化测试")
    
    # 显示演化趋势
    print(f"\n📈 特征演化趋势:")
    print(f"   步骤 | 一致性 | 置信度 | 分歧度 | 趋势   | 特征数")
    print(f"   -----|--------|--------|--------|--------|-------")
    
    for data in evolution_data[-5:]:  # 显示最后5步
        print(f"   {data['step']:4d} | {data['consensus_ratio']:6.3f} | "
              f"{data['avg_confidence']:6.3f} | {data['total_divergence']:6.3f} | "
              f"{data['consensus_trend']:6.3f} | {data['feature_count']:5d}")
    
    return evolution_data


def test_feature_importance():
    """测试特征重要性"""
    print("\n🧪 测试特征重要性...")
    
    config = {'feature_engineering': {}}
    feature_layer = FeatureEngineeringLayer(config)
    
    importance = feature_layer.get_feature_importance()
    
    print(f"✅ 获取了 {len(importance)} 个特征的重要性")
    
    # 按重要性排序显示
    sorted_importance = sorted(importance.items(), key=lambda x: x[1], reverse=True)
    
    print(f"\n📊 特征重要性排序 (Top 10):")
    for i, (feature, score) in enumerate(sorted_importance[:10]):
        print(f"   {i+1:2d}. {feature:25s}: {score:.3f}")
    
    return importance


def main():
    """主测试函数"""
    print("🚀 高级特征工程测试")
    print("=" * 60)
    
    # 1. 测试基础特征
    basic_features = test_basic_features()
    
    # 2. 测试高级特征
    advanced_features = test_advanced_features()
    
    # 3. 测试特征演化
    evolution_data = test_feature_evolution()
    
    # 4. 测试特征重要性
    importance = test_feature_importance()
    
    print("\n" + "=" * 60)
    print("🎉 高级特征工程测试完成！")
    
    # 总结
    print(f"\n📋 测试总结:")
    print(f"   ✅ 基础特征数量: {len(basic_features)}")
    print(f"   ✅ 高级特征数量: {len(advanced_features)}")
    print(f"   ✅ 特征演化步数: {len(evolution_data)}")
    print(f"   ✅ 重要性评估: {len(importance)} 个特征")
    
    # 特征增长对比
    feature_growth = len(advanced_features) - len(basic_features)
    print(f"   🚀 特征增长: +{feature_growth} 个 ({feature_growth/len(basic_features)*100:.1f}%)")
    
    return basic_features, advanced_features, evolution_data, importance


if __name__ == "__main__":
    basic_features, advanced_features, evolution_data, importance = main()
