# SimpleFusion V8 主配置文件

# 系统基础配置
system:
  name: "SimpleFusion V8"
  version: "8.0.0"
  debug: false
  log_level: "INFO"

# 基础策略层配置
base_strategies:
  # 核心3策略配置
  enabled_strategies:
    - strategy_1
    - strategy_2  
    - strategy_6
  
  # 策略权重 (初始权重，会动态调整)
  initial_weights:
    strategy_1: 0.4  # 最高胜率策略
    strategy_2: 0.3  # 低相关性策略
    strategy_6: 0.3  # 互补性策略
  
  # 策略性能阈值
  performance_thresholds:
    min_accuracy: 0.45      # 最低准确率
    max_error_streak: 15    # 最大连错次数
    weight_update_freq: 100 # 权重更新频率

# 特征工程配置
feature_engineering:
  # 一致性特征
  consistency:
    enabled: true
    window_sizes: [5, 10, 20, 50]  # 不同时间窗口
    
  # 分歧特征
  divergence:
    enabled: true
    pattern_types:
      - "1_vs_2_6"  # strategy_1 vs (strategy_2, strategy_6)
      - "2_vs_1_6"  # strategy_2 vs (strategy_1, strategy_6)
      - "6_vs_1_2"  # strategy_6 vs (strategy_1, strategy_2)
      
  # 历史特征
  historical:
    enabled: true
    lookback_windows: [3, 5, 10, 20]
    features:
      - accuracy_trend
      - stability_score
      - performance_variance
      
  # 动态权重特征
  dynamic_weights:
    enabled: true
    update_frequency: 50    # 每50次决策更新一次
    decay_factor: 0.95      # 历史权重衰减因子
    min_weight: 0.1         # 最小权重
    max_weight: 0.7         # 最大权重

  # 策略级连胜连败特征 (核心新功能)
  strategy_streaks:
    enabled: true
    window_sizes: [3, 5, 10, 20]  # 移动窗口大小
    trend_analysis: true          # 启用趋势分析
    stability_analysis: true      # 启用稳定性分析
    comparative_analysis: true    # 启用策略间比较

# 机器学习模型配置
ml_models:
  # 基础投票模型
  voting:
    simple_majority:
      enabled: true
      threshold: 0.5
    weighted_voting:
      enabled: true
      weight_update_freq: 100
      
  # XGBoost配置
  xgboost:
    enabled: true
    params:
      max_depth: 6
      learning_rate: 0.1
      n_estimators: 100
      subsample: 0.8
      colsample_bytree: 0.8
      random_state: 42
    online_learning:
      enabled: true
      update_freq: 200
      
  # LightGBM配置
  lightgbm:
    enabled: true
    params:
      num_leaves: 31
      learning_rate: 0.1
      feature_fraction: 0.8
      bagging_fraction: 0.8
      bagging_freq: 5
      verbose: -1
    online_learning:
      enabled: true
      update_freq: 200
      
  # 神经网络配置
  neural_network:
    enabled: true
    architecture:
      input_dim: 50        # 根据特征数量调整
      hidden_layers: [64, 32, 16]
      output_dim: 1
      dropout: 0.2
      activation: "relu"
    training:
      batch_size: 32
      learning_rate: 0.001
      epochs: 100
      early_stopping: 10
      
  # 元学习器配置
  meta_learner:
    enabled: true
    model_type: "logistic_regression"
    update_frequency: 500
    cross_validation_folds: 5

  # 策略选择模型 (核心新功能)
  strategy_selector:
    enabled: true
    model_type: "random_forest"  # random_forest, xgboost, neural_network
    n_estimators: 100
    max_depth: 8
    class_weight: "balanced"
    retrain_frequency: 100  # 每100次反馈重训练一次
    feature_importance_threshold: 0.01  # 特征重要性阈值

# 自适应决策层配置
adaptive_decision:
  # 置信度评估
  confidence:
    enabled: true
    factors:
      - strategy_consensus    # 策略一致性
      - historical_accuracy   # 历史准确率
      - model_uncertainty     # 模型不确定性
    weights: [0.4, 0.4, 0.2]
    min_confidence: 0.3       # 最低置信度阈值

  # 风险控制
  risk_control:
    enabled: true
    max_consecutive_errors: 8   # 最大连错次数
    error_threshold: 0.6        # 错误率阈值
    cooldown_period: 5          # 冷却期
    dynamic_threshold: true     # 动态阈值调整

  # 在线学习
  online_learning:
    enabled: true
    learning_rate: 0.01
    adaptation_speed: "medium"  # slow, medium, fast
    memory_size: 1000          # 记忆窗口大小

  # 异常检测
  anomaly_detection:
    enabled: true
    detection_methods:
      - statistical_outlier
      - isolation_forest
    sensitivity: 0.1           # 异常检测敏感度
    action_on_anomaly: "conservative"  # conservative, ignore, alert

# 数据库配置
database:
  # 主数据库 (MySQL)
  primary:
    host: "**************"
    port: 3306
    database: "lushu"
    table: "strategy_results"
    username: "root"
    password: "216888"
    
  # 缓存数据库 (Redis)
  cache:
    enabled: true
    host: "localhost"
    port: 6379
    db: 0
    ttl: 3600  # 缓存过期时间(秒)

# 监控配置
monitoring:
  enabled: true
  metrics:
    - accuracy
    - error_rate
    - response_time
    - model_performance
    - feature_importance
  alert_thresholds:
    accuracy_drop: 0.05      # 准确率下降超过5%时告警
    error_spike: 10          # 连续错误超过10次时告警
    response_time: 1000      # 响应时间超过1秒时告警
    
# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/simplefusion_v8.log"
  max_size: "50MB"
  backup_count: 10
  
  # 详细日志
  detailed_logging:
    decision_details: true
    feature_values: false     # 特征值详情 (调试时开启)
    model_predictions: true
    confidence_scores: true
    risk_assessments: true

# 性能配置
performance:
  # 并发配置
  max_workers: 4
  batch_processing: true
  batch_size: 100
  
  # 缓存配置
  feature_cache_size: 10000
  model_cache_size: 100
  
  # 优化配置
  enable_jit: false          # JIT编译 (生产环境可开启)
  memory_optimization: true
  
# 智能风险管理配置
intelligent_risk_management:
  # 风险计算器
  risk_calculator:
    lookback_period: 50
    confidence_threshold: 0.95
    var_confidence: 0.95

  # 动态风险评估器
  dynamic_risk_assessor:
    volatility_threshold: 0.3
    drawdown_threshold: 0.05
    consecutive_loss_threshold: 3
    win_rate_threshold: 0.4
    performance_window: 20

  # 风险监控器
  risk_monitor:
    alert_types:
      - volatility_spike
      - drawdown_warning
      - consecutive_losses
      - performance_decline
      - risk_threshold
    max_active_alerts: 20
    alert_decay_hours: 24

  # 智能风险管理器
  intelligent_risk_manager:
    risk_threshold: 0.7
    emergency_threshold: 0.9
    position_scaling_factor: 0.5
    emergency_scaling_factor: 0.1
    risk_budget: 0.02

# 决策解释性系统配置
explainability:
  # 特征重要性配置
  feature_importance:
    methods:
      - correlation
      - variance
      - permutation
    default_method: correlation
    top_k: 10
    min_history_size: 20
    max_history_size: 1000

  # 决策路径跟踪配置
  decision_path:
    track_all_steps: true
    max_steps: 20
    track_timing: true
    identify_critical_features: true

  # 反事实解释配置
  counterfactual:
    max_changes: 3
    change_magnitude: 0.2
    min_feasibility: 0.1
    target_prediction_auto: true

  # 规则提取配置
  rules:
    min_support: 5
    min_confidence: 0.7
    max_rules: 20
    auto_extract: true
    extract_interval: 50  # 每50个决策提取一次规则

  # 解释生成配置
  explanation_generation:
    default_types:
      - feature_importance
      - decision_path
      - rule_based
      - confidence_breakdown
    include_counterfactual: false  # 默认不包含反事实（计算量大）
    max_explanation_history: 100
    generate_summary: true
    generate_detailed_reasoning: true

  # 反馈学习配置
  feedback_learning:
    enable_feedback: true
    update_importance_weights: true
    update_rules: true
    feedback_decay: 0.95

# 自适应策略调优配置
adaptive_strategy_tuning:
  # 性能跟踪器配置
  performance_tracker:
    max_history: 1000
    performance_window: 50

  # 环境检测器配置
  environment_detector:
    max_history: 200
    volatility_threshold: 0.3
    trend_threshold: 0.1
    regime_change_threshold: 0.2

  # 权重优化器配置
  weight_optimizer:
    learning_rate: 0.01
    momentum: 0.9
    max_weight_change: 0.1
    min_weight: 0.05
    max_weight: 0.8
    adjustment_rate: 0.2

  # 阈值优化器配置
  threshold_optimizer:
    min_threshold: 0.3
    max_threshold: 0.9
    threshold_step: 0.05

  # 调优控制配置
  tuning_frequency: 50        # 每50个决策调优一次
  min_data_for_tuning: 30     # 最少30个数据点才开始调优
  min_tuning_interval: 300    # 最小调优间隔5分钟
  max_tuning_history: 100     # 最大调优历史记录

# 测试配置
testing:
  # A/B测试
  ab_testing:
    enabled: false
    test_ratio: 0.1          # 10%流量用于测试

  # 回测配置
  backtesting:
    enabled: true
    data_split: 0.8          # 80%训练，20%测试
    cross_validation: 5      # 5折交叉验证

  # 基准测试
  benchmark:
    baseline_accuracy: 0.5359  # V6最佳结果作为基准
    target_accuracy: 0.56      # 目标准确率
    max_error_streak: 8        # 目标最大连错
