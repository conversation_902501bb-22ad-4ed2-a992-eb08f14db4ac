#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习模型层

集成LSTM、GRU、Transformer等深度学习模型
处理时序依赖和复杂模式识别
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import time

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("⚠️ PyTorch未安装，将使用简化的深度学习模型")

try:
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import accuracy_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


@dataclass
class DeepLearningPrediction:
    """深度学习预测结果"""
    prediction: float
    confidence: float
    reasoning: str
    attention_weights: Optional[List[float]] = None
    hidden_states: Optional[np.ndarray] = None


class LSTMModel(nn.Module):
    """LSTM模型"""
    
    def __init__(self, input_size: int, hidden_size: int = 64, num_layers: int = 2, dropout: float = 0.2):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=4,
            dropout=dropout,
            batch_first=True
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # LSTM层
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # 注意力机制
        attn_out, attn_weights = self.attention(lstm_out, lstm_out, lstm_out)
        
        # 使用最后一个时间步的输出
        final_output = attn_out[:, -1, :]
        
        # 分类器
        prediction = self.classifier(final_output)
        
        return prediction, attn_weights


class GRUModel(nn.Module):
    """GRU模型"""
    
    def __init__(self, input_size: int, hidden_size: int = 64, num_layers: int = 2, dropout: float = 0.2):
        super(GRUModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.gru = nn.GRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        gru_out, hidden = self.gru(x)
        
        # 使用最后一个时间步的输出
        final_output = gru_out[:, -1, :]
        
        # 分类器
        prediction = self.classifier(final_output)
        
        return prediction


class TransformerModel(nn.Module):
    """简化的Transformer模型"""
    
    def __init__(self, input_size: int, d_model: int = 64, nhead: int = 4, num_layers: int = 2, dropout: float = 0.2):
        super(TransformerModel, self).__init__()
        self.d_model = d_model
        
        # 输入投影
        self.input_projection = nn.Linear(input_size, d_model)
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(100, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        seq_len = x.size(1)
        
        # 输入投影
        x = self.input_projection(x)
        
        # 添加位置编码
        x = x + self.pos_encoding[:seq_len, :].unsqueeze(0)
        
        # Transformer编码
        transformer_out = self.transformer(x)
        
        # 使用最后一个时间步的输出
        final_output = transformer_out[:, -1, :]
        
        # 分类器
        prediction = self.classifier(final_output)
        
        return prediction


class DeepLearningModelLayer:
    """深度学习模型层"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化深度学习模型层
        
        Args:
            config: 配置字典
        """
        self.config = config.get('deep_learning', {})
        self.logger = logging.getLogger(__name__)
        
        # 模型配置
        self.sequence_length = self.config.get('sequence_length', 10)
        self.feature_size = self.config.get('feature_size', 20)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu') if TORCH_AVAILABLE else None
        
        # 模型字典
        self.models = {}
        self.scalers = {}
        self.training_history = {}
        
        # 初始化模型
        self._initialize_models()
        
        self.logger.info(f"深度学习模型层初始化完成，设备: {self.device}")
    
    def _initialize_models(self):
        """初始化深度学习模型"""
        if not TORCH_AVAILABLE:
            self.logger.warning("PyTorch不可用，使用简化模型")
            self._initialize_simple_models()
            return
        
        try:
            # LSTM模型
            if self.config.get('lstm', {}).get('enabled', True):
                self.models['lstm'] = LSTMModel(
                    input_size=self.feature_size,
                    hidden_size=self.config.get('lstm', {}).get('hidden_size', 64),
                    num_layers=self.config.get('lstm', {}).get('num_layers', 2),
                    dropout=self.config.get('lstm', {}).get('dropout', 0.2)
                ).to(self.device)
                self.scalers['lstm'] = StandardScaler() if SKLEARN_AVAILABLE else None
            
            # GRU模型
            if self.config.get('gru', {}).get('enabled', True):
                self.models['gru'] = GRUModel(
                    input_size=self.feature_size,
                    hidden_size=self.config.get('gru', {}).get('hidden_size', 64),
                    num_layers=self.config.get('gru', {}).get('num_layers', 2),
                    dropout=self.config.get('gru', {}).get('dropout', 0.2)
                ).to(self.device)
                self.scalers['gru'] = StandardScaler() if SKLEARN_AVAILABLE else None
            
            # Transformer模型
            if self.config.get('transformer', {}).get('enabled', True):
                self.models['transformer'] = TransformerModel(
                    input_size=self.feature_size,
                    d_model=self.config.get('transformer', {}).get('d_model', 64),
                    nhead=self.config.get('transformer', {}).get('nhead', 4),
                    num_layers=self.config.get('transformer', {}).get('num_layers', 2),
                    dropout=self.config.get('transformer', {}).get('dropout', 0.2)
                ).to(self.device)
                self.scalers['transformer'] = StandardScaler() if SKLEARN_AVAILABLE else None
            
            self.logger.info(f"初始化了 {len(self.models)} 个深度学习模型")
            
        except Exception as e:
            self.logger.error(f"深度学习模型初始化失败: {str(e)}")
            self._initialize_simple_models()
    
    def _initialize_simple_models(self):
        """初始化简化模型（当PyTorch不可用时）"""
        # 简化的线性模型作为替代
        self.models['simple_rnn'] = {
            'type': 'simple',
            'weights': np.random.randn(self.feature_size, 1) * 0.1,
            'bias': 0.0
        }
        
        self.logger.info("初始化了简化的深度学习模型")
    
    def prepare_sequence_data(self, features_history) -> np.ndarray:
        """
        准备序列数据

        Args:
            features_history: 特征历史列表或deque

        Returns:
            序列数据数组
        """
        # 转换为列表（如果是deque）
        if hasattr(features_history, 'maxlen'):  # deque对象
            history_list = list(features_history)
        else:
            history_list = features_history

        if len(history_list) < self.sequence_length:
            # 如果历史数据不足，用零填充
            padded_history = [{}] * (self.sequence_length - len(history_list)) + history_list
        else:
            # 取最近的序列长度的数据
            padded_history = history_list[-self.sequence_length:]
        
        # 转换为特征向量序列
        sequence_data = []
        for features in padded_history:
            feature_vector = self._features_to_vector(features)
            sequence_data.append(feature_vector)
        
        return np.array(sequence_data)
    
    def _features_to_vector(self, features: Dict[str, float]) -> List[float]:
        """将特征字典转换为向量"""
        # 定义特征顺序
        feature_names = [
            'consensus_ratio', 'weighted_consensus', 'avg_confidence',
            'min_confidence', 'max_confidence', 'confidence_std',
            'total_divergence', 'max_divergence',
            'strategy_1_prediction', 'strategy_2_prediction', 'strategy_6_prediction',
            'strategy_1_confidence', 'strategy_2_confidence', 'strategy_6_confidence',
            'win_rate_10', 'win_rate_5', 'current_streak',
            'strategy_1_weight', 'strategy_2_weight', 'strategy_6_weight'
        ]
        
        feature_vector = []
        for name in feature_names:
            feature_vector.append(features.get(name, 0.0))
        
        return feature_vector
    
    def predict(self, model_name: str, features_history: List[Dict[str, float]]) -> DeepLearningPrediction:
        """
        使用指定模型进行预测
        
        Args:
            model_name: 模型名称
            features_history: 特征历史
            
        Returns:
            深度学习预测结果
        """
        if model_name not in self.models:
            return DeepLearningPrediction(
                prediction=0.5,
                confidence=0.1,
                reasoning=f"模型 {model_name} 不存在"
            )
        
        try:
            # 准备序列数据
            sequence_data = self.prepare_sequence_data(features_history)
            
            if not TORCH_AVAILABLE or (isinstance(self.models[model_name], dict) and self.models[model_name].get('type') == 'simple'):
                return self._predict_simple(model_name, sequence_data)
            else:
                return self._predict_deep(model_name, sequence_data)
                
        except Exception as e:
            self.logger.error(f"模型 {model_name} 预测失败: {str(e)}")
            return DeepLearningPrediction(
                prediction=0.5,
                confidence=0.1,
                reasoning=f"预测失败: {str(e)}"
            )
    
    def _predict_simple(self, model_name: str, sequence_data: np.ndarray) -> DeepLearningPrediction:
        """简化模型预测"""
        model = self.models[model_name]
        
        # 使用最后一个时间步的特征
        last_features = sequence_data[-1]
        
        # 简单的线性预测
        prediction = np.dot(last_features, model['weights']).item() + model['bias']
        prediction = 1 / (1 + np.exp(-prediction))  # sigmoid
        
        confidence = min(0.8, abs(prediction - 0.5) * 2 + 0.3)
        
        return DeepLearningPrediction(
            prediction=prediction,
            confidence=confidence,
            reasoning=f"简化{model_name}模型预测"
        )
    
    def _predict_deep(self, model_name: str, sequence_data: np.ndarray) -> DeepLearningPrediction:
        """深度学习模型预测"""
        model = self.models[model_name]
        scaler = self.scalers.get(model_name)
        
        # 数据预处理
        if scaler and hasattr(scaler, 'transform'):
            try:
                # 检查scaler是否已经拟合
                if not hasattr(scaler, 'scale_') or scaler.scale_ is None:
                    # 如果scaler未拟合，使用当前数据进行拟合
                    original_shape = sequence_data.shape
                    sequence_data_flat = sequence_data.reshape(-1, sequence_data.shape[-1])
                    scaler.fit(sequence_data_flat)
                    sequence_data_scaled = scaler.transform(sequence_data_flat)
                    sequence_data = sequence_data_scaled.reshape(original_shape)
                    self.logger.warning(f"模型 {model_name} 的scaler未拟合，使用当前数据进行拟合")
                else:
                    # scaler已拟合，直接使用
                    original_shape = sequence_data.shape
                    sequence_data_flat = sequence_data.reshape(-1, sequence_data.shape[-1])
                    sequence_data_scaled = scaler.transform(sequence_data_flat)
                    sequence_data = sequence_data_scaled.reshape(original_shape)
            except Exception as e:
                self.logger.warning(f"模型 {model_name} 数据标准化失败: {str(e)}，使用原始数据")
                # 如果标准化失败，使用原始数据
        
        # 转换为PyTorch张量
        input_tensor = torch.FloatTensor(sequence_data).unsqueeze(0).to(self.device)
        
        # 模型预测
        model.eval()
        with torch.no_grad():
            if model_name == 'lstm':
                prediction, attention_weights = model(input_tensor)
                attention_weights = attention_weights.cpu().numpy().flatten().tolist()
            else:
                prediction = model(input_tensor)
                attention_weights = None
            
            prediction_value = prediction.cpu().numpy().item()
        
        # 计算置信度
        confidence = min(0.9, abs(prediction_value - 0.5) * 2 + 0.4)
        
        return DeepLearningPrediction(
            prediction=prediction_value,
            confidence=confidence,
            reasoning=f"{model_name}深度学习模型预测",
            attention_weights=attention_weights
        )
    
    def get_ensemble_prediction(self, features_history: List[Dict[str, float]]) -> DeepLearningPrediction:
        """
        获取集成预测结果
        
        Args:
            features_history: 特征历史
            
        Returns:
            集成预测结果
        """
        predictions = []
        confidences = []
        reasoning_parts = []
        
        for model_name in self.models.keys():
            pred = self.predict(model_name, features_history)
            predictions.append(pred.prediction)
            confidences.append(pred.confidence)
            reasoning_parts.append(f"{model_name}:{pred.prediction:.3f}")
        
        if not predictions:
            return DeepLearningPrediction(
                prediction=0.5,
                confidence=0.1,
                reasoning="无可用模型"
            )
        
        # 加权平均（按置信度加权）
        weights = np.array(confidences)
        weights = weights / weights.sum() if weights.sum() > 0 else np.ones_like(weights) / len(weights)
        
        ensemble_prediction = np.average(predictions, weights=weights)
        ensemble_confidence = np.mean(confidences)
        
        reasoning = f"深度学习集成预测: {', '.join(reasoning_parts)}"
        
        return DeepLearningPrediction(
            prediction=ensemble_prediction,
            confidence=ensemble_confidence,
            reasoning=reasoning
        )
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            'total_models': len(self.models),
            'pytorch_available': TORCH_AVAILABLE,
            'device': str(self.device) if self.device else 'CPU',
            'sequence_length': self.sequence_length,
            'feature_size': self.feature_size,
            'models': {}
        }
        
        for model_name, model in self.models.items():
            if TORCH_AVAILABLE and hasattr(model, 'parameters'):
                param_count = sum(p.numel() for p in model.parameters())
                info['models'][model_name] = {
                    'type': 'pytorch',
                    'parameters': param_count
                }
            else:
                info['models'][model_name] = {
                    'type': 'simple',
                    'parameters': 'N/A'
                }
        
        return info

    def fit_scalers(self, training_data: List[Dict[str, float]]):
        """
        使用训练数据拟合标准化器

        Args:
            training_data: 训练数据列表
        """
        if not training_data or not SKLEARN_AVAILABLE:
            return

        try:
            # 准备训练数据
            feature_vectors = []
            for features in training_data:
                feature_vector = self._features_to_vector(features)
                feature_vectors.append(feature_vector)

            if len(feature_vectors) < 2:
                self.logger.warning("训练数据不足，无法拟合标准化器")
                return

            training_array = np.array(feature_vectors)

            # 拟合每个模型的标准化器
            for model_name, scaler in self.scalers.items():
                if scaler and hasattr(scaler, 'fit'):
                    scaler.fit(training_array)
                    self.logger.info(f"模型 {model_name} 的标准化器拟合完成")

        except Exception as e:
            self.logger.error(f"标准化器拟合失败: {str(e)}")

    def warm_up_models(self, sample_features: Dict[str, float]):
        """
        使用样本数据预热模型（确保scaler被拟合）

        Args:
            sample_features: 样本特征
        """
        # 创建一些样本数据来拟合scaler
        sample_data = []
        for i in range(10):
            # 创建轻微变化的样本数据
            varied_features = {}
            for key, value in sample_features.items():
                # 添加小的随机变化
                noise = np.random.normal(0, 0.01)
                varied_features[key] = value + noise
            sample_data.append(varied_features)

        # 拟合标准化器
        self.fit_scalers(sample_data)

        self.logger.info("深度学习模型预热完成")
