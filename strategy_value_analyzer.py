#!/usr/bin/env python3
"""
策略价值深度分析器
重新审视其他7个策略的潜在价值和利用方式
"""

import sys
import os
import logging
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class StrategyValueAnalyzer:
    """策略价值深度分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        try:
            import pymysql
            
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1, strategy_2, strategy_3, strategy_4,
                strategy_5, strategy_6, strategy_7, strategy_8,
                true_label as actual_result
            FROM strategy_results 
            WHERE true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载数据进行深度分析...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据清理
            strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
            for col in strategy_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0]
                df['actual_result'] = pd.to_numeric(df['actual_result'], errors='coerce')
            
            df = df.dropna()
            
            for col in strategy_cols + ['actual_result']:
                df[col] = df[col].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def analyze_individual_strategies(self, df: pd.DataFrame):
        """分析各个策略的个体价值"""
        self.logger.info("\n🔍 分析各个策略的个体价值...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        results = {}
        for strategy in strategy_cols:
            # 基础统计
            accuracy = (df[strategy] == df['actual_result']).mean()
            correlation = df[strategy].corr(df['actual_result'])
            
            # 分布分析
            value_counts = df[strategy].value_counts().sort_index()
            
            # 条件准确率
            conditional_accuracy = {}
            for value in [0, 1]:
                mask = df[strategy] == value
                if mask.sum() > 0:
                    conditional_accuracy[value] = df[mask]['actual_result'].mean()
            
            results[strategy] = {
                'accuracy': accuracy,
                'correlation': correlation,
                'distribution': value_counts.to_dict(),
                'conditional_accuracy': conditional_accuracy,
                'sample_count': len(df[strategy])
            }
            
            self.logger.info(f"   {strategy}:")
            self.logger.info(f"     准确率: {accuracy:.3f}")
            self.logger.info(f"     相关性: {correlation:.6f}")
            self.logger.info(f"     分布: {value_counts.to_dict()}")
            self.logger.info(f"     条件准确率: {conditional_accuracy}")
        
        return results
    
    def analyze_strategy_combinations(self, df: pd.DataFrame):
        """分析策略组合的价值"""
        self.logger.info("\n🔗 分析策略组合的价值...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. 两两组合分析
        self.logger.info("   📊 两两组合分析:")
        
        best_pairs = []
        for i, s1 in enumerate(strategy_cols):
            for s2 in strategy_cols[i+1:]:
                # 简单AND组合
                and_result = (df[s1] & df[s2]).astype(int)
                and_accuracy = (and_result == df['actual_result']).mean()
                
                # 简单OR组合
                or_result = (df[s1] | df[s2]).astype(int)
                or_accuracy = (or_result == df['actual_result']).mean()
                
                # XOR组合
                xor_result = (df[s1] ^ df[s2]).astype(int)
                xor_accuracy = (xor_result == df['actual_result']).mean()
                
                best_pairs.append({
                    'pair': f"{s1}+{s2}",
                    'and_accuracy': and_accuracy,
                    'or_accuracy': or_accuracy,
                    'xor_accuracy': xor_accuracy,
                    'best_op': max([('AND', and_accuracy), ('OR', or_accuracy), ('XOR', xor_accuracy)], key=lambda x: x[1])
                })
        
        # 排序找最佳组合
        best_pairs.sort(key=lambda x: x['best_op'][1], reverse=True)
        
        self.logger.info("   🏆 最佳两两组合 (Top 5):")
        for i, pair in enumerate(best_pairs[:5]):
            op, acc = pair['best_op']
            self.logger.info(f"     {i+1}. {pair['pair']} ({op}): {acc:.3f}")
        
        # 2. 三策略组合分析
        self.logger.info("\n   📊 三策略组合分析:")
        
        # 测试一些有意义的三策略组合
        test_combinations = [
            (['strategy_1', 'strategy_8', 'strategy_3'], "高相关性组合"),
            (['strategy_2', 'strategy_4', 'strategy_6'], "低相关性组合"),
            (['strategy_1', 'strategy_2', 'strategy_3'], "前三策略"),
            (['strategy_6', 'strategy_7', 'strategy_8'], "后三策略"),
        ]
        
        for strategies, desc in test_combinations:
            # 多数投票
            majority = (df[strategies].sum(axis=1) >= 2).astype(int)
            majority_acc = (majority == df['actual_result']).mean()
            
            # 一致性投票
            unanimous = (df[strategies].sum(axis=1) == 3).astype(int)
            unanimous_acc = (unanimous == df['actual_result']).mean() if unanimous.sum() > 0 else 0
            
            # 加权投票 (基于相关性)
            weights = [0.147, 0.038, 0.007] if 'strategy_1' in strategies else [1/3, 1/3, 1/3]
            weighted = (df[strategies] * weights).sum(axis=1)
            weighted_binary = (weighted >= sum(weights)/2).astype(int)
            weighted_acc = (weighted_binary == df['actual_result']).mean()
            
            self.logger.info(f"     {desc}:")
            self.logger.info(f"       多数投票: {majority_acc:.3f}")
            self.logger.info(f"       一致性投票: {unanimous_acc:.3f}")
            self.logger.info(f"       加权投票: {weighted_acc:.3f}")
        
        return best_pairs
    
    def analyze_contextual_value(self, df: pd.DataFrame):
        """分析策略的上下文价值"""
        self.logger.info("\n🎯 分析策略的上下文价值...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. Boot内表现分析
        self.logger.info("   📈 Boot内表现分析:")
        
        boot_performance = {}
        for strategy in strategy_cols:
            boot_accuracies = []
            for boot_id in df['boot_id'].unique():
                boot_data = df[df['boot_id'] == boot_id]
                if len(boot_data) > 5:  # 至少5个样本
                    boot_acc = (boot_data[strategy] == boot_data['actual_result']).mean()
                    boot_accuracies.append(boot_acc)
            
            if boot_accuracies:
                boot_performance[strategy] = {
                    'mean_boot_accuracy': np.mean(boot_accuracies),
                    'std_boot_accuracy': np.std(boot_accuracies),
                    'best_boot_accuracy': np.max(boot_accuracies),
                    'worst_boot_accuracy': np.min(boot_accuracies),
                    'consistent_boots': sum(1 for acc in boot_accuracies if acc > 0.6)
                }
                
                self.logger.info(f"     {strategy}:")
                self.logger.info(f"       平均Boot准确率: {boot_performance[strategy]['mean_boot_accuracy']:.3f}")
                self.logger.info(f"       Boot准确率标准差: {boot_performance[strategy]['std_boot_accuracy']:.3f}")
                self.logger.info(f"       最佳Boot准确率: {boot_performance[strategy]['best_boot_accuracy']:.3f}")
                self.logger.info(f"       高性能Boot数: {boot_performance[strategy]['consistent_boots']}")
        
        # 2. 时间序列表现分析
        self.logger.info("\n   ⏰ 时间序列表现分析:")
        
        # 按时间窗口分析
        window_size = 1000
        time_performance = {}
        
        for strategy in strategy_cols:
            window_accuracies = []
            for start in range(0, len(df) - window_size, window_size):
                window_data = df.iloc[start:start + window_size]
                window_acc = (window_data[strategy] == window_data['actual_result']).mean()
                window_accuracies.append(window_acc)
            
            if window_accuracies:
                time_performance[strategy] = {
                    'mean_window_accuracy': np.mean(window_accuracies),
                    'trend': np.polyfit(range(len(window_accuracies)), window_accuracies, 1)[0],
                    'stability': 1 - np.std(window_accuracies)
                }
                
                trend_desc = "上升" if time_performance[strategy]['trend'] > 0 else "下降"
                self.logger.info(f"     {strategy}: 平均={time_performance[strategy]['mean_window_accuracy']:.3f}, "
                               f"趋势={trend_desc}, 稳定性={time_performance[strategy]['stability']:.3f}")
        
        return boot_performance, time_performance
    
    def analyze_complementary_value(self, df: pd.DataFrame):
        """分析策略的互补价值"""
        self.logger.info("\n🤝 分析策略的互补价值...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. 错误互补分析
        self.logger.info("   ❌ 错误互补分析:")
        
        # 找出strategy_1错误的情况
        s1_errors = df[df['strategy_1'] != df['actual_result']]
        self.logger.info(f"     strategy_1错误案例数: {len(s1_errors)}")
        
        if len(s1_errors) > 0:
            # 看其他策略在这些错误案例中的表现
            for strategy in strategy_cols[1:]:  # 跳过strategy_1
                correct_on_s1_errors = (s1_errors[strategy] == s1_errors['actual_result']).mean()
                self.logger.info(f"     {strategy}在strategy_1错误时的准确率: {correct_on_s1_errors:.3f}")
        
        # 2. 条件独立性分析
        self.logger.info("\n   🎲 条件独立性分析:")
        
        for strategy in strategy_cols[1:]:
            # 在strategy_1=0时，其他策略的表现
            s1_0_mask = df['strategy_1'] == 0
            s1_0_data = df[s1_0_mask]
            if len(s1_0_data) > 0:
                acc_when_s1_0 = (s1_0_data[strategy] == s1_0_data['actual_result']).mean()
            else:
                acc_when_s1_0 = 0
            
            # 在strategy_1=1时，其他策略的表现
            s1_1_mask = df['strategy_1'] == 1
            s1_1_data = df[s1_1_mask]
            if len(s1_1_data) > 0:
                acc_when_s1_1 = (s1_1_data[strategy] == s1_1_data['actual_result']).mean()
            else:
                acc_when_s1_1 = 0
            
            self.logger.info(f"     {strategy}:")
            self.logger.info(f"       当strategy_1=0时准确率: {acc_when_s1_0:.3f}")
            self.logger.info(f"       当strategy_1=1时准确率: {acc_when_s1_1:.3f}")
            self.logger.info(f"       条件差异: {abs(acc_when_s1_1 - acc_when_s1_0):.3f}")
    
    def suggest_alternative_approaches(self, df: pd.DataFrame):
        """建议替代方法"""
        self.logger.info("\n💡 建议替代方法...")
        
        strategy_cols = [f'strategy_{i}' for i in range(1, 9)]
        
        # 1. 集成学习的新思路
        self.logger.info("   🎯 集成学习新思路:")
        
        # 动态权重集成
        self.logger.info("     1. 动态权重集成:")
        self.logger.info("        - 根据最近表现动态调整策略权重")
        self.logger.info("        - 在不同Boot阶段使用不同策略组合")
        self.logger.info("        - 基于历史成功率的自适应权重")
        
        # 分层决策
        self.logger.info("     2. 分层决策系统:")
        self.logger.info("        - 第一层：strategy_1作为主要决策")
        self.logger.info("        - 第二层：其他策略作为置信度调节")
        self.logger.info("        - 第三层：基于上下文的最终调整")
        
        # 2. 特征工程的新方向
        self.logger.info("\n   🔧 特征工程新方向:")
        
        # 策略一致性特征
        consistency_features = []
        for i in range(len(strategy_cols)):
            for j in range(i+1, len(strategy_cols)):
                s1, s2 = strategy_cols[i], strategy_cols[j]
                consistency = (df[s1] == df[s2]).astype(int)
                consistency_acc = (consistency == df['actual_result']).mean()
                consistency_features.append((f"{s1}_{s2}_consistency", consistency_acc))
        
        # 排序找最佳一致性特征
        consistency_features.sort(key=lambda x: abs(x[1] - 0.5), reverse=True)
        
        self.logger.info("     最有价值的一致性特征:")
        for feature, acc in consistency_features[:5]:
            self.logger.info(f"       {feature}: {acc:.3f}")
        
        # 3. 元学习方法
        self.logger.info("\n   🧠 元学习方法:")
        self.logger.info("     1. 学习何时信任哪个策略")
        self.logger.info("     2. 学习策略失效的模式")
        self.logger.info("     3. 学习最佳策略组合的上下文")
        
        # 4. 实际可行的改进建议
        self.logger.info("\n   🚀 实际可行的改进建议:")
        
        # 计算一些实际的改进指标
        
        # 策略选择器
        best_strategy_per_boot = {}
        for boot_id in df['boot_id'].unique():
            boot_data = df[df['boot_id'] == boot_id]
            if len(boot_data) > 5:
                boot_accuracies = {}
                for strategy in strategy_cols:
                    boot_accuracies[strategy] = (boot_data[strategy] == boot_data['actual_result']).mean()
                best_strategy_per_boot[boot_id] = max(boot_accuracies, key=boot_accuracies.get)
        
        # 如果我们能完美选择每个Boot的最佳策略
        perfect_selection_accuracy = 0
        total_samples = 0
        for boot_id, best_strategy in best_strategy_per_boot.items():
            boot_data = df[df['boot_id'] == boot_id]
            if len(boot_data) > 5:
                boot_correct = (boot_data[best_strategy] == boot_data['actual_result']).sum()
                perfect_selection_accuracy += boot_correct
                total_samples += len(boot_data)
        
        if total_samples > 0:
            perfect_selection_accuracy /= total_samples
            self.logger.info(f"     完美策略选择的理论上限: {perfect_selection_accuracy:.3f}")
            self.logger.info(f"     相比当前的提升空间: {perfect_selection_accuracy - 0.568:.3f}")
    
    def run_comprehensive_analysis(self):
        """运行综合分析"""
        self.logger.info("🚀 开始策略价值综合分析")
        self.logger.info("="*80)
        
        # 加载数据
        df = self.load_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，分析终止")
            return
        
        # 运行各项分析
        individual_results = self.analyze_individual_strategies(df)
        combination_results = self.analyze_strategy_combinations(df)
        boot_perf, time_perf = self.analyze_contextual_value(df)
        self.analyze_complementary_value(df)
        self.suggest_alternative_approaches(df)
        
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 策略价值分析总结")
        self.logger.info("="*80)
        
        # 总结关键发现
        self.logger.info("\n📊 关键发现:")
        
        # 找出除了strategy_1之外最有价值的策略
        other_strategies = {k: v for k, v in individual_results.items() if k != 'strategy_1'}
        best_other = max(other_strategies.items(), key=lambda x: abs(x[1]['correlation']))
        
        self.logger.info(f"   1. 除strategy_1外最有价值的策略: {best_other[0]} (相关性: {best_other[1]['correlation']:.6f})")
        
        # 最佳组合
        if combination_results:
            best_combo = combination_results[0]
            self.logger.info(f"   2. 最佳策略组合: {best_combo['pair']} ({best_combo['best_op'][0]}) = {best_combo['best_op'][1]:.3f}")
        
        self.logger.info("\n💡 建议:")
        self.logger.info("   1. 不要完全放弃其他策略，它们可能在特定上下文中有价值")
        self.logger.info("   2. 考虑动态策略选择而非静态权重")
        self.logger.info("   3. 探索策略失效模式，学习何时不信任某个策略")
        self.logger.info("   4. 使用元学习方法学习最佳策略组合的上下文")
        
        self.logger.info("="*80)

if __name__ == "__main__":
    analyzer = StrategyValueAnalyzer()
    analyzer.run_comprehensive_analysis()
