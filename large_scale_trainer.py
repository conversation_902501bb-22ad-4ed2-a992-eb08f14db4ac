#!/usr/bin/env python3
"""
V8系统大规模历史数据训练器
使用全部23,175条真实数据进行训练，目标准确率≥60%
"""

import sys
import os
import logging
import time
import warnings
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 过滤警告
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', message='invalid value encountered')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class LargeScaleTrainer:
    """大规模训练器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.training_stats = {
            'total_samples': 0,
            'training_samples': 0,
            'validation_samples': 0,
            'final_accuracy': 0,
            'training_time': 0,
            'batch_results': []
        }
        
    def load_all_data(self) -> pd.DataFrame:
        """加载所有可用的历史数据"""
        try:
            import pymysql
            
            self.logger.info("🔗 连接数据库...")
            connection = pymysql.connect(
                host='**************',
                user='root',
                password='216888',
                database='lushu',
                charset='utf8mb4',
                port=3306,
                connect_timeout=10
            )
            
            # 获取所有有效数据
            sql = """
            SELECT 
                id,
                boot_id,
                strategy_1,
                strategy_2,
                strategy_6,
                true_label as actual_result
            FROM strategy_results 
            WHERE strategy_1 IS NOT NULL 
                AND strategy_2 IS NOT NULL 
                AND strategy_6 IS NOT NULL
                AND true_label IS NOT NULL
            ORDER BY boot_id, id
            """
            
            self.logger.info("📊 加载全部历史数据...")
            df = pd.read_sql(sql, connection)
            connection.close()
            
            # 数据类型转换
            if df['actual_result'].dtype == 'object':
                df['actual_result'] = df['actual_result'].astype(str).str[0].astype(int)
            
            self.logger.info(f"✅ 成功加载 {len(df)} 条历史数据")
            self.logger.info(f"   - Boot范围: {df['boot_id'].min()} - {df['boot_id'].max()}")
            self.logger.info(f"   - 正例比例: {df['actual_result'].mean():.3f}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ 加载数据失败: {e}")
            return pd.DataFrame()
    
    def optimize_training_environment(self, system):
        """优化训练环境"""
        self.logger.info("🔧 优化训练环境...")
        
        # 1. 放宽风险管理限制
        if hasattr(system, 'risk_manager'):
            original_config = system.risk_manager.config.copy()
            
            # 大幅放宽风险阈值
            system.risk_manager.config['risk_thresholds']['high_risk'] = 0.95
            system.risk_manager.config['risk_thresholds']['medium_risk'] = 0.90
            system.risk_manager.config['risk_thresholds']['low_risk'] = 0.85
            
            # 降低风险权重
            if 'risk_weights' in system.risk_manager.config:
                for key in system.risk_manager.config['risk_weights']:
                    system.risk_manager.config['risk_weights'][key] *= 0.3
            
            self.logger.info("   📉 已大幅放宽风险管理限制")
        
        # 2. 优化ML模型配置
        if hasattr(system, 'ml_fusion') and hasattr(system.ml_fusion, 'models'):
            # 启用更积极的学习
            for model_name, model in system.ml_fusion.models.items():
                if hasattr(model, 'learning_rate'):
                    model.learning_rate = 0.01  # 提高学习率
                if hasattr(model, 'update_frequency'):
                    model.update_frequency = 10  # 更频繁更新
            
            self.logger.info("   🤖 已优化ML模型配置")
        
        # 3. 启用详细日志（但限制频率）
        system.config = getattr(system, 'config', {})
        system.config['training_mode'] = True
        system.config['verbose_training'] = False  # 减少日志输出
        
        self.logger.info("   ⚙️ 训练环境优化完成")
    
    def train_with_sklearn_models(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """使用sklearn模型进行训练"""
        try:
            from sklearn.model_selection import train_test_split, cross_val_score
            from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
            from sklearn.linear_model import LogisticRegression
            from sklearn.svm import SVC
            from sklearn.metrics import accuracy_score, classification_report
            
            self.logger.info("🤖 开始sklearn模型训练...")
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 创建多个模型
            models = {
                'logistic': LogisticRegression(random_state=42, max_iter=1000),
                'random_forest': RandomForestClassifier(n_estimators=200, random_state=42, max_depth=10),
                'gradient_boost': GradientBoostingClassifier(n_estimators=100, random_state=42),
                'svm': SVC(probability=True, random_state=42)
            }
            
            # 训练和评估
            results = {}
            for name, model in models.items():
                self.logger.info(f"   🔄 训练 {name}...")
                
                start_time = time.time()
                model.fit(X_train, y_train)
                train_time = time.time() - start_time
                
                # 预测和评估
                y_pred = model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
                
                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
                
                results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'train_time': train_time
                }
                
                self.logger.info(f"      ✅ {name}: 准确率={accuracy:.3f}, CV={cv_scores.mean():.3f}±{cv_scores.std():.3f}")
            
            # 创建集成模型
            self.logger.info("   🔗 创建集成模型...")
            ensemble = VotingClassifier(
                estimators=[(name, result['model']) for name, result in results.items()],
                voting='soft'
            )
            
            ensemble.fit(X_train, y_train)
            ensemble_pred = ensemble.predict(X_test)
            ensemble_accuracy = accuracy_score(y_test, ensemble_pred)
            
            self.logger.info(f"   🏆 集成模型准确率: {ensemble_accuracy:.3f}")
            
            return {
                'individual_models': results,
                'ensemble': ensemble,
                'ensemble_accuracy': ensemble_accuracy,
                'best_individual': max(results.items(), key=lambda x: x[1]['accuracy'])
            }
            
        except Exception as e:
            self.logger.error(f"❌ sklearn模型训练失败: {e}")
            return {}
    
    def train_v8_system(self, df: pd.DataFrame, batch_size: int = 2000) -> Dict[str, Any]:
        """训练V8系统"""
        try:
            from main import SimpleFusionV8
            
            self.logger.info("🚀 初始化V8系统...")
            system = SimpleFusionV8()
            system.initialize()
            
            # 优化训练环境
            self.optimize_training_environment(system)
            
            # 分批训练
            total_samples = len(df)
            num_batches = (total_samples + batch_size - 1) // batch_size
            
            self.logger.info(f"📦 开始分批训练: {num_batches} 批次，每批 {batch_size} 样本")
            
            batch_accuracies = []
            total_correct = 0
            total_processed = 0
            
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, total_samples)
                batch_df = df.iloc[start_idx:end_idx]
                
                self.logger.info(f"   🔄 批次 {batch_idx + 1}/{num_batches}: {len(batch_df)} 样本")
                
                batch_correct = 0
                batch_total = 0
                
                for idx, row in batch_df.iterrows():
                    try:
                        # 创建外部数据
                        external_data = {
                            'decision_id': f"large_train_{idx}",
                            'boot_id': int(row['boot_id']),
                            'market_data': {
                                'historical_strategies': {
                                    'strategy_1': int(row['strategy_1']),
                                    'strategy_2': int(row['strategy_2']),
                                    'strategy_6': int(row['strategy_6'])
                                }
                            }
                        }
                        
                        # 处理决策
                        decision = system.process_decision(external_data)
                        
                        if decision:
                            # 检查预测准确性
                            predicted = 1 if decision.prediction > 0.5 else 0
                            actual = int(row['actual_result'])
                            
                            if predicted == actual:
                                batch_correct += 1
                                total_correct += 1
                            
                            batch_total += 1
                            total_processed += 1
                            
                            # 提供反馈
                            system.update_feedback(external_data['decision_id'], actual)
                    
                    except Exception as e:
                        continue
                
                # 批次统计
                batch_accuracy = batch_correct / batch_total if batch_total > 0 else 0
                batch_accuracies.append(batch_accuracy)
                
                if (batch_idx + 1) % 5 == 0:  # 每5个批次报告一次
                    current_accuracy = total_correct / total_processed if total_processed > 0 else 0
                    self.logger.info(f"   📊 当前总体准确率: {current_accuracy:.3f} ({total_correct}/{total_processed})")
            
            # 最终统计
            final_accuracy = total_correct / total_processed if total_processed > 0 else 0
            
            self.training_stats.update({
                'total_samples': total_samples,
                'training_samples': total_processed,
                'final_accuracy': final_accuracy,
                'batch_results': batch_accuracies
            })
            
            self.logger.info(f"🎯 V8系统训练完成:")
            self.logger.info(f"   - 处理样本: {total_processed}/{total_samples}")
            self.logger.info(f"   - 最终准确率: {final_accuracy:.3f}")
            self.logger.info(f"   - 平均批次准确率: {np.mean(batch_accuracies):.3f}±{np.std(batch_accuracies):.3f}")
            
            return {
                'system': system,
                'final_accuracy': final_accuracy,
                'total_processed': total_processed,
                'batch_accuracies': batch_accuracies
            }
            
        except Exception as e:
            self.logger.error(f"❌ V8系统训练失败: {e}")
            return {}
    
    def run_large_scale_training(self):
        """运行大规模训练"""
        self.logger.info("🚀 开始V8系统大规模训练")
        self.logger.info("="*80)
        
        start_time = time.time()
        
        # 1. 加载全部数据
        df = self.load_all_data()
        if df.empty:
            self.logger.error("❌ 无法加载数据，训练终止")
            return
        
        # 2. 准备sklearn基准测试
        self.logger.info("\n📊 第一阶段：sklearn基准模型训练")
        X = df[['strategy_1', 'strategy_2', 'strategy_6']].values
        y = df['actual_result'].values
        
        sklearn_results = self.train_with_sklearn_models(X, y)
        
        # 3. V8系统训练
        self.logger.info("\n🎯 第二阶段：V8系统完整训练")
        v8_results = self.train_v8_system(df)
        
        # 4. 生成最终报告
        total_time = time.time() - start_time
        self.training_stats['training_time'] = total_time
        
        self.generate_final_report(sklearn_results, v8_results)
    
    def generate_final_report(self, sklearn_results: Dict, v8_results: Dict):
        """生成最终训练报告"""
        self.logger.info("\n" + "="*80)
        self.logger.info("🎯 大规模训练最终报告")
        self.logger.info("="*80)
        
        # sklearn结果
        if sklearn_results:
            self.logger.info("\n📊 sklearn基准模型结果:")
            for name, result in sklearn_results['individual_models'].items():
                self.logger.info(f"   - {name}: {result['accuracy']:.3f} (CV: {result['cv_mean']:.3f}±{result['cv_std']:.3f})")
            
            if 'ensemble_accuracy' in sklearn_results:
                self.logger.info(f"   - 集成模型: {sklearn_results['ensemble_accuracy']:.3f}")
        
        # V8系统结果
        if v8_results:
            self.logger.info(f"\n🎯 V8系统结果:")
            self.logger.info(f"   - 最终准确率: {v8_results['final_accuracy']:.3f}")
            self.logger.info(f"   - 处理样本数: {v8_results['total_processed']}")
            
            if v8_results['batch_accuracies']:
                batch_mean = np.mean(v8_results['batch_accuracies'])
                batch_std = np.std(v8_results['batch_accuracies'])
                self.logger.info(f"   - 批次准确率: {batch_mean:.3f}±{batch_std:.3f}")
        
        # 总体评估
        self.logger.info(f"\n⏱️ 训练时间: {self.training_stats['training_time']:.1f}秒")
        
        target_accuracy = 0.60
        if v8_results and v8_results['final_accuracy'] >= target_accuracy:
            self.logger.info(f"\n🎉 训练成功！V8系统达到目标准确率 {target_accuracy:.1%}")
        else:
            current_acc = v8_results['final_accuracy'] if v8_results else 0
            self.logger.warning(f"\n⚠️ 训练未达标：当前准确率 {current_acc:.3f}，目标 {target_accuracy:.3f}")
        
        self.logger.info("="*80)

if __name__ == "__main__":
    trainer = LargeScaleTrainer()
    trainer.run_large_scale_training()
